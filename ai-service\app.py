import os
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
import tensorflow as tf
import numpy as np
from PIL import Image
import io
import requests
from datetime import datetime
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Configuration
class Config:
    MODEL_PATH = os.getenv('MODEL_PATH', './models/issue_classifier.h5')
    CONFIDENCE_THRESHOLD = float(os.getenv('CONFIDENCE_THRESHOLD', 0.7))
    MAX_IMAGE_SIZE = int(os.getenv('MAX_IMAGE_SIZE', 5242880))  # 5MB
    ALLOWED_EXTENSIONS = os.getenv('ALLOWED_EXTENSIONS', 'jpg,jpeg,png,gif').split(',')
    BACKEND_API_URL = os.getenv('BACKEND_API_URL', 'http://localhost:5000')
    IMAGE_SIZE = (224, 224)  # Standard input size for most CNN models

# Issue categories mapping
ISSUE_CATEGORIES = {
    0: 'pothole',
    1: 'garbage',
    2: 'water_leak',
    3: 'broken_light',
    4: 'damaged_road',
    5: 'other'
}

# Global model variable
model = None

def load_model():
    """Load the trained model or create a simple demo model"""
    global model
    try:
        if os.path.exists(Config.MODEL_PATH):
            logger.info(f"Loading model from {Config.MODEL_PATH}")
            model = tf.keras.models.load_model(Config.MODEL_PATH)
            logger.info("Model loaded successfully")
        else:
            logger.warning("Model file not found. Creating demo model...")
            model = create_demo_model()
            logger.info("Demo model created")
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        logger.info("Creating demo model as fallback...")
        model = create_demo_model()

def create_demo_model():
    """Create a simple demo model for testing purposes"""
    model = tf.keras.Sequential([
        tf.keras.layers.Input(shape=(224, 224, 3)),
        tf.keras.layers.Conv2D(32, 3, activation='relu'),
        tf.keras.layers.MaxPooling2D(),
        tf.keras.layers.Conv2D(64, 3, activation='relu'),
        tf.keras.layers.MaxPooling2D(),
        tf.keras.layers.Conv2D(64, 3, activation='relu'),
        tf.keras.layers.GlobalAveragePooling2D(),
        tf.keras.layers.Dense(64, activation='relu'),
        tf.keras.layers.Dropout(0.5),
        tf.keras.layers.Dense(len(ISSUE_CATEGORIES), activation='softmax')
    ])
    
    model.compile(
        optimizer='adam',
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # Save the demo model
    os.makedirs(os.path.dirname(Config.MODEL_PATH), exist_ok=True)
    model.save(Config.MODEL_PATH)
    logger.info(f"Demo model saved to {Config.MODEL_PATH}")
    
    return model

def preprocess_image(image_data):
    """Preprocess image for model prediction"""
    try:
        # Open image
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Resize image
        image = image.resize(Config.IMAGE_SIZE)
        
        # Convert to numpy array and normalize
        image_array = np.array(image) / 255.0
        
        # Add batch dimension
        image_array = np.expand_dims(image_array, axis=0)
        
        return image_array
    except Exception as e:
        logger.error(f"Error preprocessing image: {str(e)}")
        raise ValueError(f"Invalid image format: {str(e)}")

def predict_issue_category(image_data):
    """Predict issue category from image"""
    try:
        # Preprocess image
        processed_image = preprocess_image(image_data)
        
        # Make prediction
        predictions = model.predict(processed_image)
        
        # Get predicted class and confidence
        predicted_class = np.argmax(predictions[0])
        confidence = float(predictions[0][predicted_class])
        
        # Get category name
        category = ISSUE_CATEGORIES.get(predicted_class, 'other')
        
        return {
            'category': category,
            'confidence': confidence,
            'all_predictions': {
                ISSUE_CATEGORIES[i]: float(predictions[0][i]) 
                for i in range(len(ISSUE_CATEGORIES))
            }
        }
    except Exception as e:
        logger.error(f"Error during prediction: {str(e)}")
        raise

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'model_loaded': model is not None,
        'tensorflow_version': tf.__version__
    })

@app.route('/classify', methods=['POST'])
def classify_image():
    """Classify an image and return the predicted issue category"""
    try:
        # Check if image is provided
        if 'image' not in request.files and 'imageUrl' not in request.json:
            return jsonify({
                'success': False,
                'error': 'No image provided. Send image file or imageUrl.'
            }), 400
        
        # Get image data
        if 'image' in request.files:
            # Direct file upload
            file = request.files['image']
            if file.filename == '':
                return jsonify({
                    'success': False,
                    'error': 'No file selected'
                }), 400
            
            # Check file extension
            file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
            if file_ext not in Config.ALLOWED_EXTENSIONS:
                return jsonify({
                    'success': False,
                    'error': f'Invalid file type. Allowed: {", ".join(Config.ALLOWED_EXTENSIONS)}'
                }), 400
            
            image_data = file.read()
        else:
            # Image URL provided
            data = request.get_json()
            image_url = data.get('imageUrl')
            
            if not image_url:
                return jsonify({
                    'success': False,
                    'error': 'imageUrl is required'
                }), 400
            
            # Download image from URL
            try:
                response = requests.get(image_url, timeout=10)
                response.raise_for_status()
                image_data = response.content
            except requests.RequestException as e:
                return jsonify({
                    'success': False,
                    'error': f'Failed to download image: {str(e)}'
                }), 400
        
        # Check image size
        if len(image_data) > Config.MAX_IMAGE_SIZE:
            return jsonify({
                'success': False,
                'error': f'Image too large. Maximum size: {Config.MAX_IMAGE_SIZE} bytes'
            }), 400
        
        # Make prediction
        result = predict_issue_category(image_data)
        
        # Check confidence threshold
        is_confident = result['confidence'] >= Config.CONFIDENCE_THRESHOLD
        
        response_data = {
            'success': True,
            'category': result['category'],
            'confidence': result['confidence'],
            'is_confident': is_confident,
            'threshold': Config.CONFIDENCE_THRESHOLD,
            'all_predictions': result['all_predictions'],
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # If issueId is provided, update the backend
        if request.is_json and 'issueId' in request.get_json():
            issue_id = request.get_json()['issueId']
            try:
                update_backend_classification(issue_id, result)
                response_data['backend_updated'] = True
            except Exception as e:
                logger.error(f"Failed to update backend: {str(e)}")
                response_data['backend_updated'] = False
                response_data['backend_error'] = str(e)
        
        return jsonify(response_data)
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400
    except Exception as e:
        logger.error(f"Unexpected error in classify_image: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

def update_backend_classification(issue_id, classification_result):
    """Update the backend with AI classification results"""
    try:
        url = f"{Config.BACKEND_API_URL}/api/issues/{issue_id}/ai-classification"
        data = {
            'aiClassification': {
                'category': classification_result['category'],
                'confidence': classification_result['confidence'],
                'processedAt': datetime.utcnow().isoformat()
            }
        }
        
        response = requests.patch(url, json=data, timeout=5)
        response.raise_for_status()
        logger.info(f"Successfully updated backend for issue {issue_id}")
        
    except requests.RequestException as e:
        logger.error(f"Failed to update backend for issue {issue_id}: {str(e)}")
        raise

@app.route('/model/info', methods=['GET'])
def model_info():
    """Get information about the loaded model"""
    if model is None:
        return jsonify({
            'success': False,
            'error': 'No model loaded'
        }), 500
    
    try:
        return jsonify({
            'success': True,
            'model_path': Config.MODEL_PATH,
            'input_shape': model.input_shape,
            'output_shape': model.output_shape,
            'categories': ISSUE_CATEGORIES,
            'confidence_threshold': Config.CONFIDENCE_THRESHOLD,
            'max_image_size': Config.MAX_IMAGE_SIZE,
            'allowed_extensions': Config.ALLOWED_EXTENSIONS
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500

if __name__ == '__main__':
    # Load model on startup
    load_model()
    
    # Run the app
    port = int(os.getenv('PORT', 5001))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Starting AI Classification Service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
