import os
import requests
import time
import json
from urllib.parse import urlparse
from PIL import Image
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataCollector:
    """Collect and organize training data for urban issue classification"""
    
    def __init__(self, dataset_path='./dataset'):
        self.dataset_path = dataset_path
        self.categories = ['pothole', 'garbage', 'water_leak', 'broken_light', 'damaged_road', 'other']
        self.create_directories()
    
    def create_directories(self):
        """Create directory structure for dataset"""
        for split in ['train', 'validation']:
            for category in self.categories:
                path = os.path.join(self.dataset_path, split, category)
                os.makedirs(path, exist_ok=True)
    
    def download_image(self, url, filepath, timeout=10):
        """Download image from URL"""
        try:
            response = requests.get(url, timeout=timeout, stream=True)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # Verify image can be opened
            with Image.open(filepath) as img:
                img.verify()
            
            return True
        except Exception as e:
            logger.error(f"Failed to download {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False
    
    def collect_from_urls(self, urls_file):
        """Collect images from a file containing URLs and categories"""
        """
        Expected format of urls_file (JSON):
        {
            "pothole": [
                "https://example.com/pothole1.jpg",
                "https://example.com/pothole2.jpg"
            ],
            "garbage": [
                "https://example.com/garbage1.jpg"
            ]
        }
        """
        try:
            with open(urls_file, 'r') as f:
                data = json.load(f)
            
            total_downloaded = 0
            
            for category, urls in data.items():
                if category not in self.categories:
                    logger.warning(f"Unknown category: {category}")
                    continue
                
                logger.info(f"Downloading {len(urls)} images for category: {category}")
                
                for i, url in enumerate(urls):
                    # Determine split (80% train, 20% validation)
                    split = 'train' if i % 5 != 0 else 'validation'
                    
                    # Generate filename
                    filename = f"{category}_{i:04d}.jpg"
                    filepath = os.path.join(self.dataset_path, split, category, filename)
                    
                    if self.download_image(url, filepath):
                        total_downloaded += 1
                        logger.info(f"Downloaded: {filename}")
                    
                    # Rate limiting
                    time.sleep(0.5)
            
            logger.info(f"Total images downloaded: {total_downloaded}")
            
        except Exception as e:
            logger.error(f"Error collecting from URLs: {str(e)}")
    
    def create_sample_urls_file(self, filename='sample_urls.json'):
        """Create a sample URLs file for reference"""
        sample_data = {
            "pothole": [
                "# Add pothole image URLs here",
                "# Example: https://example.com/pothole1.jpg"
            ],
            "garbage": [
                "# Add garbage dump image URLs here"
            ],
            "water_leak": [
                "# Add water leak image URLs here"
            ],
            "broken_light": [
                "# Add broken street light image URLs here"
            ],
            "damaged_road": [
                "# Add damaged road image URLs here"
            ],
            "other": [
                "# Add other urban issue image URLs here"
            ]
        }
        
        with open(filename, 'w') as f:
            json.dump(sample_data, f, indent=2)
        
        logger.info(f"Sample URLs file created: {filename}")
        logger.info("Edit this file with actual image URLs and run collect_from_urls()")
    
    def validate_dataset(self):
        """Validate the dataset and provide statistics"""
        stats = {}
        total_images = 0
        
        for split in ['train', 'validation']:
            stats[split] = {}
            split_total = 0
            
            for category in self.categories:
                path = os.path.join(self.dataset_path, split, category)
                if os.path.exists(path):
                    count = len([f for f in os.listdir(path) 
                               if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif'))])
                    stats[split][category] = count
                    split_total += count
                else:
                    stats[split][category] = 0
            
            stats[split]['total'] = split_total
            total_images += split_total
        
        # Print statistics
        logger.info("Dataset Statistics:")
        logger.info("=" * 50)
        
        for split in ['train', 'validation']:
            logger.info(f"\n{split.upper()} SET:")
            for category in self.categories:
                count = stats[split][category]
                logger.info(f"  {category:15}: {count:4d} images")
            logger.info(f"  {'TOTAL':15}: {stats[split]['total']:4d} images")
        
        logger.info(f"\nGRAND TOTAL: {total_images} images")
        
        # Check for minimum requirements
        min_images_per_category = 50
        issues = []
        
        for category in self.categories:
            total_cat = stats['train'][category] + stats['validation'][category]
            if total_cat < min_images_per_category:
                issues.append(f"{category}: {total_cat} images (minimum: {min_images_per_category})")
        
        if issues:
            logger.warning("\nDataset Issues:")
            for issue in issues:
                logger.warning(f"  - {issue}")
        else:
            logger.info("\n✅ Dataset looks good for training!")
        
        return stats
    
    def create_synthetic_dataset(self, images_per_category=100):
        """Create a synthetic dataset for testing purposes"""
        logger.info("Creating synthetic dataset for testing...")
        
        from PIL import Image, ImageDraw
        import random
        
        colors = {
            'pothole': [(139, 69, 19), (160, 82, 45), (210, 180, 140)],  # Brown tones
            'garbage': [(128, 128, 128), (169, 169, 169), (105, 105, 105)],  # Gray tones
            'water_leak': [(0, 191, 255), (30, 144, 255), (135, 206, 235)],  # Blue tones
            'broken_light': [(255, 255, 0), (255, 215, 0), (255, 140, 0)],  # Yellow/orange tones
            'damaged_road': [(105, 105, 105), (128, 128, 128), (169, 169, 169)],  # Gray tones
            'other': [(255, 0, 0), (0, 255, 0), (0, 0, 255)]  # Various colors
        }
        
        for category in self.categories:
            logger.info(f"Creating synthetic images for: {category}")
            
            for i in range(images_per_category):
                # Determine split
                split = 'train' if i < int(images_per_category * 0.8) else 'validation'
                
                # Create synthetic image
                img = Image.new('RGB', (224, 224), color=random.choice(colors[category]))
                draw = ImageDraw.Draw(img)
                
                # Add some random shapes to make images different
                for _ in range(random.randint(3, 8)):
                    x1, y1 = random.randint(0, 200), random.randint(0, 200)
                    x2, y2 = x1 + random.randint(10, 50), y1 + random.randint(10, 50)
                    color = random.choice(colors[category])
                    draw.rectangle([x1, y1, x2, y2], fill=color)
                
                # Save image
                filename = f"synthetic_{category}_{i:04d}.jpg"
                filepath = os.path.join(self.dataset_path, split, category, filename)
                img.save(filepath, 'JPEG')
            
            logger.info(f"Created {images_per_category} synthetic images for {category}")
        
        logger.info("Synthetic dataset creation complete!")
        self.validate_dataset()

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Data collection for urban issue classifier')
    parser.add_argument('--create-sample', action='store_true',
                       help='Create sample URLs file')
    parser.add_argument('--collect-urls', type=str,
                       help='Collect images from URLs file')
    parser.add_argument('--validate', action='store_true',
                       help='Validate existing dataset')
    parser.add_argument('--synthetic', action='store_true',
                       help='Create synthetic dataset for testing')
    parser.add_argument('--dataset-path', type=str, default='./dataset',
                       help='Path to dataset directory')
    
    args = parser.parse_args()
    
    collector = DataCollector(args.dataset_path)
    
    if args.create_sample:
        collector.create_sample_urls_file()
    elif args.collect_urls:
        collector.collect_from_urls(args.collect_urls)
    elif args.validate:
        collector.validate_dataset()
    elif args.synthetic:
        collector.create_synthetic_dataset()
    else:
        logger.info("No action specified. Use --help for options.")
        logger.info("Quick start:")
        logger.info("1. python data_collector.py --create-sample")
        logger.info("2. Edit sample_urls.json with real image URLs")
        logger.info("3. python data_collector.py --collect-urls sample_urls.json")
        logger.info("4. python data_collector.py --validate")
        logger.info("\nOr for testing:")
        logger.info("python data_collector.py --synthetic")

if __name__ == '__main__':
    main()
