#!/usr/bin/env python3
"""
Setup script for CityPulse AI Service
This script will:
1. Install dependencies
2. Train the AI model
3. Test the model
4. Start the Flask API service
"""

import os
import sys
import subprocess
import time
import requests

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("Please use Python 3.8 or higher")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    # Check if requirements.txt exists
    if not os.path.exists('requirements.txt'):
        print("❌ requirements.txt not found")
        return False
    
    # Install dependencies
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python packages"
    )

def train_model():
    """Train the AI model"""
    print("\n🤖 Training AI model...")
    
    # Check if training script exists
    if not os.path.exists('train_citypulse_model.py'):
        print("❌ Training script not found")
        return False
    
    # Run training
    return run_command(
        f"{sys.executable} train_citypulse_model.py",
        "Training AI model"
    )

def test_model():
    """Test the trained model"""
    print("\n🧪 Testing trained model...")
    
    # Check if model exists
    if not os.path.exists('models/issue_classifier.h5'):
        print("❌ Trained model not found")
        return False
    
    # Check if test script exists
    if not os.path.exists('test_trained_model.py'):
        print("❌ Test script not found")
        return False
    
    # Run tests
    return run_command(
        f"{sys.executable} test_trained_model.py",
        "Testing AI model"
    )

def start_flask_service():
    """Start the Flask API service"""
    print("\n🚀 Starting Flask API service...")
    
    # Check if app.py exists
    if not os.path.exists('app.py'):
        print("❌ Flask app not found")
        return False
    
    print("Starting Flask service on http://localhost:5001")
    print("Press Ctrl+C to stop the service")
    
    try:
        # Start Flask app
        subprocess.run([sys.executable, 'app.py'], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Flask service stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Flask service failed to start: {e}")
        return False
    
    return True

def check_service_health():
    """Check if the Flask service is running"""
    try:
        response = requests.get('http://localhost:5001/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service is healthy: {data}")
            return True
        else:
            print(f"❌ Service health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Service is not responding: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 CityPulse AI Service Setup")
    print("=" * 50)
    
    # Change to ai-service directory if not already there
    if not os.path.basename(os.getcwd()) == 'ai-service':
        if os.path.exists('ai-service'):
            os.chdir('ai-service')
            print("📁 Changed to ai-service directory")
        else:
            print("❌ ai-service directory not found")
            return False
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        return False
    
    # Train model
    print("\n" + "="*50)
    print("🤖 AI MODEL TRAINING")
    print("="*50)
    
    if os.path.exists('models/issue_classifier.h5'):
        response = input("\n⚠️  Model already exists. Retrain? (y/N): ")
        if response.lower() != 'y':
            print("⏭️  Skipping model training")
        else:
            if not train_model():
                print("❌ Failed to train model")
                return False
    else:
        if not train_model():
            print("❌ Failed to train model")
            return False
    
    # Test model
    print("\n" + "="*50)
    print("🧪 MODEL TESTING")
    print("="*50)
    
    if not test_model():
        print("⚠️  Model testing failed, but continuing...")
    
    # Start Flask service
    print("\n" + "="*50)
    print("🚀 STARTING AI SERVICE")
    print("="*50)
    
    print("\nSetup completed successfully! 🎉")
    print("\nYou can now:")
    print("1. Start the AI service: python app.py")
    print("2. Test the API: curl -X POST -F 'image=@test.jpg' http://localhost:5001/classify")
    print("3. Check health: curl http://localhost:5001/health")
    
    # Ask if user wants to start the service now
    response = input("\nStart the Flask API service now? (Y/n): ")
    if response.lower() != 'n':
        start_flask_service()
    
    return True

def quick_start():
    """Quick start for development"""
    print("🚀 CityPulse AI Service - Quick Start")
    print("=" * 50)
    
    # Check if model exists
    if os.path.exists('models/issue_classifier.h5'):
        print("✅ Model found, starting service...")
        start_flask_service()
    else:
        print("❌ Model not found. Running full setup...")
        main()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--quick':
        quick_start()
    else:
        main()
