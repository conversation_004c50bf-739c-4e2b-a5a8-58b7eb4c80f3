import os
import logging
import random
from flask import Flask, request, jsonify
from flask_cors import CORS
from PIL import Image
import io
import requests
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Configuration
class Config:
    CONFIDENCE_THRESHOLD = float(os.getenv('CONFIDENCE_THRESHOLD', 0.7))
    MAX_IMAGE_SIZE = int(os.getenv('MAX_IMAGE_SIZE', 5242880))  # 5MB
    ALLOWED_EXTENSIONS = os.getenv('ALLOWED_EXTENSIONS', 'jpg,jpeg,png,gif').split(',')
    BACKEND_API_URL = os.getenv('BACKEND_API_URL', 'http://localhost:5000')

# Issue categories mapping
ISSUE_CATEGORIES = ['pothole', 'garbage', 'water_leak', 'broken_light', 'damaged_road', 'other']

def simulate_classification(image_data):
    """Simulate AI classification for demo purposes"""
    try:
        # Open image to verify it's valid
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Simulate classification based on image properties
        width, height = image.size
        
        # Simple heuristic based on image characteristics
        if width > height:
            # Landscape images more likely to be roads/potholes
            category_weights = [0.4, 0.1, 0.1, 0.1, 0.2, 0.1]  # pothole, garbage, water_leak, broken_light, damaged_road, other
        else:
            # Portrait images more likely to be garbage/lights
            category_weights = [0.1, 0.3, 0.1, 0.3, 0.1, 0.1]
        
        # Add some randomness
        random_factor = [random.uniform(0.8, 1.2) for _ in category_weights]
        adjusted_weights = [w * r for w, r in zip(category_weights, random_factor)]
        
        # Normalize weights
        total_weight = sum(adjusted_weights)
        probabilities = [w / total_weight for w in adjusted_weights]
        
        # Select category with highest probability
        max_prob_index = probabilities.index(max(probabilities))
        category = ISSUE_CATEGORIES[max_prob_index]
        confidence = probabilities[max_prob_index]
        
        return {
            'category': category,
            'confidence': confidence,
            'all_predictions': {
                ISSUE_CATEGORIES[i]: probabilities[i] 
                for i in range(len(ISSUE_CATEGORIES))
            }
        }
    except Exception as e:
        logger.error(f"Error during classification simulation: {str(e)}")
        # Return default classification
        return {
            'category': 'other',
            'confidence': 0.5,
            'all_predictions': {cat: 1.0/len(ISSUE_CATEGORIES) for cat in ISSUE_CATEGORIES}
        }

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'model_loaded': True,  # Simulated
        'service_type': 'demo_classifier'
    })

@app.route('/classify', methods=['POST'])
def classify_image():
    """Classify an image and return the predicted issue category"""
    try:
        # Check if image is provided
        if 'image' not in request.files and 'imageUrl' not in request.json:
            return jsonify({
                'success': False,
                'error': 'No image provided. Send image file or imageUrl.'
            }), 400
        
        # Get image data
        if 'image' in request.files:
            # Direct file upload
            file = request.files['image']
            if file.filename == '':
                return jsonify({
                    'success': False,
                    'error': 'No file selected'
                }), 400
            
            # Check file extension
            file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
            if file_ext not in Config.ALLOWED_EXTENSIONS:
                return jsonify({
                    'success': False,
                    'error': f'Invalid file type. Allowed: {", ".join(Config.ALLOWED_EXTENSIONS)}'
                }), 400
            
            image_data = file.read()
        else:
            # Image URL provided
            data = request.get_json()
            image_url = data.get('imageUrl')
            
            if not image_url:
                return jsonify({
                    'success': False,
                    'error': 'imageUrl is required'
                }), 400
            
            # Download image from URL
            try:
                response = requests.get(image_url, timeout=10)
                response.raise_for_status()
                image_data = response.content
            except requests.RequestException as e:
                return jsonify({
                    'success': False,
                    'error': f'Failed to download image: {str(e)}'
                }), 400
        
        # Check image size
        if len(image_data) > Config.MAX_IMAGE_SIZE:
            return jsonify({
                'success': False,
                'error': f'Image too large. Maximum size: {Config.MAX_IMAGE_SIZE} bytes'
            }), 400
        
        # Make prediction (simulated)
        result = simulate_classification(image_data)
        
        # Check confidence threshold
        is_confident = result['confidence'] >= Config.CONFIDENCE_THRESHOLD
        
        response_data = {
            'success': True,
            'category': result['category'],
            'confidence': result['confidence'],
            'is_confident': is_confident,
            'threshold': Config.CONFIDENCE_THRESHOLD,
            'all_predictions': result['all_predictions'],
            'timestamp': datetime.utcnow().isoformat(),
            'note': 'This is a demo classifier. Replace with actual ML model for production.'
        }
        
        # If issueId is provided, update the backend
        if request.is_json and 'issueId' in request.get_json():
            issue_id = request.get_json()['issueId']
            try:
                update_backend_classification(issue_id, result)
                response_data['backend_updated'] = True
            except Exception as e:
                logger.error(f"Failed to update backend: {str(e)}")
                response_data['backend_updated'] = False
                response_data['backend_error'] = str(e)
        
        return jsonify(response_data)
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 400
    except Exception as e:
        logger.error(f"Unexpected error in classify_image: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

def update_backend_classification(issue_id, classification_result):
    """Update the backend with AI classification results"""
    try:
        url = f"{Config.BACKEND_API_URL}/api/issues/{issue_id}/ai-classification"
        data = {
            'aiClassification': {
                'category': classification_result['category'],
                'confidence': classification_result['confidence'],
                'processedAt': datetime.utcnow().isoformat()
            }
        }
        
        response = requests.patch(url, json=data, timeout=5)
        response.raise_for_status()
        logger.info(f"Successfully updated backend for issue {issue_id}")
        
    except requests.RequestException as e:
        logger.error(f"Failed to update backend for issue {issue_id}: {str(e)}")
        raise

@app.route('/model/info', methods=['GET'])
def model_info():
    """Get information about the loaded model"""
    return jsonify({
        'success': True,
        'model_type': 'demo_classifier',
        'categories': ISSUE_CATEGORIES,
        'confidence_threshold': Config.CONFIDENCE_THRESHOLD,
        'max_image_size': Config.MAX_IMAGE_SIZE,
        'allowed_extensions': Config.ALLOWED_EXTENSIONS,
        'note': 'This is a demo service. Replace with actual ML model for production.'
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500

if __name__ == '__main__':
    # Run the app
    port = int(os.getenv('PORT', 5001))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    
    logger.info(f"Starting AI Classification Demo Service on port {port}")
    logger.info("Note: This is a demo service with simulated AI classification")
    app.run(host='0.0.0.0', port=port, debug=debug)
