import requests
import json
import os
import time
from PIL import Image
import io
import base64

class AIServiceTester:
    """Test the AI classification service"""
    
    def __init__(self, base_url='http://localhost:5001'):
        self.base_url = base_url
    
    def test_health(self):
        """Test health endpoint"""
        print("Testing health endpoint...")
        try:
            response = requests.get(f"{self.base_url}/health")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            return response.status_code == 200
        except Exception as e:
            print(f"Health check failed: {str(e)}")
            return False
    
    def test_model_info(self):
        """Test model info endpoint"""
        print("\nTesting model info endpoint...")
        try:
            response = requests.get(f"{self.base_url}/model/info")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            return response.status_code == 200
        except Exception as e:
            print(f"Model info test failed: {str(e)}")
            return False
    
    def create_test_image(self, category='pothole'):
        """Create a test image for classification"""
        # Create a simple colored image
        colors = {
            'pothole': (139, 69, 19),      # Brown
            'garbage': (128, 128, 128),    # Gray
            'water_leak': (0, 191, 255),  # Blue
            'broken_light': (255, 255, 0), # Yellow
            'damaged_road': (105, 105, 105), # Dark gray
            'other': (255, 0, 0)           # Red
        }
        
        color = colors.get(category, (255, 255, 255))
        img = Image.new('RGB', (224, 224), color=color)
        
        # Save to bytes
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='JPEG')
        img_bytes.seek(0)
        
        return img_bytes
    
    def test_classification_file_upload(self):
        """Test classification with file upload"""
        print("\nTesting classification with file upload...")
        
        # Create test image
        test_image = self.create_test_image('pothole')
        
        try:
            files = {'image': ('test_pothole.jpg', test_image, 'image/jpeg')}
            response = requests.post(f"{self.base_url}/classify", files=files)
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Predicted Category: {result.get('category')}")
                print(f"Confidence: {result.get('confidence'):.3f}")
                return True
            return False
            
        except Exception as e:
            print(f"File upload test failed: {str(e)}")
            return False
    
    def test_classification_url(self, image_url=None):
        """Test classification with image URL"""
        print("\nTesting classification with image URL...")
        
        # Use a sample image URL if none provided
        if not image_url:
            image_url = "https://via.placeholder.com/224x224/8B4513/FFFFFF?text=Pothole"
        
        try:
            data = {'imageUrl': image_url}
            response = requests.post(
                f"{self.base_url}/classify",
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Predicted Category: {result.get('category')}")
                print(f"Confidence: {result.get('confidence'):.3f}")
                return True
            return False
            
        except Exception as e:
            print(f"URL test failed: {str(e)}")
            return False
    
    def test_error_cases(self):
        """Test error handling"""
        print("\nTesting error cases...")
        
        # Test 1: No image provided
        print("Test 1: No image provided")
        try:
            response = requests.post(f"{self.base_url}/classify")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"Error: {str(e)}")
        
        # Test 2: Invalid image URL
        print("\nTest 2: Invalid image URL")
        try:
            data = {'imageUrl': 'https://invalid-url-that-does-not-exist.com/image.jpg'}
            response = requests.post(
                f"{self.base_url}/classify",
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"Error: {str(e)}")
        
        # Test 3: Invalid file type
        print("\nTest 3: Invalid file type")
        try:
            files = {'image': ('test.txt', io.StringIO('not an image'), 'text/plain')}
            response = requests.post(f"{self.base_url}/classify", files=files)
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        except Exception as e:
            print(f"Error: {str(e)}")
    
    def test_performance(self, num_requests=10):
        """Test service performance"""
        print(f"\nTesting performance with {num_requests} requests...")
        
        times = []
        successes = 0
        
        for i in range(num_requests):
            test_image = self.create_test_image()
            
            start_time = time.time()
            try:
                files = {'image': (f'test_{i}.jpg', test_image, 'image/jpeg')}
                response = requests.post(f"{self.base_url}/classify", files=files)
                
                end_time = time.time()
                request_time = end_time - start_time
                times.append(request_time)
                
                if response.status_code == 200:
                    successes += 1
                
                print(f"Request {i+1}: {request_time:.3f}s - Status: {response.status_code}")
                
            except Exception as e:
                print(f"Request {i+1} failed: {str(e)}")
        
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"\nPerformance Summary:")
            print(f"Successful requests: {successes}/{num_requests}")
            print(f"Average response time: {avg_time:.3f}s")
            print(f"Min response time: {min_time:.3f}s")
            print(f"Max response time: {max_time:.3f}s")
    
    def run_all_tests(self):
        """Run all tests"""
        print("=" * 60)
        print("AI SERVICE COMPREHENSIVE TEST SUITE")
        print("=" * 60)
        
        tests = [
            ("Health Check", self.test_health),
            ("Model Info", self.test_model_info),
            ("File Upload Classification", self.test_classification_file_upload),
            ("URL Classification", self.test_classification_url),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"Test failed with exception: {str(e)}")
                results[test_name] = False
        
        # Run error tests
        print(f"\n{'='*20} Error Handling Tests {'='*20}")
        self.test_error_cases()
        
        # Run performance tests
        print(f"\n{'='*20} Performance Tests {'='*20}")
        self.test_performance(5)
        
        # Summary
        print(f"\n{'='*20} TEST SUMMARY {'='*20}")
        for test_name, passed in results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"{test_name}: {status}")
        
        total_tests = len(results)
        passed_tests = sum(results.values())
        print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
        
        return passed_tests == total_tests

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Test AI Classification Service')
    parser.add_argument('--url', type=str, default='http://localhost:5001',
                       help='Base URL of the AI service')
    parser.add_argument('--test', type=str, choices=['health', 'model', 'file', 'url', 'errors', 'performance', 'all'],
                       default='all', help='Specific test to run')
    parser.add_argument('--image-url', type=str,
                       help='Image URL for URL classification test')
    parser.add_argument('--performance-requests', type=int, default=10,
                       help='Number of requests for performance test')
    
    args = parser.parse_args()
    
    tester = AIServiceTester(args.url)
    
    if args.test == 'health':
        tester.test_health()
    elif args.test == 'model':
        tester.test_model_info()
    elif args.test == 'file':
        tester.test_classification_file_upload()
    elif args.test == 'url':
        tester.test_classification_url(args.image_url)
    elif args.test == 'errors':
        tester.test_error_cases()
    elif args.test == 'performance':
        tester.test_performance(args.performance_requests)
    elif args.test == 'all':
        tester.run_all_tests()

if __name__ == '__main__':
    main()
