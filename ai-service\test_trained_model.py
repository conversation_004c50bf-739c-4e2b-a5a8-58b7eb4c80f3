#!/usr/bin/env python3
"""
Test script for the trained CityPulse AI model
"""

import tensorflow as tf
import numpy as np
from PIL import Image
import json
import os
import requests
import io

# Load model configuration
def load_model_config():
    """Load model configuration"""
    config_path = 'models/model_config.json'
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            return json.load(f)
    else:
        # Default configuration matching Flask API
        return {
            'class_labels': {
                0: 'pothole',
                1: 'garbage', 
                2: 'water_leak',
                3: 'broken_light',
                4: 'damaged_road',
                5: 'other'
            },
            'img_size': 224,
            'num_classes': 6
        }

def predict_image(image_path, model_path='models/issue_classifier.h5'):
    """
    Predict the category of an urban issue from an image
    
    Args:
        image_path (str): Path to the image file
        model_path (str): Path to the trained model
    
    Returns:
        dict: Prediction results with category and confidence
    """
    # Load configuration
    config = load_model_config()
    class_labels = {int(k): v for k, v in config['class_labels'].items()}
    img_size = config['img_size']
    
    # Load model
    model = tf.keras.models.load_model(model_path)
    print(f"Model loaded from {model_path}")
    
    # Load and preprocess image
    img = Image.open(image_path)
    img = img.convert('RGB')
    img = img.resize((img_size, img_size))
    img_array = np.array(img) / 255.0
    img_array = np.expand_dims(img_array, axis=0)
    
    # Make prediction
    predictions = model.predict(img_array)
    predicted_class = np.argmax(predictions[0])
    confidence = float(predictions[0][predicted_class])
    
    # Get class name
    category = class_labels[predicted_class]
    
    return {
        'category': category,
        'confidence': confidence,
        'all_predictions': {
            class_labels[i]: float(predictions[0][i]) 
            for i in range(len(class_labels))
        }
    }

def test_flask_api(image_path, api_url='http://localhost:5001'):
    """Test the Flask API with an image"""
    try:
        # Test health endpoint
        health_response = requests.get(f'{api_url}/health')
        print(f"Health check: {health_response.json()}")
        
        # Test classification endpoint
        with open(image_path, 'rb') as f:
            files = {'image': f}
            response = requests.post(f'{api_url}/classify', files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"\nAPI Classification Result:")
            print(f"Category: {result['category']}")
            print(f"Confidence: {result['confidence']:.3f}")
            print(f"Is Confident: {result['is_confident']}")
            return result
        else:
            print(f"API Error: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("Flask API is not running. Start it with: python app.py")
        return None
    except Exception as e:
        print(f"Error testing API: {str(e)}")
        return None

def create_test_image(category='pothole', filename='test_image.jpg'):
    """Create a simple test image for a specific category"""
    import cv2
    
    img_size = 224
    img = np.random.randint(80, 180, (img_size, img_size, 3), dtype=np.uint8)
    
    if category == 'pothole':
        cv2.circle(img, (112, 112), 30, (20, 25, 30), -1)
    elif category == 'garbage':
        for _ in range(8):
            x, y = np.random.randint(0, img_size-20, 2)
            w, h = np.random.randint(5, 20, 2)
            color = tuple(np.random.randint(50, 255, 3).tolist())
            cv2.rectangle(img, (x, y), (x+w, y+h), color, -1)
    elif category == 'water_leak':
        cv2.circle(img, (112, 112), 40, (80, 120, 160), -1)
    elif category == 'broken_light':
        cv2.rectangle(img, (110, 0), (115, img_size), (120, 120, 80), -1)
        cv2.rectangle(img, (108, 50), (117, 100), (30, 30, 30), -1)
    
    cv2.imwrite(filename, img)
    print(f"Test image created: {filename}")
    return filename

def main():
    """Main testing function"""
    print("🧪 Testing CityPulse AI Model")
    
    # Check if model exists
    model_path = 'models/issue_classifier.h5'
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        print("Please run: python train_citypulse_model.py first")
        return
    
    # Create test images for each category
    config = load_model_config()
    categories = list(config['class_labels'].values())
    
    print(f"\n📊 Testing categories: {categories}")
    
    for category in categories[:4]:  # Test first 4 categories
        print(f"\n--- Testing {category} ---")
        
        # Create test image
        test_image = create_test_image(category, f'test_{category}.jpg')
        
        # Test direct model prediction
        try:
            result = predict_image(test_image, model_path)
            print(f"Direct Model Prediction:")
            print(f"  Category: {result['category']}")
            print(f"  Confidence: {result['confidence']:.3f}")
            print(f"  Expected: {category}")
            print(f"  Correct: {result['category'] == category}")
        except Exception as e:
            print(f"  Error: {str(e)}")
        
        # Test Flask API
        api_result = test_flask_api(test_image)
        
        # Clean up test image
        if os.path.exists(test_image):
            os.remove(test_image)
    
    print("\n✅ Testing completed!")
    print("\nTo start the Flask API server:")
    print("  cd ai-service")
    print("  python app.py")
    print("\nTo test with curl:")
    print("  curl -X POST -F 'image=@test_image.jpg' http://localhost:5001/classify")

if __name__ == "__main__":
    main()
