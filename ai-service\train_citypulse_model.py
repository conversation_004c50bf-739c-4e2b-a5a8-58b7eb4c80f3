#!/usr/bin/env python3
"""
CityPulse AI Image Classification Model Training Script
Trains a CNN model to classify urban civic issues and saves it for the Flask API
"""

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import numpy as np
import matplotlib.pyplot as plt
import os
import json
import cv2
import random
from PIL import Image
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration - Match Flask API expectations
IMG_SIZE = 224
BATCH_SIZE = 32
EPOCHS = 25
NUM_CLASSES = 6

# Class labels - Match Flask API categories
CLASS_NAMES = ['pothole', 'garbage', 'water_leak', 'broken_light', 'damaged_road', 'other']
CLASS_LABELS = {
    0: 'pothole',
    1: 'garbage', 
    2: 'water_leak',
    3: 'broken_light',
    4: 'damaged_road',
    5: 'other'
}

def create_dataset_structure():
    """Create dataset directory structure"""
    dataset_dir = 'training_dataset'
    os.makedirs(dataset_dir, exist_ok=True)
    
    for class_name in CLASS_NAMES:
        class_dir = os.path.join(dataset_dir, class_name)
        os.makedirs(class_dir, exist_ok=True)
        logger.info(f"Created directory: {class_dir}")
    
    return dataset_dir

def generate_synthetic_data(dataset_dir, samples_per_class=80):
    """Generate synthetic dataset for demonstration"""
    logger.info(f"Generating {samples_per_class} samples per class...")
    
    for i, class_name in enumerate(CLASS_NAMES):
        class_dir = os.path.join(dataset_dir, class_name)
        
        for j in range(samples_per_class):
            # Create base image with realistic urban background
            img = np.random.randint(80, 180, (IMG_SIZE, IMG_SIZE, 3), dtype=np.uint8)
            
            # Add noise for realism
            noise = np.random.normal(0, 15, (IMG_SIZE, IMG_SIZE, 3))
            img = np.clip(img + noise, 0, 255).astype(np.uint8)
            
            # Add class-specific patterns
            if class_name == 'pothole':
                # Dark circular/oval patterns for potholes
                for _ in range(random.randint(1, 3)):
                    center = (random.randint(40, IMG_SIZE-40), random.randint(40, IMG_SIZE-40))
                    axes = (random.randint(15, 35), random.randint(10, 25))
                    cv2.ellipse(img, center, axes, random.randint(0, 180), 0, 360, (20, 25, 30), -1)
                    
            elif class_name == 'garbage':
                # Scattered colorful objects for garbage
                for _ in range(random.randint(5, 15)):
                    x, y = random.randint(0, IMG_SIZE-20), random.randint(0, IMG_SIZE-20)
                    w, h = random.randint(5, 20), random.randint(5, 20)
                    color = (random.randint(50, 255), random.randint(50, 255), random.randint(50, 255))
                    cv2.rectangle(img, (x, y), (x+w, y+h), color, -1)
                    
            elif class_name == 'water_leak':
                # Blue/dark wet patches for water leaks
                for _ in range(random.randint(1, 3)):
                    center = (random.randint(30, IMG_SIZE-30), random.randint(30, IMG_SIZE-30))
                    radius = random.randint(20, 50)
                    cv2.circle(img, center, radius, (80, 120, 160), -1)
                    # Add some flow patterns
                    cv2.ellipse(img, center, (radius+10, radius//2), random.randint(0, 180), 
                               0, 360, (60, 100, 140), -1)
                    
            elif class_name == 'broken_light':
                # Vertical structures with dark/broken areas
                x = random.randint(IMG_SIZE//3, 2*IMG_SIZE//3)
                cv2.rectangle(img, (x-5, 0), (x+5, IMG_SIZE), (120, 120, 80), -1)
                # Add broken/dark sections
                for _ in range(random.randint(1, 3)):
                    y_start = random.randint(0, IMG_SIZE//2)
                    y_end = y_start + random.randint(20, 60)
                    cv2.rectangle(img, (x-8, y_start), (x+8, y_end), (30, 30, 30), -1)
                    
            elif class_name == 'damaged_road':
                # Cracks and rough surfaces
                for _ in range(random.randint(3, 8)):
                    start_point = (random.randint(0, IMG_SIZE), random.randint(0, IMG_SIZE))
                    end_point = (random.randint(0, IMG_SIZE), random.randint(0, IMG_SIZE))
                    cv2.line(img, start_point, end_point, (40, 40, 40), random.randint(2, 5))
                    
            elif class_name == 'other':
                # Mixed patterns for other category
                pattern_type = random.randint(0, 2)
                if pattern_type == 0:
                    # Random geometric shapes
                    for _ in range(random.randint(2, 6)):
                        center = (random.randint(20, IMG_SIZE-20), random.randint(20, IMG_SIZE-20))
                        radius = random.randint(10, 30)
                        color = (random.randint(100, 200), random.randint(100, 200), random.randint(100, 200))
                        cv2.circle(img, center, radius, color, -1)
            
            # Save image
            img_path = os.path.join(class_dir, f'{class_name}_{j:03d}.jpg')
            cv2.imwrite(img_path, img)
        
        logger.info(f"Generated {samples_per_class} samples for {class_name}")
    
    logger.info("Synthetic dataset generated successfully!")

def create_cnn_model():
    """Create CNN model architecture optimized for urban issue classification"""
    model = keras.Sequential([
        # Input layer
        layers.Input(shape=(IMG_SIZE, IMG_SIZE, 3)),
        
        # First Convolutional Block
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D(2, 2),
        layers.Dropout(0.25),
        
        # Second Convolutional Block
        layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D(2, 2),
        layers.Dropout(0.25),
        
        # Third Convolutional Block
        layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D(2, 2),
        layers.Dropout(0.25),
        
        # Fourth Convolutional Block
        layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling2D(2, 2),
        layers.Dropout(0.25),
        
        # Global Average Pooling and Dense layers
        layers.GlobalAveragePooling2D(),
        layers.Dense(512, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),
        layers.Dense(256, activation='relu'),
        layers.Dropout(0.3),
        layers.Dense(NUM_CLASSES, activation='softmax')
    ])
    
    return model

def setup_data_generators(dataset_dir):
    """Setup data generators for training and validation"""
    # Data augmentation for training
    train_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=25,
        width_shift_range=0.2,
        height_shift_range=0.2,
        horizontal_flip=True,
        vertical_flip=False,
        brightness_range=[0.8, 1.2],
        zoom_range=0.2,
        shear_range=0.1,
        fill_mode='nearest',
        validation_split=0.2
    )
    
    # Validation data (no augmentation, only rescaling)
    val_datagen = ImageDataGenerator(
        rescale=1./255,
        validation_split=0.2
    )
    
    # Load dataset using ImageDataGenerator
    train_generator = train_datagen.flow_from_directory(
        dataset_dir,
        target_size=(IMG_SIZE, IMG_SIZE),
        batch_size=BATCH_SIZE,
        class_mode='categorical',
        subset='training',
        classes=CLASS_NAMES,
        shuffle=True
    )
    
    validation_generator = val_datagen.flow_from_directory(
        dataset_dir,
        target_size=(IMG_SIZE, IMG_SIZE),
        batch_size=BATCH_SIZE,
        class_mode='categorical',
        subset='validation',
        classes=CLASS_NAMES,
        shuffle=False
    )
    
    return train_generator, validation_generator

def train_model(model, train_generator, validation_generator):
    """Train the CNN model with callbacks"""
    # Create models directory
    os.makedirs('models', exist_ok=True)
    
    # Callbacks for better training
    callbacks = [
        keras.callbacks.EarlyStopping(
            patience=8, 
            restore_best_weights=True,
            monitor='val_accuracy'
        ),
        keras.callbacks.ReduceLROnPlateau(
            factor=0.3, 
            patience=4,
            min_lr=1e-7,
            monitor='val_loss'
        ),
        keras.callbacks.ModelCheckpoint(
            'models/best_model.h5', 
            save_best_only=True,
            monitor='val_accuracy',
            mode='max'
        )
    ]
    
    # Compile model
    model.compile(
        optimizer=keras.optimizers.Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # Train the model
    history = model.fit(
        train_generator,
        epochs=EPOCHS,
        validation_data=validation_generator,
        callbacks=callbacks,
        verbose=1
    )
    
    return history

def save_model_for_flask_api(model):
    """Save the trained model in the format expected by Flask API"""
    # Create models directory
    os.makedirs('models', exist_ok=True)
    
    # Save the trained model for Flask API
    model_path = 'models/issue_classifier.h5'
    model.save(model_path)
    logger.info(f"Model saved as '{model_path}' for Flask API")
    
    # Save class labels and configuration
    config = {
        'class_labels': CLASS_LABELS,
        'class_names': CLASS_NAMES,
        'img_size': IMG_SIZE,
        'num_classes': NUM_CLASSES
    }
    
    with open('models/model_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    logger.info("Model configuration saved")
    
    return model_path

def plot_training_history(history):
    """Plot training history"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # Plot accuracy
    ax1.plot(history.history['accuracy'], label='Training Accuracy')
    ax1.plot(history.history['val_accuracy'], label='Validation Accuracy')
    ax1.set_title('Model Accuracy')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Accuracy')
    ax1.legend()
    ax1.grid(True)
    
    # Plot loss
    ax2.plot(history.history['loss'], label='Training Loss')
    ax2.plot(history.history['val_loss'], label='Validation Loss')
    ax2.set_title('Model Loss')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('models/training_history.png', dpi=300, bbox_inches='tight')
    plt.show()
    logger.info("Training history plot saved")

def main():
    """Main training function"""
    logger.info("🚀 Starting CityPulse AI Model Training")
    logger.info(f"TensorFlow version: {tf.__version__}")
    logger.info(f"GPU Available: {len(tf.config.list_physical_devices('GPU')) > 0}")
    logger.info(f"Classes: {CLASS_NAMES}")
    
    # Create dataset structure
    dataset_dir = create_dataset_structure()
    
    # Generate synthetic data
    generate_synthetic_data(dataset_dir, samples_per_class=100)
    
    # Create model
    model = create_cnn_model()
    
    logger.info("\nModel Architecture:")
    model.summary()
    
    # Setup data generators
    train_generator, validation_generator = setup_data_generators(dataset_dir)
    
    logger.info(f"\nTraining samples: {train_generator.samples}")
    logger.info(f"Validation samples: {validation_generator.samples}")
    logger.info(f"Class indices: {train_generator.class_indices}")
    
    # Train model
    logger.info("\n🔥 Starting training...")
    history = train_model(model, train_generator, validation_generator)
    
    # Evaluate model
    val_loss, val_accuracy = model.evaluate(validation_generator, verbose=0)
    logger.info(f"\n✅ Training completed!")
    logger.info(f"Final Validation Accuracy: {val_accuracy:.4f}")
    logger.info(f"Final Validation Loss: {val_loss:.4f}")
    
    # Save model for Flask API
    model_path = save_model_for_flask_api(model)
    
    # Plot training history
    plot_training_history(history)
    
    logger.info("\n🎉 Model training completed successfully!")
    logger.info(f"\nModel saved at: {model_path}")
    logger.info("You can now start the Flask API with: python app.py")
    
    return model, history

if __name__ == "__main__":
    model, history = main()
