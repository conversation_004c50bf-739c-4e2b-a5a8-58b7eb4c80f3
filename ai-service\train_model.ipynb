{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CityPulse AI Image Classification Model\n", "## Urban Civic Issues Classification using CNN\n", "\n", "This notebook trains a CNN model to classify urban civic issues into:\n", "- Pothole\n", "- Gar<PERSON>\n", "- Water Leakage\n", "- Street Light Issue\n", "- Others/Unknown"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Install required packages\n", "!pip install tensorflow keras pillow numpy matplotlib scikit-learn opencv-python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras import layers\n", "from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "from sklearn.model_selection import train_test_split\n", "from PIL import Image\n", "import cv2\n", "import json\n", "\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"GPU Available: {tf.config.list_physical_devices('GPU')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Dataset Setup and Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Configuration\n", "IMG_SIZE = 224\n", "BATCH_SIZE = 32\n", "EPOCHS = 20\n", "NUM_CLASSES = 5\n", "\n", "# Class labels\n", "CLASS_NAMES = ['pothole', 'garbage', 'water_leak', 'street_light', 'other']\n", "CLASS_LABELS = {\n", "    0: 'pothole',\n", "    1: 'garbage', \n", "    2: 'water_leak',\n", "    3: 'street_light',\n", "    4: 'other'\n", "}\n", "\n", "print(f\"Classes: {CLASS_NAMES}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Create dataset directory structure\n", "dataset_dir = 'dataset'\n", "os.makedirs(dataset_dir, exist_ok=True)\n", "\n", "for class_name in CLASS_NAMES:\n", "    class_dir = os.path.join(dataset_dir, class_name)\n", "    os.makedirs(class_dir, exist_ok=True)\n", "    print(f\"Created directory: {class_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Data Augmentation and Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Data augmentation for training\n", "train_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    rotation_range=20,\n", "    width_shift_range=0.2,\n", "    height_shift_range=0.2,\n", "    horizontal_flip=True,\n", "    brightness_range=[0.8, 1.2],\n", "    zoom_range=0.2,\n", "    validation_split=0.2\n", ")\n", "\n", "# Validation data (no augmentation, only rescaling)\n", "val_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    validation_split=0.2\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: CNN Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def create_cnn_model():\n", "    model = keras.Sequential([\n", "        # First Convolutional Block\n", "        layers.Conv2D(32, (3, 3), activation='relu', input_shape=(IMG_SIZE, IMG_SIZE, 3)),\n", "        layers.BatchNormalization(),\n", "        layers.MaxPooling2D(2, 2),\n", "        \n", "        # Second Convolutional Block\n", "        layers.Conv2D(64, (3, 3), activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.MaxPooling2D(2, 2),\n", "        \n", "        # Third Convolutional Block\n", "        layers.Conv2D(128, (3, 3), activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.MaxPooling2D(2, 2),\n", "        \n", "        # Fourth Convolutional Block\n", "        layers.Conv2D(256, (3, 3), activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.MaxPooling2D(2, 2),\n", "        \n", "        # Flatten and Dense layers\n", "        layers.<PERSON><PERSON>(),\n", "        layers.Dropout(0.5),\n", "        layers.Dense(512, activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.Dropout(0.3),\n", "        layers.Dense(NUM_CLASSES, activation='softmax')\n", "    ])\n", "    \n", "    return model\n", "\n", "# Create and compile model\n", "model = create_cnn_model()\n", "model.compile(\n", "    optimizer='adam',\n", "    loss='categorical_crossentropy',\n", "    metrics=['accuracy']\n", ")\n", "\n", "model.summary()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}