{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CityPulse AI Image Classification Model\n", "## Urban Civic Issues Classification using CNN\n", "\n", "This notebook trains a CNN model to classify urban civic issues into:\n", "- Pothole\n", "- Gar<PERSON>\n", "- Water Leakage\n", "- Street Light Issue\n", "- Others/Unknown"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Install required packages\n", "!pip install tensorflow keras pillow numpy matplotlib scikit-learn opencv-python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras import layers\n", "from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "from sklearn.model_selection import train_test_split\n", "from PIL import Image\n", "import cv2\n", "import json\n", "\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"GPU Available: {tf.config.list_physical_devices('GPU')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Dataset Setup and Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Configuration\n", "IMG_SIZE = 224\n", "BATCH_SIZE = 32\n", "EPOCHS = 20\n", "NUM_CLASSES = 5\n", "\n", "# Class labels\n", "CLASS_NAMES = ['pothole', 'garbage', 'water_leak', 'street_light', 'other']\n", "CLASS_LABELS = {\n", "    0: 'pothole',\n", "    1: 'garbage', \n", "    2: 'water_leak',\n", "    3: 'street_light',\n", "    4: 'other'\n", "}\n", "\n", "print(f\"Classes: {CLASS_NAMES}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Create dataset directory structure\n", "dataset_dir = 'dataset'\n", "os.makedirs(dataset_dir, exist_ok=True)\n", "\n", "for class_name in CLASS_NAMES:\n", "    class_dir = os.path.join(dataset_dir, class_name)\n", "    os.makedirs(class_dir, exist_ok=True)\n", "    print(f\"Created directory: {class_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Data Augmentation and Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Data augmentation for training\n", "train_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    rotation_range=20,\n", "    width_shift_range=0.2,\n", "    height_shift_range=0.2,\n", "    horizontal_flip=True,\n", "    brightness_range=[0.8, 1.2],\n", "    zoom_range=0.2,\n", "    validation_split=0.2\n", ")\n", "\n", "# Validation data (no augmentation, only rescaling)\n", "val_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    validation_split=0.2\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: CNN Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def create_cnn_model():\n", "    model = keras.Sequential([\n", "        # First Convolutional Block\n", "        layers.Conv2D(32, (3, 3), activation='relu', input_shape=(IMG_SIZE, IMG_SIZE, 3)),\n", "        layers.BatchNormalization(),\n", "        layers.MaxPooling2D(2, 2),\n", "        \n", "        # Second Convolutional Block\n", "        layers.Conv2D(64, (3, 3), activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.MaxPooling2D(2, 2),\n", "        \n", "        # Third Convolutional Block\n", "        layers.Conv2D(128, (3, 3), activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.MaxPooling2D(2, 2),\n", "        \n", "        # Fourth Convolutional Block\n", "        layers.Conv2D(256, (3, 3), activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.MaxPooling2D(2, 2),\n", "        \n", "        # Flatten and Dense layers\n", "        layers.<PERSON><PERSON>(),\n", "        layers.Dropout(0.5),\n", "        layers.Dense(512, activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.Dropout(0.3),\n", "        layers.Dense(NUM_CLASSES, activation='softmax')\n", "    ])\n", "    \n", "    return model\n", "\n", "# Create and compile model\n", "model = create_cnn_model()\n", "model.compile(\n", "    optimizer='adam',\n", "    loss='categorical_crossentropy',\n", "    metrics=['accuracy']\n", ")\n", "\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Generate Sample Dataset (for demonstration)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Generate synthetic dataset for demonstration\n", "def generate_sample_data():\n", "    \"\"\"Generate sample images for each class\"\"\"\n", "    import random\n", "    \n", "    for i, class_name in enumerate(CLASS_NAMES):\n", "        class_dir = os.path.join(dataset_dir, class_name)\n", "        \n", "        # Generate 60 sample images per class\n", "        for j in range(60):\n", "            # Create a random colored image with some patterns\n", "            img = np.random.randint(0, 255, (IMG_SIZE, IMG_SIZE, 3), dtype=np.uint8)\n", "            \n", "            # Add class-specific patterns\n", "            if class_name == 'pothole':\n", "                # Dark circular patterns for potholes\n", "                cv2.circle(img, (random.randint(50, 174), random.randint(50, 174)), \n", "                          random.ran<PERSON>t(20, 40), (30, 30, 30), -1)\n", "            elif class_name == 'garbage':\n", "                # Random scattered bright colors for garbage\n", "                for _ in range(10):\n", "                    cv2.rectangle(img, \n", "                                (random.randint(0, 200), random.randint(0, 200)),\n", "                                (random.randint(0, 200), random.randint(0, 200)),\n", "                                (random.randint(100, 255), random.randint(100, 255), random.randint(100, 255)), -1)\n", "            elif class_name == 'water_leak':\n", "                # Blue-ish patterns for water\n", "                cv2.ellipse(img, (random.randint(50, 174), random.randint(50, 174)),\n", "                           (random.randint(30, 60), random.randint(10, 30)), 0, 0, 360,\n", "                           (100, 150, 255), -1)\n", "            elif class_name == 'street_light':\n", "                # Vertical lines for street lights\n", "                cv2.line(img, (random.randint(100, 124), 0), \n", "                        (random.randint(100, 124), IMG_SIZE), (200, 200, 100), 5)\n", "            \n", "            # Save image\n", "            img_path = os.path.join(class_dir, f'{class_name}_{j:03d}.jpg')\n", "            cv2.imwrite(img_path, img)\n", "        \n", "        print(f\"Generated 60 sample images for {class_name}\")\n", "\n", "# Generate sample data\n", "generate_sample_data()\n", "print(\"Sample dataset generated successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Load and Split Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Load dataset using ImageDataGenerator\n", "train_generator = train_datagen.flow_from_directory(\n", "    dataset_dir,\n", "    target_size=(IMG_SIZE, IMG_SIZE),\n", "    batch_size=BATCH_SIZE,\n", "    class_mode='categorical',\n", "    subset='training',\n", "    classes=CLASS_NAMES\n", ")\n", "\n", "validation_generator = val_datagen.flow_from_directory(\n", "    dataset_dir,\n", "    target_size=(IMG_SIZE, IMG_SIZE),\n", "    batch_size=BATCH_SIZE,\n", "    class_mode='categorical',\n", "    subset='validation',\n", "    classes=CLASS_NAMES\n", ")\n", "\n", "print(f\"Training samples: {train_generator.samples}\")\n", "print(f\"Validation samples: {validation_generator.samples}\")\n", "print(f\"Class indices: {train_generator.class_indices}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Training the Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Callbacks for better training\n", "callbacks = [\n", "    keras.callbacks.EarlyStopping(patience=5, restore_best_weights=True),\n", "    keras.callbacks.ReduceLROnPlateau(factor=0.2, patience=3),\n", "    keras.callbacks.ModelCheckpoint('best_model.h5', save_best_only=True)\n", "]\n", "\n", "# Train the model\n", "history = model.fit(\n", "    train_generator,\n", "    epochs=EPOCHS,\n", "    validation_data=validation_generator,\n", "    callbacks=callbacks,\n", "    verbose=1\n", ")\n", "\n", "print(\"Training completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: <PERSON><PERSON>ate Model Performance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Plot training history\n", "def plot_training_history(history):\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))\n", "    \n", "    # Plot accuracy\n", "    ax1.plot(history.history['accuracy'], label='Training Accuracy')\n", "    ax1.plot(history.history['val_accuracy'], label='Validation Accuracy')\n", "    ax1.set_title('Model Accuracy')\n", "    ax1.set_xlabel('Epoch')\n", "    ax1.set_ylabel('Accuracy')\n", "    ax1.legend()\n", "    \n", "    # Plot loss\n", "    ax2.plot(history.history['loss'], label='Training Loss')\n", "    ax2.plot(history.history['val_loss'], label='Validation Loss')\n", "    ax2.set_title('Model Loss')\n", "    ax2.set_xlabel('Epoch')\n", "    ax2.set_ylabel('Loss')\n", "    ax2.legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "plot_training_history(history)\n", "\n", "# Evaluate on validation set\n", "val_loss, val_accuracy = model.evaluate(validation_generator)\n", "print(f\"Validation Accuracy: {val_accuracy:.4f}\")\n", "print(f\"Validation Loss: {val_loss:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Save Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Save the trained model\n", "model.save('citypulse_classifier.h5')\n", "print(\"Model saved as 'citypulse_classifier.h5'\")\n", "\n", "# Save class labels for later use\n", "with open('class_labels.json', 'w') as f:\n", "    json.dump(CLASS_LABELS, f)\n", "print(\"Class labels saved as 'class_labels.json'\")\n", "\n", "# Save model configuration\n", "model_config = {\n", "    'img_size': IMG_SIZE,\n", "    'num_classes': NUM_CLASSES,\n", "    'class_names': CLASS_NAMES,\n", "    'class_labels': CLASS_LABELS\n", "}\n", "\n", "with open('model_config.json', 'w') as f:\n", "    json.dump(model_config, f)\n", "print(\"Model configuration saved as 'model_config.json'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 9: Prediction Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def predict_image(image_path, model_path='citypulse_classifier.h5'):\n", "    \"\"\"\n", "    Predict the category of an urban issue from an image\n", "    \n", "    Args:\n", "        image_path (str): Path to the image file\n", "        model_path (str): Path to the trained model\n", "    \n", "    Returns:\n", "        dict: Prediction results with category and confidence\n", "    \"\"\"\n", "    # Load model if not already loaded\n", "    if 'loaded_model' not in globals():\n", "        global loaded_model\n", "        loaded_model = keras.models.load_model(model_path)\n", "        print(f\"Model loaded from {model_path}\")\n", "    \n", "    # Load and preprocess image\n", "    img = Image.open(image_path)\n", "    img = img.convert('RGB')\n", "    img = img.resize((IMG_SIZE, IMG_SIZE))\n", "    img_array = np.array(img) / 255.0\n", "    img_array = np.expand_dims(img_array, axis=0)\n", "    \n", "    # Make prediction\n", "    predictions = loaded_model.predict(img_array)\n", "    predicted_class = np.argmax(predictions[0])\n", "    confidence = float(predictions[0][predicted_class])\n", "    \n", "    # Get class name\n", "    category = CLASS_LABELS[predicted_class]\n", "    \n", "    return {\n", "        'category': category,\n", "        'confidence': confidence,\n", "        'all_predictions': {\n", "            CLASS_LABELS[i]: float(predictions[0][i]) \n", "            for i in range(len(CLASS_LABELS))\n", "        }\n", "    }\n", "\n", "# Test the prediction function\n", "print(\"\\nPrediction function created successfully!\")\n", "print(\"Usage: result = predict_image('path/to/image.jpg')\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 10: Test Predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Test prediction on sample images\n", "import glob\n", "\n", "print(\"Testing predictions on sample images:\")\n", "print(\"-\" * 50)\n", "\n", "for class_name in CLASS_NAMES[:3]:  # Test first 3 classes\n", "    class_dir = os.path.join(dataset_dir, class_name)\n", "    sample_images = glob.glob(os.path.join(class_dir, '*.jpg'))[:2]\n", "    \n", "    for img_path in sample_images:\n", "        result = predict_image(img_path)\n", "        print(f\"Image: {os.path.basename(img_path)}\")\n", "        print(f\"Predicted: {result['category']} (confidence: {result['confidence']:.3f})\")\n", "        print(f\"Actual: {class_name}\")\n", "        print()\n", "\n", "print(\"Model training and testing completed successfully!\")\n", "print(\"\\nFiles created:\")\n", "print(\"- citypulse_classifier.h5 (trained model)\")\n", "print(\"- class_labels.json (class mappings)\")\n", "print(\"- model_config.json (model configuration)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}