import os
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
import matplotlib.pyplot as plt
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
class TrainingConfig:
    IMAGE_SIZE = (224, 224)
    BATCH_SIZE = 32
    EPOCHS = 50
    LEARNING_RATE = 0.001
    VALIDATION_SPLIT = 0.2
    MODEL_SAVE_PATH = './models/issue_classifier.h5'
    DATASET_PATH = './dataset'
    
    # Issue categories
    CATEGORIES = ['pothole', 'garbage', 'water_leak', 'broken_light', 'damaged_road', 'other']
    NUM_CLASSES = len(CATEGORIES)

def create_dataset_structure():
    """Create dataset directory structure for training"""
    base_path = TrainingConfig.DATASET_PATH
    
    for split in ['train', 'validation']:
        for category in TrainingConfig.CATEGORIES:
            path = os.path.join(base_path, split, category)
            os.makedirs(path, exist_ok=True)
            logger.info(f"Created directory: {path}")
    
    logger.info("Dataset structure created. Please add images to the respective folders.")
    logger.info("Expected structure:")
    logger.info("dataset/")
    logger.info("├── train/")
    logger.info("│   ├── pothole/")
    logger.info("│   ├── garbage/")
    logger.info("│   ├── water_leak/")
    logger.info("│   ├── broken_light/")
    logger.info("│   ├── damaged_road/")
    logger.info("│   └── other/")
    logger.info("└── validation/")
    logger.info("    ├── pothole/")
    logger.info("    ├── garbage/")
    logger.info("    ├── water_leak/")
    logger.info("    ├── broken_light/")
    logger.info("    ├── damaged_road/")
    logger.info("    └── other/")

def create_data_generators():
    """Create data generators for training and validation"""
    
    # Data augmentation for training
    train_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=20,
        width_shift_range=0.2,
        height_shift_range=0.2,
        horizontal_flip=True,
        zoom_range=0.2,
        shear_range=0.2,
        fill_mode='nearest'
    )
    
    # Only rescaling for validation
    validation_datagen = ImageDataGenerator(rescale=1./255)
    
    # Create generators
    train_generator = train_datagen.flow_from_directory(
        os.path.join(TrainingConfig.DATASET_PATH, 'train'),
        target_size=TrainingConfig.IMAGE_SIZE,
        batch_size=TrainingConfig.BATCH_SIZE,
        class_mode='categorical',
        shuffle=True
    )
    
    validation_generator = validation_datagen.flow_from_directory(
        os.path.join(TrainingConfig.DATASET_PATH, 'validation'),
        target_size=TrainingConfig.IMAGE_SIZE,
        batch_size=TrainingConfig.BATCH_SIZE,
        class_mode='categorical',
        shuffle=False
    )
    
    return train_generator, validation_generator

def create_model():
    """Create the CNN model using transfer learning"""
    
    # Load pre-trained MobileNetV2 model
    base_model = MobileNetV2(
        weights='imagenet',
        include_top=False,
        input_shape=(*TrainingConfig.IMAGE_SIZE, 3)
    )
    
    # Freeze the base model
    base_model.trainable = False
    
    # Add custom classification head
    model = models.Sequential([
        base_model,
        layers.GlobalAveragePooling2D(),
        layers.Dropout(0.2),
        layers.Dense(128, activation='relu'),
        layers.Dropout(0.5),
        layers.Dense(TrainingConfig.NUM_CLASSES, activation='softmax')
    ])
    
    return model

def create_simple_cnn():
    """Create a simple CNN model from scratch"""
    
    model = models.Sequential([
        layers.Input(shape=(*TrainingConfig.IMAGE_SIZE, 3)),
        
        # First convolutional block
        layers.Conv2D(32, (3, 3), activation='relu'),
        layers.MaxPooling2D((2, 2)),
        layers.BatchNormalization(),
        
        # Second convolutional block
        layers.Conv2D(64, (3, 3), activation='relu'),
        layers.MaxPooling2D((2, 2)),
        layers.BatchNormalization(),
        
        # Third convolutional block
        layers.Conv2D(128, (3, 3), activation='relu'),
        layers.MaxPooling2D((2, 2)),
        layers.BatchNormalization(),
        
        # Fourth convolutional block
        layers.Conv2D(128, (3, 3), activation='relu'),
        layers.GlobalAveragePooling2D(),
        
        # Classification head
        layers.Dropout(0.5),
        layers.Dense(512, activation='relu'),
        layers.Dropout(0.3),
        layers.Dense(TrainingConfig.NUM_CLASSES, activation='softmax')
    ])
    
    return model

def train_model(use_transfer_learning=True):
    """Train the model"""
    
    # Check if dataset exists
    if not os.path.exists(TrainingConfig.DATASET_PATH):
        logger.error(f"Dataset path {TrainingConfig.DATASET_PATH} does not exist.")
        logger.info("Creating dataset structure...")
        create_dataset_structure()
        logger.info("Please add training images and run again.")
        return None
    
    # Create data generators
    try:
        train_generator, validation_generator = create_data_generators()
        logger.info(f"Found {train_generator.samples} training images")
        logger.info(f"Found {validation_generator.samples} validation images")
        logger.info(f"Classes: {train_generator.class_indices}")
    except Exception as e:
        logger.error(f"Error creating data generators: {str(e)}")
        logger.info("Make sure you have images in the dataset folders.")
        return None
    
    # Create model
    if use_transfer_learning:
        model = create_model()
        logger.info("Created model with transfer learning (MobileNetV2)")
    else:
        model = create_simple_cnn()
        logger.info("Created simple CNN model")
    
    # Compile model
    model.compile(
        optimizer=Adam(learning_rate=TrainingConfig.LEARNING_RATE),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # Print model summary
    model.summary()
    
    # Create callbacks
    callbacks = [
        EarlyStopping(
            monitor='val_loss',
            patience=10,
            restore_best_weights=True
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.2,
            patience=5,
            min_lr=1e-7
        ),
        ModelCheckpoint(
            TrainingConfig.MODEL_SAVE_PATH,
            monitor='val_accuracy',
            save_best_only=True,
            verbose=1
        )
    ]
    
    # Calculate steps per epoch
    steps_per_epoch = train_generator.samples // TrainingConfig.BATCH_SIZE
    validation_steps = validation_generator.samples // TrainingConfig.BATCH_SIZE
    
    # Train model
    logger.info("Starting training...")
    history = model.fit(
        train_generator,
        steps_per_epoch=steps_per_epoch,
        epochs=TrainingConfig.EPOCHS,
        validation_data=validation_generator,
        validation_steps=validation_steps,
        callbacks=callbacks,
        verbose=1
    )
    
    # Save final model
    os.makedirs(os.path.dirname(TrainingConfig.MODEL_SAVE_PATH), exist_ok=True)
    model.save(TrainingConfig.MODEL_SAVE_PATH)
    logger.info(f"Model saved to {TrainingConfig.MODEL_SAVE_PATH}")
    
    # Plot training history
    plot_training_history(history)
    
    return model, history

def plot_training_history(history):
    """Plot training history"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # Plot accuracy
    ax1.plot(history.history['accuracy'], label='Training Accuracy')
    ax1.plot(history.history['val_accuracy'], label='Validation Accuracy')
    ax1.set_title('Model Accuracy')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Accuracy')
    ax1.legend()
    
    # Plot loss
    ax2.plot(history.history['loss'], label='Training Loss')
    ax2.plot(history.history['val_loss'], label='Validation Loss')
    ax2.set_title('Model Loss')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('./models/training_history.png')
    plt.show()
    
    logger.info("Training history plot saved to ./models/training_history.png")

def create_demo_model_with_weights():
    """Create a demo model with some realistic weights for testing"""
    
    logger.info("Creating demo model with synthetic weights...")
    
    # Create simple model
    model = create_simple_cnn()
    
    # Compile model
    model.compile(
        optimizer=Adam(learning_rate=TrainingConfig.LEARNING_RATE),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # Create some dummy data to initialize weights
    dummy_x = np.random.random((1, *TrainingConfig.IMAGE_SIZE, 3))
    dummy_y = np.random.random((1, TrainingConfig.NUM_CLASSES))
    
    # Train for 1 step to initialize weights
    model.fit(dummy_x, dummy_y, epochs=1, verbose=0)
    
    # Save model
    os.makedirs(os.path.dirname(TrainingConfig.MODEL_SAVE_PATH), exist_ok=True)
    model.save(TrainingConfig.MODEL_SAVE_PATH)
    
    logger.info(f"Demo model saved to {TrainingConfig.MODEL_SAVE_PATH}")
    return model

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Train urban issue classifier')
    parser.add_argument('--create-structure', action='store_true', 
                       help='Create dataset directory structure')
    parser.add_argument('--demo', action='store_true',
                       help='Create demo model for testing')
    parser.add_argument('--simple', action='store_true',
                       help='Use simple CNN instead of transfer learning')
    
    args = parser.parse_args()
    
    if args.create_structure:
        create_dataset_structure()
    elif args.demo:
        create_demo_model_with_weights()
    else:
        train_model(use_transfer_learning=not args.simple)
