const mongoose = require('mongoose');

const issueSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Issue title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Issue description is required'],
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  category: {
    type: String,
    required: [true, 'Issue category is required'],
    enum: {
      values: ['pothole', 'garbage', 'water_leak', 'broken_light', 'damaged_road', 'other'],
      message: 'Category must be one of: pothole, garbage, water_leak, broken_light, damaged_road, other'
    }
  },
  aiClassification: {
    category: {
      type: String,
      enum: ['pothole', 'garbage', 'water_leak', 'broken_light', 'damaged_road', 'other']
    },
    confidence: {
      type: Number,
      min: 0,
      max: 1
    },
    processedAt: Date
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      required: true,
      default: 'Point'
    },
    coordinates: {
      type: [Number],
      required: [true, 'Location coordinates are required'],
      validate: {
        validator: function(coords) {
          return coords.length === 2 && 
                 coords[0] >= -180 && coords[0] <= 180 && // longitude
                 coords[1] >= -90 && coords[1] <= 90;     // latitude
        },
        message: 'Invalid coordinates format'
      }
    }
  },
  address: {
    street: String,
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true
    },
    state: {
      type: String,
      required: [true, 'State is required'],
      trim: true
    },
    pincode: {
      type: String,
      trim: true,
      match: [/^\d{6}$/, 'Please enter a valid 6-digit pincode']
    },
    country: {
      type: String,
      default: 'India',
      trim: true
    }
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    filename: String,
    size: Number,
    mimetype: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  reportedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Reporter information is required']
  },
  status: {
    type: String,
    enum: {
      values: ['open', 'in_progress', 'resolved', 'closed', 'duplicate'],
      message: 'Status must be one of: open, in_progress, resolved, closed, duplicate'
    },
    default: 'open'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  votes: {
    upvotes: {
      type: Number,
      default: 0
    },
    downvotes: {
      type: Number,
      default: 0
    },
    score: {
      type: Number,
      default: 0
    }
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  resolvedAt: Date,
  resolvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  resolutionNotes: String,
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  isVerified: {
    type: Boolean,
    default: false
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  verifiedAt: Date,
  duplicateOf: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Issue'
  },
  relatedIssues: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Issue'
  }],
  metadata: {
    deviceInfo: String,
    userAgent: String,
    ipAddress: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create geospatial index for location-based queries
issueSchema.index({ location: '2dsphere' });

// Compound indexes for better query performance
issueSchema.index({ status: 1, createdAt: -1 });
issueSchema.index({ category: 1, status: 1 });
issueSchema.index({ reportedBy: 1, createdAt: -1 });
issueSchema.index({ 'address.city': 1, status: 1 });
issueSchema.index({ 'votes.score': -1 });

// Virtual for vote score calculation
issueSchema.virtual('voteScore').get(function() {
  return this.votes.upvotes - this.votes.downvotes;
});

// Virtual for age in days
issueSchema.virtual('ageInDays').get(function() {
  return Math.floor((Date.now() - this.createdAt) / (1000 * 60 * 60 * 24));
});

// Pre-save middleware to update vote score
issueSchema.pre('save', function(next) {
  this.votes.score = this.votes.upvotes - this.votes.downvotes;
  next();
});

// Static method to find nearby issues
issueSchema.statics.findNearby = function(longitude, latitude, maxDistance = 1000) {
  return this.find({
    location: {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistance
      }
    }
  });
};

// Static method to get issues by status
issueSchema.statics.findByStatus = function(status) {
  return this.find({ status }).populate('reportedBy', 'username fullName');
};

// Instance method to mark as resolved
issueSchema.methods.markAsResolved = function(resolvedBy, notes) {
  this.status = 'resolved';
  this.resolvedAt = new Date();
  this.resolvedBy = resolvedBy;
  this.resolutionNotes = notes;
  return this.save();
};

// Instance method to add vote
issueSchema.methods.addVote = function(voteType) {
  if (voteType === 'up') {
    this.votes.upvotes += 1;
  } else if (voteType === 'down') {
    this.votes.downvotes += 1;
  }
  return this.save();
};

module.exports = mongoose.model('Issue', issueSchema);
