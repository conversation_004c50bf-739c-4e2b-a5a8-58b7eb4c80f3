const mongoose = require('mongoose');

const voteSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required for voting']
  },
  issue: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Issue',
    required: [true, 'Issue is required for voting']
  },
  voteType: {
    type: String,
    enum: {
      values: ['upvote', 'downvote'],
      message: 'Vote type must be either upvote or downvote'
    },
    required: [true, 'Vote type is required']
  },
  weight: {
    type: Number,
    default: 1,
    min: 0.1,
    max: 5.0
  },
  reason: {
    type: String,
    trim: true,
    maxlength: [200, 'Reason cannot exceed 200 characters']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  metadata: {
    ipAddress: String,
    userAgent: String,
    deviceInfo: String
  }
}, {
  timestamps: true
});

// Compound index to ensure one vote per user per issue
voteSchema.index({ user: 1, issue: 1 }, { unique: true });

// Index for better query performance
voteSchema.index({ issue: 1, voteType: 1 });
voteSchema.index({ user: 1, createdAt: -1 });
voteSchema.index({ isActive: 1 });

// Pre-save middleware to calculate vote weight based on user credibility
voteSchema.pre('save', async function(next) {
  if (this.isNew) {
    try {
      const User = mongoose.model('User');
      const user = await User.findById(this.user);
      
      if (user) {
        // Calculate weight based on credibility score (100-1000 -> 0.5-2.0)
        this.weight = Math.max(0.5, Math.min(2.0, user.credibilityScore / 500));
      }
    } catch (error) {
      console.error('Error calculating vote weight:', error);
    }
  }
  next();
});

// Post-save middleware to update issue vote counts
voteSchema.post('save', async function(doc) {
  try {
    const Issue = mongoose.model('Issue');
    const issue = await Issue.findById(doc.issue);
    
    if (issue) {
      // Recalculate vote counts
      const votes = await mongoose.model('Vote').find({ 
        issue: doc.issue, 
        isActive: true 
      });
      
      let upvotes = 0;
      let downvotes = 0;
      
      votes.forEach(vote => {
        if (vote.voteType === 'upvote') {
          upvotes += vote.weight;
        } else {
          downvotes += vote.weight;
        }
      });
      
      issue.votes.upvotes = Math.round(upvotes * 10) / 10; // Round to 1 decimal
      issue.votes.downvotes = Math.round(downvotes * 10) / 10;
      issue.votes.score = Math.round((upvotes - downvotes) * 10) / 10;
      
      await issue.save();
    }
  } catch (error) {
    console.error('Error updating issue vote counts:', error);
  }
});

// Post-remove middleware to update issue vote counts when vote is deleted
voteSchema.post('findOneAndDelete', async function(doc) {
  if (doc) {
    try {
      const Issue = mongoose.model('Issue');
      const issue = await Issue.findById(doc.issue);
      
      if (issue) {
        // Recalculate vote counts after deletion
        const votes = await mongoose.model('Vote').find({ 
          issue: doc.issue, 
          isActive: true 
        });
        
        let upvotes = 0;
        let downvotes = 0;
        
        votes.forEach(vote => {
          if (vote.voteType === 'upvote') {
            upvotes += vote.weight;
          } else {
            downvotes += vote.weight;
          }
        });
        
        issue.votes.upvotes = Math.round(upvotes * 10) / 10;
        issue.votes.downvotes = Math.round(downvotes * 10) / 10;
        issue.votes.score = Math.round((upvotes - downvotes) * 10) / 10;
        
        await issue.save();
      }
    } catch (error) {
      console.error('Error updating issue vote counts after deletion:', error);
    }
  }
});

// Static method to get user's vote on an issue
voteSchema.statics.getUserVote = function(userId, issueId) {
  return this.findOne({ 
    user: userId, 
    issue: issueId, 
    isActive: true 
  });
};

// Static method to get vote statistics for an issue
voteSchema.statics.getIssueVoteStats = function(issueId) {
  return this.aggregate([
    { $match: { issue: mongoose.Types.ObjectId(issueId), isActive: true } },
    {
      $group: {
        _id: '$voteType',
        count: { $sum: 1 },
        totalWeight: { $sum: '$weight' }
      }
    }
  ]);
};

// Static method to get user's voting history
voteSchema.statics.getUserVotingHistory = function(userId, limit = 50) {
  return this.find({ user: userId, isActive: true })
    .populate('issue', 'title category status createdAt')
    .sort({ createdAt: -1 })
    .limit(limit);
};

// Instance method to toggle vote
voteSchema.methods.toggle = function() {
  this.voteType = this.voteType === 'upvote' ? 'downvote' : 'upvote';
  return this.save();
};

// Instance method to deactivate vote
voteSchema.methods.deactivate = function() {
  this.isActive = false;
  return this.save();
};

module.exports = mongoose.model('Vote', voteSchema);
