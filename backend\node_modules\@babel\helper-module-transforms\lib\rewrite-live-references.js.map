{"version": 3, "names": ["_core", "require", "isInType", "path", "parent", "type", "parentPath", "exportKind", "isStatement", "isExpression", "rewriteLiveReferences", "programPath", "metadata", "wrapReference", "imported", "Map", "exported", "requeueInParent", "requeue", "source", "data", "localName", "importName", "imports", "set", "importsNamespace", "local", "exportMeta", "get", "push", "names", "rewriteBindingInitVisitorState", "scope", "traverse", "rewriteBindingInitVisitor", "rewriteReferencesVisitorState", "seen", "WeakSet", "buildImportReference", "identNode", "meta", "referenced", "wrap", "_wrapReference", "namespace", "t", "identifier", "name", "_wrapReference2", "interop", "computed", "stringSpecifiers", "has", "memberExpression", "stringLiteral", "rewriteReferencesVisitor", "<PERSON><PERSON>", "skip", "ClassDeclaration", "id", "node", "Error", "exportNames", "length", "statement", "expressionStatement", "buildBindingExportAssignmentExpression", "_blockHoist", "insertAfter", "VariableDeclaration", "isVar", "kind", "decl", "init", "isIdentifier", "isArrowFunctionExpression", "isFunctionExpression", "isClassExpression", "buildUndefinedNode", "Object", "keys", "getOuterBindingIdentifiers", "localExpr", "exportsObjectName", "exportName", "currentScope", "hasOwnBinding", "rename", "reduce", "expr", "assignmentExpression", "buildImportThrow", "template", "expression", "ast", "ReferencedIdentifier", "add", "importData", "buildCodeFrameError", "localBinding", "getBinding", "rootBinding", "ref", "loc", "isCallExpression", "callee", "isOptionalCallExpression", "isTaggedTemplateExpression", "tag", "isMemberExpression", "replaceWith", "sequenceExpression", "numericLiteral", "isJSXIdentifier", "object", "property", "jsxMemberExpression", "jsxIdentifier", "UpdateExpression", "arg", "update", "exportedNames", "operator", "prefix", "cloneNode", "generateDeclaredUidIdentifier", "AssignmentExpression", "exit", "left", "assignment", "right", "newExpr", "logicalExpression", "slice", "binaryExpression", "ids", "programScopeIds", "filter", "find", "items", "for<PERSON>ach", "isExpressionStatement", "ForXStatement", "programScope", "isVariableDeclaration", "didTransformExport", "importConstViolationName", "loopBodyScope", "ensureBlock", "bodyPath", "newLoopId", "generateUidIdentifierBasedOnNode", "variableDeclaration", "variableDeclarator", "registerDeclaration", "unshiftContainer"], "sources": ["../src/rewrite-live-references.ts"], "sourcesContent": ["import { template, types as t } from \"@babel/core\";\nimport type { Node<PERSON><PERSON>, Visitor, Scope } from \"@babel/core\";\n\nimport type { ModuleMetadata } from \"./normalize-and-load-metadata.ts\";\n\ninterface RewriteReferencesVisitorState {\n  exported: Map<any, any>;\n  metadata: ModuleMetadata;\n  requeueInParent: (path: NodePath) => void;\n  scope: Scope;\n  imported: Map<any, any>;\n  buildImportReference: (\n    [source, importName, localName]: readonly [string, string, string],\n    identNode: t.Identifier | t.CallExpression | t.JSXIdentifier,\n  ) => any;\n  seen: WeakSet<object>;\n}\n\ninterface RewriteBindingInitVisitorState {\n  exported: Map<any, any>;\n  metadata: ModuleMetadata;\n  requeueInParent: (path: NodePath) => void;\n  scope: Scope;\n}\n\nfunction isInType(path: NodePath) {\n  do {\n    switch (path.parent.type) {\n      case \"TSTypeAnnotation\":\n      case \"TSTypeAliasDeclaration\":\n      case \"TSTypeReference\":\n      case \"TypeAnnotation\":\n      case \"TypeAlias\":\n        return true;\n      case \"ExportSpecifier\":\n        return (\n          (\n            path.parentPath.parent as\n              | t.ExportDefaultDeclaration\n              | t.ExportNamedDeclaration\n          ).exportKind === \"type\"\n        );\n      default:\n        if (path.parentPath.isStatement() || path.parentPath.isExpression()) {\n          return false;\n        }\n    }\n  } while ((path = path.parentPath));\n}\n\nexport default function rewriteLiveReferences(\n  programPath: NodePath<t.Program>,\n  metadata: ModuleMetadata,\n  wrapReference: (ref: t.Expression, payload: unknown) => null | t.Expression,\n) {\n  const imported = new Map();\n  const exported = new Map();\n  const requeueInParent = (path: NodePath) => {\n    // Manually re-queue `exports.default =` expressions so that the ES3\n    // transform has an opportunity to convert them. Ideally this would\n    // happen automatically from the replaceWith above. See #4140 for\n    // more info.\n    programPath.requeue(path);\n  };\n\n  for (const [source, data] of metadata.source) {\n    for (const [localName, importName] of data.imports) {\n      imported.set(localName, [source, importName, null]);\n    }\n    for (const localName of data.importsNamespace) {\n      imported.set(localName, [source, null, localName]);\n    }\n  }\n\n  for (const [local, data] of metadata.local) {\n    let exportMeta = exported.get(local);\n    if (!exportMeta) {\n      exportMeta = [];\n      exported.set(local, exportMeta);\n    }\n\n    exportMeta.push(...data.names);\n  }\n\n  // Rewrite initialization of bindings to update exports.\n  const rewriteBindingInitVisitorState: RewriteBindingInitVisitorState = {\n    metadata,\n    requeueInParent,\n    scope: programPath.scope,\n    exported, // local name => exported name list\n  };\n  programPath.traverse(\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    rewriteBindingInitVisitor,\n    rewriteBindingInitVisitorState,\n  );\n\n  // Rewrite reads/writes from imports and exports to have the correct behavior.\n  const rewriteReferencesVisitorState: RewriteReferencesVisitorState = {\n    seen: new WeakSet(),\n    metadata,\n    requeueInParent,\n    scope: programPath.scope,\n    imported, // local / import\n    exported, // local name => exported name list\n    buildImportReference([source, importName, localName], identNode) {\n      const meta = metadata.source.get(source);\n      meta.referenced = true;\n\n      if (localName) {\n        if (meta.wrap) {\n          // @ts-expect-error Fixme: we should handle the case when identNode is a JSXIdentifier\n          identNode = wrapReference(identNode, meta.wrap) ?? identNode;\n        }\n        return identNode;\n      }\n\n      let namespace: t.Expression = t.identifier(meta.name);\n      if (meta.wrap) {\n        namespace = wrapReference(namespace, meta.wrap) ?? namespace;\n      }\n\n      if (importName === \"default\" && meta.interop === \"node-default\") {\n        return namespace;\n      }\n\n      const computed = metadata.stringSpecifiers.has(importName);\n\n      return t.memberExpression(\n        namespace,\n        computed ? t.stringLiteral(importName) : t.identifier(importName),\n        computed,\n      );\n    },\n  };\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  programPath.traverse(rewriteReferencesVisitor, rewriteReferencesVisitorState);\n}\n\n/**\n * A visitor to inject export update statements during binding initialization.\n */\nconst rewriteBindingInitVisitor: Visitor<RewriteBindingInitVisitorState> = {\n  Scope(path) {\n    path.skip();\n  },\n  ClassDeclaration(path) {\n    const { requeueInParent, exported, metadata } = this;\n\n    const { id } = path.node;\n    if (!id) throw new Error(\"Expected class to have a name\");\n    const localName = id.name;\n\n    const exportNames = exported.get(localName) || [];\n    if (exportNames.length > 0) {\n      const statement = t.expressionStatement(\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        buildBindingExportAssignmentExpression(\n          metadata,\n          exportNames,\n          t.identifier(localName),\n          path.scope,\n        ),\n      );\n      // @ts-expect-error todo(flow->ts): avoid mutations\n      statement._blockHoist = path.node._blockHoist;\n\n      requeueInParent(path.insertAfter(statement)[0]);\n    }\n  },\n  VariableDeclaration(path) {\n    const { requeueInParent, exported, metadata } = this;\n\n    const isVar = path.node.kind === \"var\";\n\n    for (const decl of path.get(\"declarations\")) {\n      const { id } = decl.node;\n      let { init } = decl.node;\n      if (\n        t.isIdentifier(id) &&\n        exported.has(id.name) &&\n        !t.isArrowFunctionExpression(init) &&\n        (!t.isFunctionExpression(init) || init.id) &&\n        (!t.isClassExpression(init) || init.id)\n      ) {\n        if (!init) {\n          if (isVar) {\n            // This variable might have already been assigned to, and the\n            // uninitalized declaration doesn't set it to `undefined` and does\n            // not updated the exported value.\n            continue;\n          } else {\n            init = path.scope.buildUndefinedNode();\n          }\n        }\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        decl.node.init = buildBindingExportAssignmentExpression(\n          metadata,\n          exported.get(id.name),\n          init,\n          path.scope,\n        );\n        requeueInParent(decl.get(\"init\"));\n      } else {\n        for (const localName of Object.keys(\n          decl.getOuterBindingIdentifiers(),\n        )) {\n          if (exported.has(localName)) {\n            const statement = t.expressionStatement(\n              // eslint-disable-next-line @typescript-eslint/no-use-before-define\n              buildBindingExportAssignmentExpression(\n                metadata,\n                exported.get(localName),\n                t.identifier(localName),\n                path.scope,\n              ),\n            );\n            // @ts-expect-error todo(flow->ts): avoid mutations\n            statement._blockHoist = path.node._blockHoist;\n\n            requeueInParent(path.insertAfter(statement)[0]);\n          }\n        }\n      }\n    }\n  },\n};\n\nconst buildBindingExportAssignmentExpression = (\n  metadata: ModuleMetadata,\n  exportNames: string[],\n  localExpr: t.Expression,\n  scope: Scope,\n) => {\n  const exportsObjectName = metadata.exportName;\n  for (\n    let currentScope = scope;\n    currentScope != null;\n    currentScope = currentScope.parent\n  ) {\n    if (currentScope.hasOwnBinding(exportsObjectName)) {\n      currentScope.rename(exportsObjectName);\n    }\n  }\n  return (exportNames || []).reduce((expr, exportName) => {\n    // class Foo {} export { Foo, Foo as Bar };\n    // as\n    // class Foo {} exports.Foo = exports.Bar = Foo;\n    const { stringSpecifiers } = metadata;\n    const computed = stringSpecifiers.has(exportName);\n    return t.assignmentExpression(\n      \"=\",\n      t.memberExpression(\n        t.identifier(exportsObjectName),\n        computed ? t.stringLiteral(exportName) : t.identifier(exportName),\n        /* computed */ computed,\n      ),\n      expr,\n    );\n  }, localExpr);\n};\n\nconst buildImportThrow = (localName: string) => {\n  return template.expression.ast`\n    (function() {\n      throw new Error('\"' + '${localName}' + '\" is read-only.');\n    })()\n  `;\n};\n\nconst rewriteReferencesVisitor: Visitor<RewriteReferencesVisitorState> = {\n  ReferencedIdentifier(path) {\n    const { seen, buildImportReference, scope, imported, requeueInParent } =\n      this;\n    if (seen.has(path.node)) return;\n    seen.add(path.node);\n\n    const localName = path.node.name;\n\n    const importData = imported.get(localName);\n    if (importData) {\n      if (isInType(path)) {\n        throw path.buildCodeFrameError(\n          `Cannot transform the imported binding \"${localName}\" since it's also used in a type annotation. ` +\n            `Please strip type annotations using @babel/preset-typescript or @babel/preset-flow.`,\n        );\n      }\n\n      const localBinding = path.scope.getBinding(localName);\n      const rootBinding = scope.getBinding(localName);\n\n      // redeclared in this scope\n      if (rootBinding !== localBinding) return;\n\n      const ref = buildImportReference(importData, path.node);\n\n      // Preserve the binding location so that sourcemaps are nicer.\n      ref.loc = path.node.loc;\n\n      if (\n        (path.parentPath.isCallExpression({ callee: path.node }) ||\n          path.parentPath.isOptionalCallExpression({ callee: path.node }) ||\n          path.parentPath.isTaggedTemplateExpression({ tag: path.node })) &&\n        t.isMemberExpression(ref)\n      ) {\n        path.replaceWith(t.sequenceExpression([t.numericLiteral(0), ref]));\n      } else if (path.isJSXIdentifier() && t.isMemberExpression(ref)) {\n        const { object, property } = ref;\n        path.replaceWith(\n          t.jsxMemberExpression(\n            // @ts-expect-error todo(flow->ts): possible bug `object` might not have a name\n            t.jsxIdentifier(object.name),\n            // @ts-expect-error todo(flow->ts): possible bug `property` might not have a name\n            t.jsxIdentifier(property.name),\n          ),\n        );\n      } else {\n        path.replaceWith(ref);\n      }\n\n      requeueInParent(path);\n\n      // The path could have been replaced with an identifier that would\n      // otherwise be re-visited, so we skip processing its children.\n      path.skip();\n    }\n  },\n\n  UpdateExpression(path) {\n    const {\n      scope,\n      seen,\n      imported,\n      exported,\n      requeueInParent,\n      buildImportReference,\n    } = this;\n\n    if (seen.has(path.node)) return;\n\n    seen.add(path.node);\n\n    const arg = path.get(\"argument\");\n\n    // No change needed\n    if (arg.isMemberExpression()) return;\n\n    const update = path.node;\n\n    if (arg.isIdentifier()) {\n      const localName = arg.node.name;\n\n      // redeclared in this scope\n      if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n        return;\n      }\n\n      const exportedNames = exported.get(localName);\n      const importData = imported.get(localName);\n\n      if (exportedNames?.length > 0 || importData) {\n        if (importData) {\n          path.replaceWith(\n            t.assignmentExpression(\n              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n              (update.operator[0] + \"=\") as t.AssignmentExpression[\"operator\"],\n              buildImportReference(importData, arg.node),\n              buildImportThrow(localName),\n            ),\n          );\n        } else if (update.prefix) {\n          // ++foo\n          // =>   exports.foo = ++foo\n          path.replaceWith(\n            buildBindingExportAssignmentExpression(\n              this.metadata,\n              exportedNames,\n              t.cloneNode(update),\n              path.scope,\n            ),\n          );\n        } else {\n          // foo++\n          // =>   (ref = i++, exports.i = i, ref)\n          const ref = scope.generateDeclaredUidIdentifier(localName);\n\n          path.replaceWith(\n            t.sequenceExpression([\n              t.assignmentExpression(\n                \"=\",\n                t.cloneNode(ref),\n                t.cloneNode(update),\n              ),\n              buildBindingExportAssignmentExpression(\n                this.metadata,\n                exportedNames,\n                t.identifier(localName),\n                path.scope,\n              ),\n              t.cloneNode(ref),\n            ]),\n          );\n        }\n      }\n    }\n\n    requeueInParent(path);\n    path.skip();\n  },\n\n  AssignmentExpression: {\n    exit(path) {\n      const {\n        scope,\n        seen,\n        imported,\n        exported,\n        requeueInParent,\n        buildImportReference,\n      } = this;\n\n      if (seen.has(path.node)) return;\n      seen.add(path.node);\n\n      const left = path.get(\"left\");\n\n      // No change needed\n      if (left.isMemberExpression()) return;\n\n      if (left.isIdentifier()) {\n        // Simple update-assign foo += 1; export { foo };\n        // =>   exports.foo =  (foo += 1);\n        const localName = left.node.name;\n\n        // redeclared in this scope\n        if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n          return;\n        }\n\n        const exportedNames = exported.get(localName);\n        const importData = imported.get(localName);\n        if (exportedNames?.length > 0 || importData) {\n          const assignment = path.node;\n\n          if (importData) {\n            assignment.left = buildImportReference(importData, left.node);\n\n            assignment.right = t.sequenceExpression([\n              assignment.right,\n              buildImportThrow(localName),\n            ]);\n          }\n\n          const { operator } = assignment;\n          let newExpr;\n          if (operator === \"=\") {\n            newExpr = assignment;\n          } else if (\n            operator === \"&&=\" ||\n            operator === \"||=\" ||\n            operator === \"??=\"\n          ) {\n            newExpr = t.assignmentExpression(\n              \"=\",\n              assignment.left,\n              t.logicalExpression(\n                operator.slice(0, -1) as t.LogicalExpression[\"operator\"],\n                t.cloneNode(assignment.left) as t.Expression,\n                assignment.right,\n              ),\n            );\n          } else {\n            newExpr = t.assignmentExpression(\n              \"=\",\n              assignment.left,\n              t.binaryExpression(\n                operator.slice(0, -1) as t.BinaryExpression[\"operator\"],\n                t.cloneNode(assignment.left) as t.Expression,\n                assignment.right,\n              ),\n            );\n          }\n\n          path.replaceWith(\n            buildBindingExportAssignmentExpression(\n              this.metadata,\n              exportedNames,\n              newExpr,\n              path.scope,\n            ),\n          );\n\n          requeueInParent(path);\n\n          path.skip();\n        }\n      } else {\n        const ids = left.getOuterBindingIdentifiers();\n        const programScopeIds = Object.keys(ids).filter(\n          localName =>\n            scope.getBinding(localName) === path.scope.getBinding(localName),\n        );\n        const id = programScopeIds.find(localName => imported.has(localName));\n\n        if (id) {\n          path.node.right = t.sequenceExpression([\n            path.node.right,\n            buildImportThrow(id),\n          ]);\n        }\n\n        // Complex ({a, b, c} = {}); export { a, c };\n        // =>   ({a, b, c} = {}), (exports.a = a, exports.c = c);\n        const items: t.Expression[] = [];\n        programScopeIds.forEach(localName => {\n          const exportedNames = exported.get(localName) || [];\n          if (exportedNames.length > 0) {\n            items.push(\n              buildBindingExportAssignmentExpression(\n                this.metadata,\n                exportedNames,\n                t.identifier(localName),\n                path.scope,\n              ),\n            );\n          }\n        });\n\n        if (items.length > 0) {\n          let node: t.Node = t.sequenceExpression(items);\n          if (path.parentPath.isExpressionStatement()) {\n            node = t.expressionStatement(node);\n            // @ts-expect-error todo(flow->ts): avoid mutations\n            node._blockHoist = path.parentPath.node._blockHoist;\n          }\n\n          const statement = path.insertAfter(node)[0];\n          requeueInParent(statement);\n        }\n      }\n    },\n  },\n  ForXStatement(path) {\n    const { scope, node } = path;\n    const { left } = node;\n    const { exported, imported, scope: programScope } = this;\n\n    if (!t.isVariableDeclaration(left)) {\n      let didTransformExport = false,\n        importConstViolationName;\n      const loopBodyScope = path.get(\"body\").scope;\n      for (const name of Object.keys(t.getOuterBindingIdentifiers(left))) {\n        if (programScope.getBinding(name) === scope.getBinding(name)) {\n          if (exported.has(name)) {\n            didTransformExport = true;\n            if (loopBodyScope.hasOwnBinding(name)) {\n              loopBodyScope.rename(name);\n            }\n          }\n          if (imported.has(name) && !importConstViolationName) {\n            importConstViolationName = name;\n          }\n        }\n      }\n      if (!didTransformExport && !importConstViolationName) {\n        return;\n      }\n\n      path.ensureBlock();\n      const bodyPath = path.get(\"body\") as NodePath<t.BlockStatement>;\n\n      const newLoopId = scope.generateUidIdentifierBasedOnNode(left);\n      path\n        .get(\"left\")\n        .replaceWith(\n          t.variableDeclaration(\"let\", [\n            t.variableDeclarator(t.cloneNode(newLoopId)),\n          ]),\n        );\n      scope.registerDeclaration(path.get(\"left\"));\n\n      if (didTransformExport) {\n        bodyPath.unshiftContainer(\n          \"body\",\n          t.expressionStatement(t.assignmentExpression(\"=\", left, newLoopId)),\n        );\n      }\n      if (importConstViolationName) {\n        bodyPath.unshiftContainer(\n          \"body\",\n          t.expressionStatement(buildImportThrow(importConstViolationName)),\n        );\n      }\n    }\n  },\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAyBA,SAASC,QAAQA,CAACC,IAAc,EAAE;EAChC,GAAG;IACD,QAAQA,IAAI,CAACC,MAAM,CAACC,IAAI;MACtB,KAAK,kBAAkB;MACvB,KAAK,wBAAwB;MAC7B,KAAK,iBAAiB;MACtB,KAAK,gBAAgB;MACrB,KAAK,WAAW;QACd,OAAO,IAAI;MACb,KAAK,iBAAiB;QACpB,OAEIF,IAAI,CAACG,UAAU,CAACF,MAAM,CAGtBG,UAAU,KAAK,MAAM;MAE3B;QACE,IAAIJ,IAAI,CAACG,UAAU,CAACE,WAAW,CAAC,CAAC,IAAIL,IAAI,CAACG,UAAU,CAACG,YAAY,CAAC,CAAC,EAAE;UACnE,OAAO,KAAK;QACd;IACJ;EACF,CAAC,QAASN,IAAI,GAAGA,IAAI,CAACG,UAAU;AAClC;AAEe,SAASI,qBAAqBA,CAC3CC,WAAgC,EAChCC,QAAwB,EACxBC,aAA2E,EAC3E;EACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC1B,MAAMC,QAAQ,GAAG,IAAID,GAAG,CAAC,CAAC;EAC1B,MAAME,eAAe,GAAId,IAAc,IAAK;IAK1CQ,WAAW,CAACO,OAAO,CAACf,IAAI,CAAC;EAC3B,CAAC;EAED,KAAK,MAAM,CAACgB,MAAM,EAAEC,IAAI,CAAC,IAAIR,QAAQ,CAACO,MAAM,EAAE;IAC5C,KAAK,MAAM,CAACE,SAAS,EAAEC,UAAU,CAAC,IAAIF,IAAI,CAACG,OAAO,EAAE;MAClDT,QAAQ,CAACU,GAAG,CAACH,SAAS,EAAE,CAACF,MAAM,EAAEG,UAAU,EAAE,IAAI,CAAC,CAAC;IACrD;IACA,KAAK,MAAMD,SAAS,IAAID,IAAI,CAACK,gBAAgB,EAAE;MAC7CX,QAAQ,CAACU,GAAG,CAACH,SAAS,EAAE,CAACF,MAAM,EAAE,IAAI,EAAEE,SAAS,CAAC,CAAC;IACpD;EACF;EAEA,KAAK,MAAM,CAACK,KAAK,EAAEN,IAAI,CAAC,IAAIR,QAAQ,CAACc,KAAK,EAAE;IAC1C,IAAIC,UAAU,GAAGX,QAAQ,CAACY,GAAG,CAACF,KAAK,CAAC;IACpC,IAAI,CAACC,UAAU,EAAE;MACfA,UAAU,GAAG,EAAE;MACfX,QAAQ,CAACQ,GAAG,CAACE,KAAK,EAAEC,UAAU,CAAC;IACjC;IAEAA,UAAU,CAACE,IAAI,CAAC,GAAGT,IAAI,CAACU,KAAK,CAAC;EAChC;EAGA,MAAMC,8BAA8D,GAAG;IACrEnB,QAAQ;IACRK,eAAe;IACfe,KAAK,EAAErB,WAAW,CAACqB,KAAK;IACxBhB;EACF,CAAC;EACDL,WAAW,CAACsB,QAAQ,CAElBC,yBAAyB,EACzBH,8BACF,CAAC;EAGD,MAAMI,6BAA4D,GAAG;IACnEC,IAAI,EAAE,IAAIC,OAAO,CAAC,CAAC;IACnBzB,QAAQ;IACRK,eAAe;IACfe,KAAK,EAAErB,WAAW,CAACqB,KAAK;IACxBlB,QAAQ;IACRE,QAAQ;IACRsB,oBAAoBA,CAAC,CAACnB,MAAM,EAAEG,UAAU,EAAED,SAAS,CAAC,EAAEkB,SAAS,EAAE;MAC/D,MAAMC,IAAI,GAAG5B,QAAQ,CAACO,MAAM,CAACS,GAAG,CAACT,MAAM,CAAC;MACxCqB,IAAI,CAACC,UAAU,GAAG,IAAI;MAEtB,IAAIpB,SAAS,EAAE;QACb,IAAImB,IAAI,CAACE,IAAI,EAAE;UAAA,IAAAC,cAAA;UAEbJ,SAAS,IAAAI,cAAA,GAAG9B,aAAa,CAAC0B,SAAS,EAAEC,IAAI,CAACE,IAAI,CAAC,YAAAC,cAAA,GAAIJ,SAAS;QAC9D;QACA,OAAOA,SAAS;MAClB;MAEA,IAAIK,SAAuB,GAAGC,WAAC,CAACC,UAAU,CAACN,IAAI,CAACO,IAAI,CAAC;MACrD,IAAIP,IAAI,CAACE,IAAI,EAAE;QAAA,IAAAM,eAAA;QACbJ,SAAS,IAAAI,eAAA,GAAGnC,aAAa,CAAC+B,SAAS,EAAEJ,IAAI,CAACE,IAAI,CAAC,YAAAM,eAAA,GAAIJ,SAAS;MAC9D;MAEA,IAAItB,UAAU,KAAK,SAAS,IAAIkB,IAAI,CAACS,OAAO,KAAK,cAAc,EAAE;QAC/D,OAAOL,SAAS;MAClB;MAEA,MAAMM,QAAQ,GAAGtC,QAAQ,CAACuC,gBAAgB,CAACC,GAAG,CAAC9B,UAAU,CAAC;MAE1D,OAAOuB,WAAC,CAACQ,gBAAgB,CACvBT,SAAS,EACTM,QAAQ,GAAGL,WAAC,CAACS,aAAa,CAAChC,UAAU,CAAC,GAAGuB,WAAC,CAACC,UAAU,CAACxB,UAAU,CAAC,EACjE4B,QACF,CAAC;IACH;EACF,CAAC;EAEDvC,WAAW,CAACsB,QAAQ,CAACsB,wBAAwB,EAAEpB,6BAA6B,CAAC;AAC/E;AAKA,MAAMD,yBAAkE,GAAG;EACzEsB,KAAKA,CAACrD,IAAI,EAAE;IACVA,IAAI,CAACsD,IAAI,CAAC,CAAC;EACb,CAAC;EACDC,gBAAgBA,CAACvD,IAAI,EAAE;IACrB,MAAM;MAAEc,eAAe;MAAED,QAAQ;MAAEJ;IAAS,CAAC,GAAG,IAAI;IAEpD,MAAM;MAAE+C;IAAG,CAAC,GAAGxD,IAAI,CAACyD,IAAI;IACxB,IAAI,CAACD,EAAE,EAAE,MAAM,IAAIE,KAAK,CAAC,+BAA+B,CAAC;IACzD,MAAMxC,SAAS,GAAGsC,EAAE,CAACZ,IAAI;IAEzB,MAAMe,WAAW,GAAG9C,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;IACjD,IAAIyC,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMC,SAAS,GAAGnB,WAAC,CAACoB,mBAAmB,CAErCC,sCAAsC,CACpCtD,QAAQ,EACRkD,WAAW,EACXjB,WAAC,CAACC,UAAU,CAACzB,SAAS,CAAC,EACvBlB,IAAI,CAAC6B,KACP,CACF,CAAC;MAEDgC,SAAS,CAACG,WAAW,GAAGhE,IAAI,CAACyD,IAAI,CAACO,WAAW;MAE7ClD,eAAe,CAACd,IAAI,CAACiE,WAAW,CAACJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EACDK,mBAAmBA,CAAClE,IAAI,EAAE;IACxB,MAAM;MAAEc,eAAe;MAAED,QAAQ;MAAEJ;IAAS,CAAC,GAAG,IAAI;IAEpD,MAAM0D,KAAK,GAAGnE,IAAI,CAACyD,IAAI,CAACW,IAAI,KAAK,KAAK;IAEtC,KAAK,MAAMC,IAAI,IAAIrE,IAAI,CAACyB,GAAG,CAAC,cAAc,CAAC,EAAE;MAC3C,MAAM;QAAE+B;MAAG,CAAC,GAAGa,IAAI,CAACZ,IAAI;MACxB,IAAI;QAAEa;MAAK,CAAC,GAAGD,IAAI,CAACZ,IAAI;MACxB,IACEf,WAAC,CAAC6B,YAAY,CAACf,EAAE,CAAC,IAClB3C,QAAQ,CAACoC,GAAG,CAACO,EAAE,CAACZ,IAAI,CAAC,IACrB,CAACF,WAAC,CAAC8B,yBAAyB,CAACF,IAAI,CAAC,KACjC,CAAC5B,WAAC,CAAC+B,oBAAoB,CAACH,IAAI,CAAC,IAAIA,IAAI,CAACd,EAAE,CAAC,KACzC,CAACd,WAAC,CAACgC,iBAAiB,CAACJ,IAAI,CAAC,IAAIA,IAAI,CAACd,EAAE,CAAC,EACvC;QACA,IAAI,CAACc,IAAI,EAAE;UACT,IAAIH,KAAK,EAAE;YAIT;UACF,CAAC,MAAM;YACLG,IAAI,GAAGtE,IAAI,CAAC6B,KAAK,CAAC8C,kBAAkB,CAAC,CAAC;UACxC;QACF;QAEAN,IAAI,CAACZ,IAAI,CAACa,IAAI,GAAGP,sCAAsC,CACrDtD,QAAQ,EACRI,QAAQ,CAACY,GAAG,CAAC+B,EAAE,CAACZ,IAAI,CAAC,EACrB0B,IAAI,EACJtE,IAAI,CAAC6B,KACP,CAAC;QACDf,eAAe,CAACuD,IAAI,CAAC5C,GAAG,CAAC,MAAM,CAAC,CAAC;MACnC,CAAC,MAAM;QACL,KAAK,MAAMP,SAAS,IAAI0D,MAAM,CAACC,IAAI,CACjCR,IAAI,CAACS,0BAA0B,CAAC,CAClC,CAAC,EAAE;UACD,IAAIjE,QAAQ,CAACoC,GAAG,CAAC/B,SAAS,CAAC,EAAE;YAC3B,MAAM2C,SAAS,GAAGnB,WAAC,CAACoB,mBAAmB,CAErCC,sCAAsC,CACpCtD,QAAQ,EACRI,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,EACvBwB,WAAC,CAACC,UAAU,CAACzB,SAAS,CAAC,EACvBlB,IAAI,CAAC6B,KACP,CACF,CAAC;YAEDgC,SAAS,CAACG,WAAW,GAAGhE,IAAI,CAACyD,IAAI,CAACO,WAAW;YAE7ClD,eAAe,CAACd,IAAI,CAACiE,WAAW,CAACJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD;QACF;MACF;IACF;EACF;AACF,CAAC;AAED,MAAME,sCAAsC,GAAGA,CAC7CtD,QAAwB,EACxBkD,WAAqB,EACrBoB,SAAuB,EACvBlD,KAAY,KACT;EACH,MAAMmD,iBAAiB,GAAGvE,QAAQ,CAACwE,UAAU;EAC7C,KACE,IAAIC,YAAY,GAAGrD,KAAK,EACxBqD,YAAY,IAAI,IAAI,EACpBA,YAAY,GAAGA,YAAY,CAACjF,MAAM,EAClC;IACA,IAAIiF,YAAY,CAACC,aAAa,CAACH,iBAAiB,CAAC,EAAE;MACjDE,YAAY,CAACE,MAAM,CAACJ,iBAAiB,CAAC;IACxC;EACF;EACA,OAAO,CAACrB,WAAW,IAAI,EAAE,EAAE0B,MAAM,CAAC,CAACC,IAAI,EAAEL,UAAU,KAAK;IAItD,MAAM;MAAEjC;IAAiB,CAAC,GAAGvC,QAAQ;IACrC,MAAMsC,QAAQ,GAAGC,gBAAgB,CAACC,GAAG,CAACgC,UAAU,CAAC;IACjD,OAAOvC,WAAC,CAAC6C,oBAAoB,CAC3B,GAAG,EACH7C,WAAC,CAACQ,gBAAgB,CAChBR,WAAC,CAACC,UAAU,CAACqC,iBAAiB,CAAC,EAC/BjC,QAAQ,GAAGL,WAAC,CAACS,aAAa,CAAC8B,UAAU,CAAC,GAAGvC,WAAC,CAACC,UAAU,CAACsC,UAAU,CAAC,EAClDlC,QACjB,CAAC,EACDuC,IACF,CAAC;EACH,CAAC,EAAEP,SAAS,CAAC;AACf,CAAC;AAED,MAAMS,gBAAgB,GAAItE,SAAiB,IAAK;EAC9C,OAAOuE,cAAQ,CAACC,UAAU,CAACC,GAAG;AAChC;AACA,+BAA+BzE,SAAS;AACxC;AACA,GAAG;AACH,CAAC;AAED,MAAMkC,wBAAgE,GAAG;EACvEwC,oBAAoBA,CAAC5F,IAAI,EAAE;IACzB,MAAM;MAAEiC,IAAI;MAAEE,oBAAoB;MAAEN,KAAK;MAAElB,QAAQ;MAAEG;IAAgB,CAAC,GACpE,IAAI;IACN,IAAImB,IAAI,CAACgB,GAAG,CAACjD,IAAI,CAACyD,IAAI,CAAC,EAAE;IACzBxB,IAAI,CAAC4D,GAAG,CAAC7F,IAAI,CAACyD,IAAI,CAAC;IAEnB,MAAMvC,SAAS,GAAGlB,IAAI,CAACyD,IAAI,CAACb,IAAI;IAEhC,MAAMkD,UAAU,GAAGnF,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;IAC1C,IAAI4E,UAAU,EAAE;MACd,IAAI/F,QAAQ,CAACC,IAAI,CAAC,EAAE;QAClB,MAAMA,IAAI,CAAC+F,mBAAmB,CAC5B,0CAA0C7E,SAAS,+CAA+C,GAChG,qFACJ,CAAC;MACH;MAEA,MAAM8E,YAAY,GAAGhG,IAAI,CAAC6B,KAAK,CAACoE,UAAU,CAAC/E,SAAS,CAAC;MACrD,MAAMgF,WAAW,GAAGrE,KAAK,CAACoE,UAAU,CAAC/E,SAAS,CAAC;MAG/C,IAAIgF,WAAW,KAAKF,YAAY,EAAE;MAElC,MAAMG,GAAG,GAAGhE,oBAAoB,CAAC2D,UAAU,EAAE9F,IAAI,CAACyD,IAAI,CAAC;MAGvD0C,GAAG,CAACC,GAAG,GAAGpG,IAAI,CAACyD,IAAI,CAAC2C,GAAG;MAEvB,IACE,CAACpG,IAAI,CAACG,UAAU,CAACkG,gBAAgB,CAAC;QAAEC,MAAM,EAAEtG,IAAI,CAACyD;MAAK,CAAC,CAAC,IACtDzD,IAAI,CAACG,UAAU,CAACoG,wBAAwB,CAAC;QAAED,MAAM,EAAEtG,IAAI,CAACyD;MAAK,CAAC,CAAC,IAC/DzD,IAAI,CAACG,UAAU,CAACqG,0BAA0B,CAAC;QAAEC,GAAG,EAAEzG,IAAI,CAACyD;MAAK,CAAC,CAAC,KAChEf,WAAC,CAACgE,kBAAkB,CAACP,GAAG,CAAC,EACzB;QACAnG,IAAI,CAAC2G,WAAW,CAACjE,WAAC,CAACkE,kBAAkB,CAAC,CAAClE,WAAC,CAACmE,cAAc,CAAC,CAAC,CAAC,EAAEV,GAAG,CAAC,CAAC,CAAC;MACpE,CAAC,MAAM,IAAInG,IAAI,CAAC8G,eAAe,CAAC,CAAC,IAAIpE,WAAC,CAACgE,kBAAkB,CAACP,GAAG,CAAC,EAAE;QAC9D,MAAM;UAAEY,MAAM;UAAEC;QAAS,CAAC,GAAGb,GAAG;QAChCnG,IAAI,CAAC2G,WAAW,CACdjE,WAAC,CAACuE,mBAAmB,CAEnBvE,WAAC,CAACwE,aAAa,CAACH,MAAM,CAACnE,IAAI,CAAC,EAE5BF,WAAC,CAACwE,aAAa,CAACF,QAAQ,CAACpE,IAAI,CAC/B,CACF,CAAC;MACH,CAAC,MAAM;QACL5C,IAAI,CAAC2G,WAAW,CAACR,GAAG,CAAC;MACvB;MAEArF,eAAe,CAACd,IAAI,CAAC;MAIrBA,IAAI,CAACsD,IAAI,CAAC,CAAC;IACb;EACF,CAAC;EAED6D,gBAAgBA,CAACnH,IAAI,EAAE;IACrB,MAAM;MACJ6B,KAAK;MACLI,IAAI;MACJtB,QAAQ;MACRE,QAAQ;MACRC,eAAe;MACfqB;IACF,CAAC,GAAG,IAAI;IAER,IAAIF,IAAI,CAACgB,GAAG,CAACjD,IAAI,CAACyD,IAAI,CAAC,EAAE;IAEzBxB,IAAI,CAAC4D,GAAG,CAAC7F,IAAI,CAACyD,IAAI,CAAC;IAEnB,MAAM2D,GAAG,GAAGpH,IAAI,CAACyB,GAAG,CAAC,UAAU,CAAC;IAGhC,IAAI2F,GAAG,CAACV,kBAAkB,CAAC,CAAC,EAAE;IAE9B,MAAMW,MAAM,GAAGrH,IAAI,CAACyD,IAAI;IAExB,IAAI2D,GAAG,CAAC7C,YAAY,CAAC,CAAC,EAAE;MACtB,MAAMrD,SAAS,GAAGkG,GAAG,CAAC3D,IAAI,CAACb,IAAI;MAG/B,IAAIf,KAAK,CAACoE,UAAU,CAAC/E,SAAS,CAAC,KAAKlB,IAAI,CAAC6B,KAAK,CAACoE,UAAU,CAAC/E,SAAS,CAAC,EAAE;QACpE;MACF;MAEA,MAAMoG,aAAa,GAAGzG,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC;MAC7C,MAAM4E,UAAU,GAAGnF,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;MAE1C,IAAI,CAAAoG,aAAa,oBAAbA,aAAa,CAAE1D,MAAM,IAAG,CAAC,IAAIkC,UAAU,EAAE;QAC3C,IAAIA,UAAU,EAAE;UACd9F,IAAI,CAAC2G,WAAW,CACdjE,WAAC,CAAC6C,oBAAoB,CAEnB8B,MAAM,CAACE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,EACzBpF,oBAAoB,CAAC2D,UAAU,EAAEsB,GAAG,CAAC3D,IAAI,CAAC,EAC1C+B,gBAAgB,CAACtE,SAAS,CAC5B,CACF,CAAC;QACH,CAAC,MAAM,IAAImG,MAAM,CAACG,MAAM,EAAE;UAGxBxH,IAAI,CAAC2G,WAAW,CACd5C,sCAAsC,CACpC,IAAI,CAACtD,QAAQ,EACb6G,aAAa,EACb5E,WAAC,CAAC+E,SAAS,CAACJ,MAAM,CAAC,EACnBrH,IAAI,CAAC6B,KACP,CACF,CAAC;QACH,CAAC,MAAM;UAGL,MAAMsE,GAAG,GAAGtE,KAAK,CAAC6F,6BAA6B,CAACxG,SAAS,CAAC;UAE1DlB,IAAI,CAAC2G,WAAW,CACdjE,WAAC,CAACkE,kBAAkB,CAAC,CACnBlE,WAAC,CAAC6C,oBAAoB,CACpB,GAAG,EACH7C,WAAC,CAAC+E,SAAS,CAACtB,GAAG,CAAC,EAChBzD,WAAC,CAAC+E,SAAS,CAACJ,MAAM,CACpB,CAAC,EACDtD,sCAAsC,CACpC,IAAI,CAACtD,QAAQ,EACb6G,aAAa,EACb5E,WAAC,CAACC,UAAU,CAACzB,SAAS,CAAC,EACvBlB,IAAI,CAAC6B,KACP,CAAC,EACDa,WAAC,CAAC+E,SAAS,CAACtB,GAAG,CAAC,CACjB,CACH,CAAC;QACH;MACF;IACF;IAEArF,eAAe,CAACd,IAAI,CAAC;IACrBA,IAAI,CAACsD,IAAI,CAAC,CAAC;EACb,CAAC;EAEDqE,oBAAoB,EAAE;IACpBC,IAAIA,CAAC5H,IAAI,EAAE;MACT,MAAM;QACJ6B,KAAK;QACLI,IAAI;QACJtB,QAAQ;QACRE,QAAQ;QACRC,eAAe;QACfqB;MACF,CAAC,GAAG,IAAI;MAER,IAAIF,IAAI,CAACgB,GAAG,CAACjD,IAAI,CAACyD,IAAI,CAAC,EAAE;MACzBxB,IAAI,CAAC4D,GAAG,CAAC7F,IAAI,CAACyD,IAAI,CAAC;MAEnB,MAAMoE,IAAI,GAAG7H,IAAI,CAACyB,GAAG,CAAC,MAAM,CAAC;MAG7B,IAAIoG,IAAI,CAACnB,kBAAkB,CAAC,CAAC,EAAE;MAE/B,IAAImB,IAAI,CAACtD,YAAY,CAAC,CAAC,EAAE;QAGvB,MAAMrD,SAAS,GAAG2G,IAAI,CAACpE,IAAI,CAACb,IAAI;QAGhC,IAAIf,KAAK,CAACoE,UAAU,CAAC/E,SAAS,CAAC,KAAKlB,IAAI,CAAC6B,KAAK,CAACoE,UAAU,CAAC/E,SAAS,CAAC,EAAE;UACpE;QACF;QAEA,MAAMoG,aAAa,GAAGzG,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC;QAC7C,MAAM4E,UAAU,GAAGnF,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;QAC1C,IAAI,CAAAoG,aAAa,oBAAbA,aAAa,CAAE1D,MAAM,IAAG,CAAC,IAAIkC,UAAU,EAAE;UAC3C,MAAMgC,UAAU,GAAG9H,IAAI,CAACyD,IAAI;UAE5B,IAAIqC,UAAU,EAAE;YACdgC,UAAU,CAACD,IAAI,GAAG1F,oBAAoB,CAAC2D,UAAU,EAAE+B,IAAI,CAACpE,IAAI,CAAC;YAE7DqE,UAAU,CAACC,KAAK,GAAGrF,WAAC,CAACkE,kBAAkB,CAAC,CACtCkB,UAAU,CAACC,KAAK,EAChBvC,gBAAgB,CAACtE,SAAS,CAAC,CAC5B,CAAC;UACJ;UAEA,MAAM;YAAEqG;UAAS,CAAC,GAAGO,UAAU;UAC/B,IAAIE,OAAO;UACX,IAAIT,QAAQ,KAAK,GAAG,EAAE;YACpBS,OAAO,GAAGF,UAAU;UACtB,CAAC,MAAM,IACLP,QAAQ,KAAK,KAAK,IAClBA,QAAQ,KAAK,KAAK,IAClBA,QAAQ,KAAK,KAAK,EAClB;YACAS,OAAO,GAAGtF,WAAC,CAAC6C,oBAAoB,CAC9B,GAAG,EACHuC,UAAU,CAACD,IAAI,EACfnF,WAAC,CAACuF,iBAAiB,CACjBV,QAAQ,CAACW,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACrBxF,WAAC,CAAC+E,SAAS,CAACK,UAAU,CAACD,IAAI,CAAC,EAC5BC,UAAU,CAACC,KACb,CACF,CAAC;UACH,CAAC,MAAM;YACLC,OAAO,GAAGtF,WAAC,CAAC6C,oBAAoB,CAC9B,GAAG,EACHuC,UAAU,CAACD,IAAI,EACfnF,WAAC,CAACyF,gBAAgB,CAChBZ,QAAQ,CAACW,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACrBxF,WAAC,CAAC+E,SAAS,CAACK,UAAU,CAACD,IAAI,CAAC,EAC5BC,UAAU,CAACC,KACb,CACF,CAAC;UACH;UAEA/H,IAAI,CAAC2G,WAAW,CACd5C,sCAAsC,CACpC,IAAI,CAACtD,QAAQ,EACb6G,aAAa,EACbU,OAAO,EACPhI,IAAI,CAAC6B,KACP,CACF,CAAC;UAEDf,eAAe,CAACd,IAAI,CAAC;UAErBA,IAAI,CAACsD,IAAI,CAAC,CAAC;QACb;MACF,CAAC,MAAM;QACL,MAAM8E,GAAG,GAAGP,IAAI,CAAC/C,0BAA0B,CAAC,CAAC;QAC7C,MAAMuD,eAAe,GAAGzD,MAAM,CAACC,IAAI,CAACuD,GAAG,CAAC,CAACE,MAAM,CAC7CpH,SAAS,IACPW,KAAK,CAACoE,UAAU,CAAC/E,SAAS,CAAC,KAAKlB,IAAI,CAAC6B,KAAK,CAACoE,UAAU,CAAC/E,SAAS,CACnE,CAAC;QACD,MAAMsC,EAAE,GAAG6E,eAAe,CAACE,IAAI,CAACrH,SAAS,IAAIP,QAAQ,CAACsC,GAAG,CAAC/B,SAAS,CAAC,CAAC;QAErE,IAAIsC,EAAE,EAAE;UACNxD,IAAI,CAACyD,IAAI,CAACsE,KAAK,GAAGrF,WAAC,CAACkE,kBAAkB,CAAC,CACrC5G,IAAI,CAACyD,IAAI,CAACsE,KAAK,EACfvC,gBAAgB,CAAChC,EAAE,CAAC,CACrB,CAAC;QACJ;QAIA,MAAMgF,KAAqB,GAAG,EAAE;QAChCH,eAAe,CAACI,OAAO,CAACvH,SAAS,IAAI;UACnC,MAAMoG,aAAa,GAAGzG,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;UACnD,IAAIoG,aAAa,CAAC1D,MAAM,GAAG,CAAC,EAAE;YAC5B4E,KAAK,CAAC9G,IAAI,CACRqC,sCAAsC,CACpC,IAAI,CAACtD,QAAQ,EACb6G,aAAa,EACb5E,WAAC,CAACC,UAAU,CAACzB,SAAS,CAAC,EACvBlB,IAAI,CAAC6B,KACP,CACF,CAAC;UACH;QACF,CAAC,CAAC;QAEF,IAAI2G,KAAK,CAAC5E,MAAM,GAAG,CAAC,EAAE;UACpB,IAAIH,IAAY,GAAGf,WAAC,CAACkE,kBAAkB,CAAC4B,KAAK,CAAC;UAC9C,IAAIxI,IAAI,CAACG,UAAU,CAACuI,qBAAqB,CAAC,CAAC,EAAE;YAC3CjF,IAAI,GAAGf,WAAC,CAACoB,mBAAmB,CAACL,IAAI,CAAC;YAElCA,IAAI,CAACO,WAAW,GAAGhE,IAAI,CAACG,UAAU,CAACsD,IAAI,CAACO,WAAW;UACrD;UAEA,MAAMH,SAAS,GAAG7D,IAAI,CAACiE,WAAW,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3C3C,eAAe,CAAC+C,SAAS,CAAC;QAC5B;MACF;IACF;EACF,CAAC;EACD8E,aAAaA,CAAC3I,IAAI,EAAE;IAClB,MAAM;MAAE6B,KAAK;MAAE4B;IAAK,CAAC,GAAGzD,IAAI;IAC5B,MAAM;MAAE6H;IAAK,CAAC,GAAGpE,IAAI;IACrB,MAAM;MAAE5C,QAAQ;MAAEF,QAAQ;MAAEkB,KAAK,EAAE+G;IAAa,CAAC,GAAG,IAAI;IAExD,IAAI,CAAClG,WAAC,CAACmG,qBAAqB,CAAChB,IAAI,CAAC,EAAE;MAClC,IAAIiB,kBAAkB,GAAG,KAAK;QAC5BC,wBAAwB;MAC1B,MAAMC,aAAa,GAAGhJ,IAAI,CAACyB,GAAG,CAAC,MAAM,CAAC,CAACI,KAAK;MAC5C,KAAK,MAAMe,IAAI,IAAIgC,MAAM,CAACC,IAAI,CAACnC,WAAC,CAACoC,0BAA0B,CAAC+C,IAAI,CAAC,CAAC,EAAE;QAClE,IAAIe,YAAY,CAAC3C,UAAU,CAACrD,IAAI,CAAC,KAAKf,KAAK,CAACoE,UAAU,CAACrD,IAAI,CAAC,EAAE;UAC5D,IAAI/B,QAAQ,CAACoC,GAAG,CAACL,IAAI,CAAC,EAAE;YACtBkG,kBAAkB,GAAG,IAAI;YACzB,IAAIE,aAAa,CAAC7D,aAAa,CAACvC,IAAI,CAAC,EAAE;cACrCoG,aAAa,CAAC5D,MAAM,CAACxC,IAAI,CAAC;YAC5B;UACF;UACA,IAAIjC,QAAQ,CAACsC,GAAG,CAACL,IAAI,CAAC,IAAI,CAACmG,wBAAwB,EAAE;YACnDA,wBAAwB,GAAGnG,IAAI;UACjC;QACF;MACF;MACA,IAAI,CAACkG,kBAAkB,IAAI,CAACC,wBAAwB,EAAE;QACpD;MACF;MAEA/I,IAAI,CAACiJ,WAAW,CAAC,CAAC;MAClB,MAAMC,QAAQ,GAAGlJ,IAAI,CAACyB,GAAG,CAAC,MAAM,CAA+B;MAE/D,MAAM0H,SAAS,GAAGtH,KAAK,CAACuH,gCAAgC,CAACvB,IAAI,CAAC;MAC9D7H,IAAI,CACDyB,GAAG,CAAC,MAAM,CAAC,CACXkF,WAAW,CACVjE,WAAC,CAAC2G,mBAAmB,CAAC,KAAK,EAAE,CAC3B3G,WAAC,CAAC4G,kBAAkB,CAAC5G,WAAC,CAAC+E,SAAS,CAAC0B,SAAS,CAAC,CAAC,CAC7C,CACH,CAAC;MACHtH,KAAK,CAAC0H,mBAAmB,CAACvJ,IAAI,CAACyB,GAAG,CAAC,MAAM,CAAC,CAAC;MAE3C,IAAIqH,kBAAkB,EAAE;QACtBI,QAAQ,CAACM,gBAAgB,CACvB,MAAM,EACN9G,WAAC,CAACoB,mBAAmB,CAACpB,WAAC,CAAC6C,oBAAoB,CAAC,GAAG,EAAEsC,IAAI,EAAEsB,SAAS,CAAC,CACpE,CAAC;MACH;MACA,IAAIJ,wBAAwB,EAAE;QAC5BG,QAAQ,CAACM,gBAAgB,CACvB,MAAM,EACN9G,WAAC,CAACoB,mBAAmB,CAAC0B,gBAAgB,CAACuD,wBAAwB,CAAC,CAClE,CAAC;MACH;IACF;EACF;AACF,CAAC", "ignoreList": []}