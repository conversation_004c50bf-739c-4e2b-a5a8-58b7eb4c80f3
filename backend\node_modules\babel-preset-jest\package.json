{"name": "babel-preset-jest", "version": "29.6.3", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/babel-preset-jest"}, "license": "MIT", "main": "./index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b"}