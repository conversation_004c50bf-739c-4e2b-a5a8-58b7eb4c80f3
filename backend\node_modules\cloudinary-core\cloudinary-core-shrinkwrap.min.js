!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.cloudinary=t():e.cloudinary=t()}(this,(function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s="./src/namespace/cloudinary-core-shrinkwrap.js")}({"./node_modules/lodash/_DataView.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(n("./node_modules/lodash/_root.js"),"DataView");e.exports=o},"./node_modules/lodash/_Hash.js":function(e,t,n){var o=n("./node_modules/lodash/_hashClear.js"),r=n("./node_modules/lodash/_hashDelete.js"),i=n("./node_modules/lodash/_hashGet.js"),u=n("./node_modules/lodash/_hashHas.js"),s=n("./node_modules/lodash/_hashSet.js");function a(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}a.prototype.clear=o,a.prototype.delete=r,a.prototype.get=i,a.prototype.has=u,a.prototype.set=s,e.exports=a},"./node_modules/lodash/_ListCache.js":function(e,t,n){var o=n("./node_modules/lodash/_listCacheClear.js"),r=n("./node_modules/lodash/_listCacheDelete.js"),i=n("./node_modules/lodash/_listCacheGet.js"),u=n("./node_modules/lodash/_listCacheHas.js"),s=n("./node_modules/lodash/_listCacheSet.js");function a(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}a.prototype.clear=o,a.prototype.delete=r,a.prototype.get=i,a.prototype.has=u,a.prototype.set=s,e.exports=a},"./node_modules/lodash/_Map.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(n("./node_modules/lodash/_root.js"),"Map");e.exports=o},"./node_modules/lodash/_MapCache.js":function(e,t,n){var o=n("./node_modules/lodash/_mapCacheClear.js"),r=n("./node_modules/lodash/_mapCacheDelete.js"),i=n("./node_modules/lodash/_mapCacheGet.js"),u=n("./node_modules/lodash/_mapCacheHas.js"),s=n("./node_modules/lodash/_mapCacheSet.js");function a(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}a.prototype.clear=o,a.prototype.delete=r,a.prototype.get=i,a.prototype.has=u,a.prototype.set=s,e.exports=a},"./node_modules/lodash/_Promise.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(n("./node_modules/lodash/_root.js"),"Promise");e.exports=o},"./node_modules/lodash/_Set.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(n("./node_modules/lodash/_root.js"),"Set");e.exports=o},"./node_modules/lodash/_SetCache.js":function(e,t,n){var o=n("./node_modules/lodash/_MapCache.js"),r=n("./node_modules/lodash/_setCacheAdd.js"),i=n("./node_modules/lodash/_setCacheHas.js");function u(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new o;++t<n;)this.add(e[t])}u.prototype.add=u.prototype.push=r,u.prototype.has=i,e.exports=u},"./node_modules/lodash/_Stack.js":function(e,t,n){var o=n("./node_modules/lodash/_ListCache.js"),r=n("./node_modules/lodash/_stackClear.js"),i=n("./node_modules/lodash/_stackDelete.js"),u=n("./node_modules/lodash/_stackGet.js"),s=n("./node_modules/lodash/_stackHas.js"),a=n("./node_modules/lodash/_stackSet.js");function l(e){var t=this.__data__=new o(e);this.size=t.size}l.prototype.clear=r,l.prototype.delete=i,l.prototype.get=u,l.prototype.has=s,l.prototype.set=a,e.exports=l},"./node_modules/lodash/_Symbol.js":function(e,t,n){var o=n("./node_modules/lodash/_root.js").Symbol;e.exports=o},"./node_modules/lodash/_Uint8Array.js":function(e,t,n){var o=n("./node_modules/lodash/_root.js").Uint8Array;e.exports=o},"./node_modules/lodash/_WeakMap.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(n("./node_modules/lodash/_root.js"),"WeakMap");e.exports=o},"./node_modules/lodash/_apply.js":function(e,t){e.exports=function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}},"./node_modules/lodash/_arrayEach.js":function(e,t){e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length;++n<o&&!1!==t(e[n],n,e););return e}},"./node_modules/lodash/_arrayFilter.js":function(e,t){e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length,r=0,i=[];++n<o;){var u=e[n];t(u,n,e)&&(i[r++]=u)}return i}},"./node_modules/lodash/_arrayIncludes.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIndexOf.js");e.exports=function(e,t){return!!(null==e?0:e.length)&&o(e,t,0)>-1}},"./node_modules/lodash/_arrayIncludesWith.js":function(e,t){e.exports=function(e,t,n){for(var o=-1,r=null==e?0:e.length;++o<r;)if(n(t,e[o]))return!0;return!1}},"./node_modules/lodash/_arrayLikeKeys.js":function(e,t,n){var o=n("./node_modules/lodash/_baseTimes.js"),r=n("./node_modules/lodash/isArguments.js"),i=n("./node_modules/lodash/isArray.js"),u=n("./node_modules/lodash/isBuffer.js"),s=n("./node_modules/lodash/_isIndex.js"),a=n("./node_modules/lodash/isTypedArray.js"),l=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=i(e),c=!n&&r(e),d=!n&&!c&&u(e),f=!n&&!c&&!d&&a(e),h=n||c||d||f,p=h?o(e.length,String):[],m=p.length;for(var y in e)!t&&!l.call(e,y)||h&&("length"==y||d&&("offset"==y||"parent"==y)||f&&("buffer"==y||"byteLength"==y||"byteOffset"==y)||s(y,m))||p.push(y);return p}},"./node_modules/lodash/_arrayMap.js":function(e,t){e.exports=function(e,t){for(var n=-1,o=null==e?0:e.length,r=Array(o);++n<o;)r[n]=t(e[n],n,e);return r}},"./node_modules/lodash/_arrayPush.js":function(e,t){e.exports=function(e,t){for(var n=-1,o=t.length,r=e.length;++n<o;)e[r+n]=t[n];return e}},"./node_modules/lodash/_asciiToArray.js":function(e,t){e.exports=function(e){return e.split("")}},"./node_modules/lodash/_assignMergeValue.js":function(e,t,n){var o=n("./node_modules/lodash/_baseAssignValue.js"),r=n("./node_modules/lodash/eq.js");e.exports=function(e,t,n){(void 0!==n&&!r(e[t],n)||void 0===n&&!(t in e))&&o(e,t,n)}},"./node_modules/lodash/_assignValue.js":function(e,t,n){var o=n("./node_modules/lodash/_baseAssignValue.js"),r=n("./node_modules/lodash/eq.js"),i=Object.prototype.hasOwnProperty;e.exports=function(e,t,n){var u=e[t];i.call(e,t)&&r(u,n)&&(void 0!==n||t in e)||o(e,t,n)}},"./node_modules/lodash/_assocIndexOf.js":function(e,t,n){var o=n("./node_modules/lodash/eq.js");e.exports=function(e,t){for(var n=e.length;n--;)if(o(e[n][0],t))return n;return-1}},"./node_modules/lodash/_baseAssign.js":function(e,t,n){var o=n("./node_modules/lodash/_copyObject.js"),r=n("./node_modules/lodash/keys.js");e.exports=function(e,t){return e&&o(t,r(t),e)}},"./node_modules/lodash/_baseAssignIn.js":function(e,t,n){var o=n("./node_modules/lodash/_copyObject.js"),r=n("./node_modules/lodash/keysIn.js");e.exports=function(e,t){return e&&o(t,r(t),e)}},"./node_modules/lodash/_baseAssignValue.js":function(e,t,n){var o=n("./node_modules/lodash/_defineProperty.js");e.exports=function(e,t,n){"__proto__"==t&&o?o(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}},"./node_modules/lodash/_baseClone.js":function(e,t,n){var o=n("./node_modules/lodash/_Stack.js"),r=n("./node_modules/lodash/_arrayEach.js"),i=n("./node_modules/lodash/_assignValue.js"),u=n("./node_modules/lodash/_baseAssign.js"),s=n("./node_modules/lodash/_baseAssignIn.js"),a=n("./node_modules/lodash/_cloneBuffer.js"),l=n("./node_modules/lodash/_copyArray.js"),c=n("./node_modules/lodash/_copySymbols.js"),d=n("./node_modules/lodash/_copySymbolsIn.js"),f=n("./node_modules/lodash/_getAllKeys.js"),h=n("./node_modules/lodash/_getAllKeysIn.js"),p=n("./node_modules/lodash/_getTag.js"),m=n("./node_modules/lodash/_initCloneArray.js"),y=n("./node_modules/lodash/_initCloneByTag.js"),_=n("./node_modules/lodash/_initCloneObject.js"),v=n("./node_modules/lodash/isArray.js"),b=n("./node_modules/lodash/isBuffer.js"),g=n("./node_modules/lodash/isMap.js"),j=n("./node_modules/lodash/isObject.js"),w=n("./node_modules/lodash/isSet.js"),A=n("./node_modules/lodash/keys.js"),D=n("./node_modules/lodash/keysIn.js"),O={};O["[object Arguments]"]=O["[object Array]"]=O["[object ArrayBuffer]"]=O["[object DataView]"]=O["[object Boolean]"]=O["[object Date]"]=O["[object Float32Array]"]=O["[object Float64Array]"]=O["[object Int8Array]"]=O["[object Int16Array]"]=O["[object Int32Array]"]=O["[object Map]"]=O["[object Number]"]=O["[object Object]"]=O["[object RegExp]"]=O["[object Set]"]=O["[object String]"]=O["[object Symbol]"]=O["[object Uint8Array]"]=O["[object Uint8ClampedArray]"]=O["[object Uint16Array]"]=O["[object Uint32Array]"]=!0,O["[object Error]"]=O["[object Function]"]=O["[object WeakMap]"]=!1,e.exports=function E(e,t,n,B,C,S){var k,x=1&t,F=2&t,P=4&t;if(n&&(k=C?n(e,B,C,S):n(e)),void 0!==k)return k;if(!j(e))return e;var T=v(e);if(T){if(k=m(e),!x)return l(e,k)}else{var I=p(e),R="[object Function]"==I||"[object GeneratorFunction]"==I;if(b(e))return a(e,x);if("[object Object]"==I||"[object Arguments]"==I||R&&!C){if(k=F||R?{}:_(e),!x)return F?d(e,s(k,e)):c(e,u(k,e))}else{if(!O[I])return C?e:{};k=y(e,I,x)}}S||(S=new o);var L=S.get(e);if(L)return L;S.set(e,k),w(e)?e.forEach((function(o){k.add(E(o,t,n,o,e,S))})):g(e)&&e.forEach((function(o,r){k.set(r,E(o,t,n,r,e,S))}));var z=T?void 0:(P?F?h:f:F?D:A)(e);return r(z||e,(function(o,r){z&&(o=e[r=o]),i(k,r,E(o,t,n,r,e,S))})),k}},"./node_modules/lodash/_baseCreate.js":function(e,t,n){var o=n("./node_modules/lodash/isObject.js"),r=Object.create,i=function(){function e(){}return function(t){if(!o(t))return{};if(r)return r(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();e.exports=i},"./node_modules/lodash/_baseDifference.js":function(e,t,n){var o=n("./node_modules/lodash/_SetCache.js"),r=n("./node_modules/lodash/_arrayIncludes.js"),i=n("./node_modules/lodash/_arrayIncludesWith.js"),u=n("./node_modules/lodash/_arrayMap.js"),s=n("./node_modules/lodash/_baseUnary.js"),a=n("./node_modules/lodash/_cacheHas.js");e.exports=function(e,t,n,l){var c=-1,d=r,f=!0,h=e.length,p=[],m=t.length;if(!h)return p;n&&(t=u(t,s(n))),l?(d=i,f=!1):t.length>=200&&(d=a,f=!1,t=new o(t));e:for(;++c<h;){var y=e[c],_=null==n?y:n(y);if(y=l||0!==y?y:0,f&&_==_){for(var v=m;v--;)if(t[v]===_)continue e;p.push(y)}else d(t,_,l)||p.push(y)}return p}},"./node_modules/lodash/_baseFindIndex.js":function(e,t){e.exports=function(e,t,n,o){for(var r=e.length,i=n+(o?1:-1);o?i--:++i<r;)if(t(e[i],i,e))return i;return-1}},"./node_modules/lodash/_baseFlatten.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayPush.js"),r=n("./node_modules/lodash/_isFlattenable.js");e.exports=function i(e,t,n,u,s){var a=-1,l=e.length;for(n||(n=r),s||(s=[]);++a<l;){var c=e[a];t>0&&n(c)?t>1?i(c,t-1,n,u,s):o(s,c):u||(s[s.length]=c)}return s}},"./node_modules/lodash/_baseFor.js":function(e,t,n){var o=n("./node_modules/lodash/_createBaseFor.js")();e.exports=o},"./node_modules/lodash/_baseFunctions.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayFilter.js"),r=n("./node_modules/lodash/isFunction.js");e.exports=function(e,t){return o(t,(function(t){return r(e[t])}))}},"./node_modules/lodash/_baseGetAllKeys.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayPush.js"),r=n("./node_modules/lodash/isArray.js");e.exports=function(e,t,n){var i=t(e);return r(e)?i:o(i,n(e))}},"./node_modules/lodash/_baseGetTag.js":function(e,t,n){var o=n("./node_modules/lodash/_Symbol.js"),r=n("./node_modules/lodash/_getRawTag.js"),i=n("./node_modules/lodash/_objectToString.js"),u=o?o.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":u&&u in Object(e)?r(e):i(e)}},"./node_modules/lodash/_baseIndexOf.js":function(e,t,n){var o=n("./node_modules/lodash/_baseFindIndex.js"),r=n("./node_modules/lodash/_baseIsNaN.js"),i=n("./node_modules/lodash/_strictIndexOf.js");e.exports=function(e,t,n){return t==t?i(e,t,n):o(e,r,n)}},"./node_modules/lodash/_baseIsArguments.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return r(e)&&"[object Arguments]"==o(e)}},"./node_modules/lodash/_baseIsMap.js":function(e,t,n){var o=n("./node_modules/lodash/_getTag.js"),r=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return r(e)&&"[object Map]"==o(e)}},"./node_modules/lodash/_baseIsNaN.js":function(e,t){e.exports=function(e){return e!=e}},"./node_modules/lodash/_baseIsNative.js":function(e,t,n){var o=n("./node_modules/lodash/isFunction.js"),r=n("./node_modules/lodash/_isMasked.js"),i=n("./node_modules/lodash/isObject.js"),u=n("./node_modules/lodash/_toSource.js"),s=/^\[object .+?Constructor\]$/,a=Function.prototype,l=Object.prototype,c=a.toString,d=l.hasOwnProperty,f=RegExp("^"+c.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||r(e))&&(o(e)?f:s).test(u(e))}},"./node_modules/lodash/_baseIsSet.js":function(e,t,n){var o=n("./node_modules/lodash/_getTag.js"),r=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return r(e)&&"[object Set]"==o(e)}},"./node_modules/lodash/_baseIsTypedArray.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/isLength.js"),i=n("./node_modules/lodash/isObjectLike.js"),u={};u["[object Float32Array]"]=u["[object Float64Array]"]=u["[object Int8Array]"]=u["[object Int16Array]"]=u["[object Int32Array]"]=u["[object Uint8Array]"]=u["[object Uint8ClampedArray]"]=u["[object Uint16Array]"]=u["[object Uint32Array]"]=!0,u["[object Arguments]"]=u["[object Array]"]=u["[object ArrayBuffer]"]=u["[object Boolean]"]=u["[object DataView]"]=u["[object Date]"]=u["[object Error]"]=u["[object Function]"]=u["[object Map]"]=u["[object Number]"]=u["[object Object]"]=u["[object RegExp]"]=u["[object Set]"]=u["[object String]"]=u["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&r(e.length)&&!!u[o(e)]}},"./node_modules/lodash/_baseKeys.js":function(e,t,n){var o=n("./node_modules/lodash/_isPrototype.js"),r=n("./node_modules/lodash/_nativeKeys.js"),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!o(e))return r(e);var t=[];for(var n in Object(e))i.call(e,n)&&"constructor"!=n&&t.push(n);return t}},"./node_modules/lodash/_baseKeysIn.js":function(e,t,n){var o=n("./node_modules/lodash/isObject.js"),r=n("./node_modules/lodash/_isPrototype.js"),i=n("./node_modules/lodash/_nativeKeysIn.js"),u=Object.prototype.hasOwnProperty;e.exports=function(e){if(!o(e))return i(e);var t=r(e),n=[];for(var s in e)("constructor"!=s||!t&&u.call(e,s))&&n.push(s);return n}},"./node_modules/lodash/_baseMerge.js":function(e,t,n){var o=n("./node_modules/lodash/_Stack.js"),r=n("./node_modules/lodash/_assignMergeValue.js"),i=n("./node_modules/lodash/_baseFor.js"),u=n("./node_modules/lodash/_baseMergeDeep.js"),s=n("./node_modules/lodash/isObject.js"),a=n("./node_modules/lodash/keysIn.js"),l=n("./node_modules/lodash/_safeGet.js");e.exports=function c(e,t,n,d,f){e!==t&&i(t,(function(i,a){if(f||(f=new o),s(i))u(e,t,a,n,c,d,f);else{var h=d?d(l(e,a),i,a+"",e,t,f):void 0;void 0===h&&(h=i),r(e,a,h)}}),a)}},"./node_modules/lodash/_baseMergeDeep.js":function(e,t,n){var o=n("./node_modules/lodash/_assignMergeValue.js"),r=n("./node_modules/lodash/_cloneBuffer.js"),i=n("./node_modules/lodash/_cloneTypedArray.js"),u=n("./node_modules/lodash/_copyArray.js"),s=n("./node_modules/lodash/_initCloneObject.js"),a=n("./node_modules/lodash/isArguments.js"),l=n("./node_modules/lodash/isArray.js"),c=n("./node_modules/lodash/isArrayLikeObject.js"),d=n("./node_modules/lodash/isBuffer.js"),f=n("./node_modules/lodash/isFunction.js"),h=n("./node_modules/lodash/isObject.js"),p=n("./node_modules/lodash/isPlainObject.js"),m=n("./node_modules/lodash/isTypedArray.js"),y=n("./node_modules/lodash/_safeGet.js"),_=n("./node_modules/lodash/toPlainObject.js");e.exports=function(e,t,n,v,b,g,j){var w=y(e,n),A=y(t,n),D=j.get(A);if(D)o(e,n,D);else{var O=g?g(w,A,n+"",e,t,j):void 0,E=void 0===O;if(E){var B=l(A),C=!B&&d(A),S=!B&&!C&&m(A);O=A,B||C||S?l(w)?O=w:c(w)?O=u(w):C?(E=!1,O=r(A,!0)):S?(E=!1,O=i(A,!0)):O=[]:p(A)||a(A)?(O=w,a(w)?O=_(w):h(w)&&!f(w)||(O=s(A))):E=!1}E&&(j.set(A,O),b(O,A,v,g,j),j.delete(A)),o(e,n,O)}}},"./node_modules/lodash/_baseRest.js":function(e,t,n){var o=n("./node_modules/lodash/identity.js"),r=n("./node_modules/lodash/_overRest.js"),i=n("./node_modules/lodash/_setToString.js");e.exports=function(e,t){return i(r(e,t,o),e+"")}},"./node_modules/lodash/_baseSetToString.js":function(e,t,n){var o=n("./node_modules/lodash/constant.js"),r=n("./node_modules/lodash/_defineProperty.js"),i=n("./node_modules/lodash/identity.js"),u=r?function(e,t){return r(e,"toString",{configurable:!0,enumerable:!1,value:o(t),writable:!0})}:i;e.exports=u},"./node_modules/lodash/_baseSlice.js":function(e,t){e.exports=function(e,t,n){var o=-1,r=e.length;t<0&&(t=-t>r?0:r+t),(n=n>r?r:n)<0&&(n+=r),r=t>n?0:n-t>>>0,t>>>=0;for(var i=Array(r);++o<r;)i[o]=e[o+t];return i}},"./node_modules/lodash/_baseTimes.js":function(e,t){e.exports=function(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}},"./node_modules/lodash/_baseToString.js":function(e,t,n){var o=n("./node_modules/lodash/_Symbol.js"),r=n("./node_modules/lodash/_arrayMap.js"),i=n("./node_modules/lodash/isArray.js"),u=n("./node_modules/lodash/isSymbol.js"),s=o?o.prototype:void 0,a=s?s.toString:void 0;e.exports=function l(e){if("string"==typeof e)return e;if(i(e))return r(e,l)+"";if(u(e))return a?a.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}},"./node_modules/lodash/_baseTrim.js":function(e,t,n){var o=n("./node_modules/lodash/_trimmedEndIndex.js"),r=/^\s+/;e.exports=function(e){return e?e.slice(0,o(e)+1).replace(r,""):e}},"./node_modules/lodash/_baseUnary.js":function(e,t){e.exports=function(e){return function(t){return e(t)}}},"./node_modules/lodash/_baseValues.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayMap.js");e.exports=function(e,t){return o(t,(function(t){return e[t]}))}},"./node_modules/lodash/_cacheHas.js":function(e,t){e.exports=function(e,t){return e.has(t)}},"./node_modules/lodash/_castSlice.js":function(e,t,n){var o=n("./node_modules/lodash/_baseSlice.js");e.exports=function(e,t,n){var r=e.length;return n=void 0===n?r:n,!t&&n>=r?e:o(e,t,n)}},"./node_modules/lodash/_charsEndIndex.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIndexOf.js");e.exports=function(e,t){for(var n=e.length;n--&&o(t,e[n],0)>-1;);return n}},"./node_modules/lodash/_charsStartIndex.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIndexOf.js");e.exports=function(e,t){for(var n=-1,r=e.length;++n<r&&o(t,e[n],0)>-1;);return n}},"./node_modules/lodash/_cloneArrayBuffer.js":function(e,t,n){var o=n("./node_modules/lodash/_Uint8Array.js");e.exports=function(e){var t=new e.constructor(e.byteLength);return new o(t).set(new o(e)),t}},"./node_modules/lodash/_cloneBuffer.js":function(e,t,n){(function(e){var o=n("./node_modules/lodash/_root.js"),r=t&&!t.nodeType&&t,i=r&&"object"==typeof e&&e&&!e.nodeType&&e,u=i&&i.exports===r?o.Buffer:void 0,s=u?u.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,o=s?s(n):new e.constructor(n);return e.copy(o),o}}).call(this,n("./node_modules/webpack/buildin/module.js")(e))},"./node_modules/lodash/_cloneDataView.js":function(e,t,n){var o=n("./node_modules/lodash/_cloneArrayBuffer.js");e.exports=function(e,t){var n=t?o(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}},"./node_modules/lodash/_cloneRegExp.js":function(e,t){var n=/\w*$/;e.exports=function(e){var t=new e.constructor(e.source,n.exec(e));return t.lastIndex=e.lastIndex,t}},"./node_modules/lodash/_cloneSymbol.js":function(e,t,n){var o=n("./node_modules/lodash/_Symbol.js"),r=o?o.prototype:void 0,i=r?r.valueOf:void 0;e.exports=function(e){return i?Object(i.call(e)):{}}},"./node_modules/lodash/_cloneTypedArray.js":function(e,t,n){var o=n("./node_modules/lodash/_cloneArrayBuffer.js");e.exports=function(e,t){var n=t?o(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}},"./node_modules/lodash/_copyArray.js":function(e,t){e.exports=function(e,t){var n=-1,o=e.length;for(t||(t=Array(o));++n<o;)t[n]=e[n];return t}},"./node_modules/lodash/_copyObject.js":function(e,t,n){var o=n("./node_modules/lodash/_assignValue.js"),r=n("./node_modules/lodash/_baseAssignValue.js");e.exports=function(e,t,n,i){var u=!n;n||(n={});for(var s=-1,a=t.length;++s<a;){var l=t[s],c=i?i(n[l],e[l],l,n,e):void 0;void 0===c&&(c=e[l]),u?r(n,l,c):o(n,l,c)}return n}},"./node_modules/lodash/_copySymbols.js":function(e,t,n){var o=n("./node_modules/lodash/_copyObject.js"),r=n("./node_modules/lodash/_getSymbols.js");e.exports=function(e,t){return o(e,r(e),t)}},"./node_modules/lodash/_copySymbolsIn.js":function(e,t,n){var o=n("./node_modules/lodash/_copyObject.js"),r=n("./node_modules/lodash/_getSymbolsIn.js");e.exports=function(e,t){return o(e,r(e),t)}},"./node_modules/lodash/_coreJsData.js":function(e,t,n){var o=n("./node_modules/lodash/_root.js")["__core-js_shared__"];e.exports=o},"./node_modules/lodash/_createAssigner.js":function(e,t,n){var o=n("./node_modules/lodash/_baseRest.js"),r=n("./node_modules/lodash/_isIterateeCall.js");e.exports=function(e){return o((function(t,n){var o=-1,i=n.length,u=i>1?n[i-1]:void 0,s=i>2?n[2]:void 0;for(u=e.length>3&&"function"==typeof u?(i--,u):void 0,s&&r(n[0],n[1],s)&&(u=i<3?void 0:u,i=1),t=Object(t);++o<i;){var a=n[o];a&&e(t,a,o,u)}return t}))}},"./node_modules/lodash/_createBaseFor.js":function(e,t){e.exports=function(e){return function(t,n,o){for(var r=-1,i=Object(t),u=o(t),s=u.length;s--;){var a=u[e?s:++r];if(!1===n(i[a],a,i))break}return t}}},"./node_modules/lodash/_defineProperty.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js"),r=function(){try{var e=o(Object,"defineProperty");return e({},"",{}),e}catch(t){}}();e.exports=r},"./node_modules/lodash/_freeGlobal.js":function(e,t,n){(function(t){var n="object"==typeof t&&t&&t.Object===Object&&t;e.exports=n}).call(this,n("./node_modules/webpack/buildin/global.js"))},"./node_modules/lodash/_getAllKeys.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetAllKeys.js"),r=n("./node_modules/lodash/_getSymbols.js"),i=n("./node_modules/lodash/keys.js");e.exports=function(e){return o(e,i,r)}},"./node_modules/lodash/_getAllKeysIn.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetAllKeys.js"),r=n("./node_modules/lodash/_getSymbolsIn.js"),i=n("./node_modules/lodash/keysIn.js");e.exports=function(e){return o(e,i,r)}},"./node_modules/lodash/_getMapData.js":function(e,t,n){var o=n("./node_modules/lodash/_isKeyable.js");e.exports=function(e,t){var n=e.__data__;return o(t)?n["string"==typeof t?"string":"hash"]:n.map}},"./node_modules/lodash/_getNative.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIsNative.js"),r=n("./node_modules/lodash/_getValue.js");e.exports=function(e,t){var n=r(e,t);return o(n)?n:void 0}},"./node_modules/lodash/_getPrototype.js":function(e,t,n){var o=n("./node_modules/lodash/_overArg.js")(Object.getPrototypeOf,Object);e.exports=o},"./node_modules/lodash/_getRawTag.js":function(e,t,n){var o=n("./node_modules/lodash/_Symbol.js"),r=Object.prototype,i=r.hasOwnProperty,u=r.toString,s=o?o.toStringTag:void 0;e.exports=function(e){var t=i.call(e,s),n=e[s];try{e[s]=void 0;var o=!0}catch(a){}var r=u.call(e);return o&&(t?e[s]=n:delete e[s]),r}},"./node_modules/lodash/_getSymbols.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayFilter.js"),r=n("./node_modules/lodash/stubArray.js"),i=Object.prototype.propertyIsEnumerable,u=Object.getOwnPropertySymbols,s=u?function(e){return null==e?[]:(e=Object(e),o(u(e),(function(t){return i.call(e,t)})))}:r;e.exports=s},"./node_modules/lodash/_getSymbolsIn.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayPush.js"),r=n("./node_modules/lodash/_getPrototype.js"),i=n("./node_modules/lodash/_getSymbols.js"),u=n("./node_modules/lodash/stubArray.js"),s=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)o(t,i(e)),e=r(e);return t}:u;e.exports=s},"./node_modules/lodash/_getTag.js":function(e,t,n){var o=n("./node_modules/lodash/_DataView.js"),r=n("./node_modules/lodash/_Map.js"),i=n("./node_modules/lodash/_Promise.js"),u=n("./node_modules/lodash/_Set.js"),s=n("./node_modules/lodash/_WeakMap.js"),a=n("./node_modules/lodash/_baseGetTag.js"),l=n("./node_modules/lodash/_toSource.js"),c=l(o),d=l(r),f=l(i),h=l(u),p=l(s),m=a;(o&&"[object DataView]"!=m(new o(new ArrayBuffer(1)))||r&&"[object Map]"!=m(new r)||i&&"[object Promise]"!=m(i.resolve())||u&&"[object Set]"!=m(new u)||s&&"[object WeakMap]"!=m(new s))&&(m=function(e){var t=a(e),n="[object Object]"==t?e.constructor:void 0,o=n?l(n):"";if(o)switch(o){case c:return"[object DataView]";case d:return"[object Map]";case f:return"[object Promise]";case h:return"[object Set]";case p:return"[object WeakMap]"}return t}),e.exports=m},"./node_modules/lodash/_getValue.js":function(e,t){e.exports=function(e,t){return null==e?void 0:e[t]}},"./node_modules/lodash/_hasUnicode.js":function(e,t){var n=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return n.test(e)}},"./node_modules/lodash/_hashClear.js":function(e,t,n){var o=n("./node_modules/lodash/_nativeCreate.js");e.exports=function(){this.__data__=o?o(null):{},this.size=0}},"./node_modules/lodash/_hashDelete.js":function(e,t){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},"./node_modules/lodash/_hashGet.js":function(e,t,n){var o=n("./node_modules/lodash/_nativeCreate.js"),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(o){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return r.call(t,e)?t[e]:void 0}},"./node_modules/lodash/_hashHas.js":function(e,t,n){var o=n("./node_modules/lodash/_nativeCreate.js"),r=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return o?void 0!==t[e]:r.call(t,e)}},"./node_modules/lodash/_hashSet.js":function(e,t,n){var o=n("./node_modules/lodash/_nativeCreate.js");e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=o&&void 0===t?"__lodash_hash_undefined__":t,this}},"./node_modules/lodash/_initCloneArray.js":function(e,t){var n=Object.prototype.hasOwnProperty;e.exports=function(e){var t=e.length,o=new e.constructor(t);return t&&"string"==typeof e[0]&&n.call(e,"index")&&(o.index=e.index,o.input=e.input),o}},"./node_modules/lodash/_initCloneByTag.js":function(e,t,n){var o=n("./node_modules/lodash/_cloneArrayBuffer.js"),r=n("./node_modules/lodash/_cloneDataView.js"),i=n("./node_modules/lodash/_cloneRegExp.js"),u=n("./node_modules/lodash/_cloneSymbol.js"),s=n("./node_modules/lodash/_cloneTypedArray.js");e.exports=function(e,t,n){var a=e.constructor;switch(t){case"[object ArrayBuffer]":return o(e);case"[object Boolean]":case"[object Date]":return new a(+e);case"[object DataView]":return r(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return s(e,n);case"[object Map]":return new a;case"[object Number]":case"[object String]":return new a(e);case"[object RegExp]":return i(e);case"[object Set]":return new a;case"[object Symbol]":return u(e)}}},"./node_modules/lodash/_initCloneObject.js":function(e,t,n){var o=n("./node_modules/lodash/_baseCreate.js"),r=n("./node_modules/lodash/_getPrototype.js"),i=n("./node_modules/lodash/_isPrototype.js");e.exports=function(e){return"function"!=typeof e.constructor||i(e)?{}:o(r(e))}},"./node_modules/lodash/_isFlattenable.js":function(e,t,n){var o=n("./node_modules/lodash/_Symbol.js"),r=n("./node_modules/lodash/isArguments.js"),i=n("./node_modules/lodash/isArray.js"),u=o?o.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||r(e)||!!(u&&e&&e[u])}},"./node_modules/lodash/_isIndex.js":function(e,t){var n=/^(?:0|[1-9]\d*)$/;e.exports=function(e,t){var o=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==o||"symbol"!=o&&n.test(e))&&e>-1&&e%1==0&&e<t}},"./node_modules/lodash/_isIterateeCall.js":function(e,t,n){var o=n("./node_modules/lodash/eq.js"),r=n("./node_modules/lodash/isArrayLike.js"),i=n("./node_modules/lodash/_isIndex.js"),u=n("./node_modules/lodash/isObject.js");e.exports=function(e,t,n){if(!u(n))return!1;var s=typeof t;return!!("number"==s?r(n)&&i(t,n.length):"string"==s&&t in n)&&o(n[t],e)}},"./node_modules/lodash/_isKeyable.js":function(e,t){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},"./node_modules/lodash/_isMasked.js":function(e,t,n){var o,r=n("./node_modules/lodash/_coreJsData.js"),i=(o=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||""))?"Symbol(src)_1."+o:"";e.exports=function(e){return!!i&&i in e}},"./node_modules/lodash/_isPrototype.js":function(e,t){var n=Object.prototype;e.exports=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||n)}},"./node_modules/lodash/_listCacheClear.js":function(e,t){e.exports=function(){this.__data__=[],this.size=0}},"./node_modules/lodash/_listCacheDelete.js":function(e,t,n){var o=n("./node_modules/lodash/_assocIndexOf.js"),r=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=o(t,e);return!(n<0)&&(n==t.length-1?t.pop():r.call(t,n,1),--this.size,!0)}},"./node_modules/lodash/_listCacheGet.js":function(e,t,n){var o=n("./node_modules/lodash/_assocIndexOf.js");e.exports=function(e){var t=this.__data__,n=o(t,e);return n<0?void 0:t[n][1]}},"./node_modules/lodash/_listCacheHas.js":function(e,t,n){var o=n("./node_modules/lodash/_assocIndexOf.js");e.exports=function(e){return o(this.__data__,e)>-1}},"./node_modules/lodash/_listCacheSet.js":function(e,t,n){var o=n("./node_modules/lodash/_assocIndexOf.js");e.exports=function(e,t){var n=this.__data__,r=o(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this}},"./node_modules/lodash/_mapCacheClear.js":function(e,t,n){var o=n("./node_modules/lodash/_Hash.js"),r=n("./node_modules/lodash/_ListCache.js"),i=n("./node_modules/lodash/_Map.js");e.exports=function(){this.size=0,this.__data__={hash:new o,map:new(i||r),string:new o}}},"./node_modules/lodash/_mapCacheDelete.js":function(e,t,n){var o=n("./node_modules/lodash/_getMapData.js");e.exports=function(e){var t=o(this,e).delete(e);return this.size-=t?1:0,t}},"./node_modules/lodash/_mapCacheGet.js":function(e,t,n){var o=n("./node_modules/lodash/_getMapData.js");e.exports=function(e){return o(this,e).get(e)}},"./node_modules/lodash/_mapCacheHas.js":function(e,t,n){var o=n("./node_modules/lodash/_getMapData.js");e.exports=function(e){return o(this,e).has(e)}},"./node_modules/lodash/_mapCacheSet.js":function(e,t,n){var o=n("./node_modules/lodash/_getMapData.js");e.exports=function(e,t){var n=o(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this}},"./node_modules/lodash/_nativeCreate.js":function(e,t,n){var o=n("./node_modules/lodash/_getNative.js")(Object,"create");e.exports=o},"./node_modules/lodash/_nativeKeys.js":function(e,t,n){var o=n("./node_modules/lodash/_overArg.js")(Object.keys,Object);e.exports=o},"./node_modules/lodash/_nativeKeysIn.js":function(e,t){e.exports=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}},"./node_modules/lodash/_nodeUtil.js":function(e,t,n){(function(e){var o=n("./node_modules/lodash/_freeGlobal.js"),r=t&&!t.nodeType&&t,i=r&&"object"==typeof e&&e&&!e.nodeType&&e,u=i&&i.exports===r&&o.process,s=function(){try{var e=i&&i.require&&i.require("util").types;return e||u&&u.binding&&u.binding("util")}catch(t){}}();e.exports=s}).call(this,n("./node_modules/webpack/buildin/module.js")(e))},"./node_modules/lodash/_objectToString.js":function(e,t){var n=Object.prototype.toString;e.exports=function(e){return n.call(e)}},"./node_modules/lodash/_overArg.js":function(e,t){e.exports=function(e,t){return function(n){return e(t(n))}}},"./node_modules/lodash/_overRest.js":function(e,t,n){var o=n("./node_modules/lodash/_apply.js"),r=Math.max;e.exports=function(e,t,n){return t=r(void 0===t?e.length-1:t,0),function(){for(var i=arguments,u=-1,s=r(i.length-t,0),a=Array(s);++u<s;)a[u]=i[t+u];u=-1;for(var l=Array(t+1);++u<t;)l[u]=i[u];return l[t]=n(a),o(e,this,l)}}},"./node_modules/lodash/_root.js":function(e,t,n){var o=n("./node_modules/lodash/_freeGlobal.js"),r="object"==typeof self&&self&&self.Object===Object&&self,i=o||r||Function("return this")();e.exports=i},"./node_modules/lodash/_safeGet.js":function(e,t){e.exports=function(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}},"./node_modules/lodash/_setCacheAdd.js":function(e,t){e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},"./node_modules/lodash/_setCacheHas.js":function(e,t){e.exports=function(e){return this.__data__.has(e)}},"./node_modules/lodash/_setToString.js":function(e,t,n){var o=n("./node_modules/lodash/_baseSetToString.js"),r=n("./node_modules/lodash/_shortOut.js")(o);e.exports=r},"./node_modules/lodash/_shortOut.js":function(e,t){var n=Date.now;e.exports=function(e){var t=0,o=0;return function(){var r=n(),i=16-(r-o);if(o=r,i>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}},"./node_modules/lodash/_stackClear.js":function(e,t,n){var o=n("./node_modules/lodash/_ListCache.js");e.exports=function(){this.__data__=new o,this.size=0}},"./node_modules/lodash/_stackDelete.js":function(e,t){e.exports=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}},"./node_modules/lodash/_stackGet.js":function(e,t){e.exports=function(e){return this.__data__.get(e)}},"./node_modules/lodash/_stackHas.js":function(e,t){e.exports=function(e){return this.__data__.has(e)}},"./node_modules/lodash/_stackSet.js":function(e,t,n){var o=n("./node_modules/lodash/_ListCache.js"),r=n("./node_modules/lodash/_Map.js"),i=n("./node_modules/lodash/_MapCache.js");e.exports=function(e,t){var n=this.__data__;if(n instanceof o){var u=n.__data__;if(!r||u.length<199)return u.push([e,t]),this.size=++n.size,this;n=this.__data__=new i(u)}return n.set(e,t),this.size=n.size,this}},"./node_modules/lodash/_strictIndexOf.js":function(e,t){e.exports=function(e,t,n){for(var o=n-1,r=e.length;++o<r;)if(e[o]===t)return o;return-1}},"./node_modules/lodash/_stringToArray.js":function(e,t,n){var o=n("./node_modules/lodash/_asciiToArray.js"),r=n("./node_modules/lodash/_hasUnicode.js"),i=n("./node_modules/lodash/_unicodeToArray.js");e.exports=function(e){return r(e)?i(e):o(e)}},"./node_modules/lodash/_toSource.js":function(e,t){var n=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return n.call(e)}catch(t){}try{return e+""}catch(t){}}return""}},"./node_modules/lodash/_trimmedEndIndex.js":function(e,t){var n=/\s/;e.exports=function(e){for(var t=e.length;t--&&n.test(e.charAt(t)););return t}},"./node_modules/lodash/_unicodeToArray.js":function(e,t){var n="[\\ud800-\\udfff]",o="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",r="\\ud83c[\\udffb-\\udfff]",i="[^\\ud800-\\udfff]",u="(?:\\ud83c[\\udde6-\\uddff]){2}",s="[\\ud800-\\udbff][\\udc00-\\udfff]",a="(?:"+o+"|"+r+")"+"?",l="[\\ufe0e\\ufe0f]?"+a+("(?:\\u200d(?:"+[i,u,s].join("|")+")[\\ufe0e\\ufe0f]?"+a+")*"),c="(?:"+[i+o+"?",o,u,s,n].join("|")+")",d=RegExp(r+"(?="+r+")|"+c+l,"g");e.exports=function(e){return e.match(d)||[]}},"./node_modules/lodash/assign.js":function(e,t,n){var o=n("./node_modules/lodash/_assignValue.js"),r=n("./node_modules/lodash/_copyObject.js"),i=n("./node_modules/lodash/_createAssigner.js"),u=n("./node_modules/lodash/isArrayLike.js"),s=n("./node_modules/lodash/_isPrototype.js"),a=n("./node_modules/lodash/keys.js"),l=Object.prototype.hasOwnProperty,c=i((function(e,t){if(s(t)||u(t))r(t,a(t),e);else for(var n in t)l.call(t,n)&&o(e,n,t[n])}));e.exports=c},"./node_modules/lodash/cloneDeep.js":function(e,t,n){var o=n("./node_modules/lodash/_baseClone.js");e.exports=function(e){return o(e,5)}},"./node_modules/lodash/compact.js":function(e,t){e.exports=function(e){for(var t=-1,n=null==e?0:e.length,o=0,r=[];++t<n;){var i=e[t];i&&(r[o++]=i)}return r}},"./node_modules/lodash/constant.js":function(e,t){e.exports=function(e){return function(){return e}}},"./node_modules/lodash/difference.js":function(e,t,n){var o=n("./node_modules/lodash/_baseDifference.js"),r=n("./node_modules/lodash/_baseFlatten.js"),i=n("./node_modules/lodash/_baseRest.js"),u=n("./node_modules/lodash/isArrayLikeObject.js"),s=i((function(e,t){return u(e)?o(e,r(t,1,u,!0)):[]}));e.exports=s},"./node_modules/lodash/eq.js":function(e,t){e.exports=function(e,t){return e===t||e!=e&&t!=t}},"./node_modules/lodash/functions.js":function(e,t,n){var o=n("./node_modules/lodash/_baseFunctions.js"),r=n("./node_modules/lodash/keys.js");e.exports=function(e){return null==e?[]:o(e,r(e))}},"./node_modules/lodash/identity.js":function(e,t){e.exports=function(e){return e}},"./node_modules/lodash/includes.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIndexOf.js"),r=n("./node_modules/lodash/isArrayLike.js"),i=n("./node_modules/lodash/isString.js"),u=n("./node_modules/lodash/toInteger.js"),s=n("./node_modules/lodash/values.js"),a=Math.max;e.exports=function(e,t,n,l){e=r(e)?e:s(e),n=n&&!l?u(n):0;var c=e.length;return n<0&&(n=a(c+n,0)),i(e)?n<=c&&e.indexOf(t,n)>-1:!!c&&o(e,t,n)>-1}},"./node_modules/lodash/isArguments.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIsArguments.js"),r=n("./node_modules/lodash/isObjectLike.js"),i=Object.prototype,u=i.hasOwnProperty,s=i.propertyIsEnumerable,a=o(function(){return arguments}())?o:function(e){return r(e)&&u.call(e,"callee")&&!s.call(e,"callee")};e.exports=a},"./node_modules/lodash/isArray.js":function(e,t){var n=Array.isArray;e.exports=n},"./node_modules/lodash/isArrayLike.js":function(e,t,n){var o=n("./node_modules/lodash/isFunction.js"),r=n("./node_modules/lodash/isLength.js");e.exports=function(e){return null!=e&&r(e.length)&&!o(e)}},"./node_modules/lodash/isArrayLikeObject.js":function(e,t,n){var o=n("./node_modules/lodash/isArrayLike.js"),r=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return r(e)&&o(e)}},"./node_modules/lodash/isBuffer.js":function(e,t,n){(function(e){var o=n("./node_modules/lodash/_root.js"),r=n("./node_modules/lodash/stubFalse.js"),i=t&&!t.nodeType&&t,u=i&&"object"==typeof e&&e&&!e.nodeType&&e,s=u&&u.exports===i?o.Buffer:void 0,a=(s?s.isBuffer:void 0)||r;e.exports=a}).call(this,n("./node_modules/webpack/buildin/module.js")(e))},"./node_modules/lodash/isElement.js":function(e,t,n){var o=n("./node_modules/lodash/isObjectLike.js"),r=n("./node_modules/lodash/isPlainObject.js");e.exports=function(e){return o(e)&&1===e.nodeType&&!r(e)}},"./node_modules/lodash/isFunction.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/isObject.js");e.exports=function(e){if(!r(e))return!1;var t=o(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},"./node_modules/lodash/isLength.js":function(e,t){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},"./node_modules/lodash/isMap.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIsMap.js"),r=n("./node_modules/lodash/_baseUnary.js"),i=n("./node_modules/lodash/_nodeUtil.js"),u=i&&i.isMap,s=u?r(u):o;e.exports=s},"./node_modules/lodash/isObject.js":function(e,t){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},"./node_modules/lodash/isObjectLike.js":function(e,t){e.exports=function(e){return null!=e&&"object"==typeof e}},"./node_modules/lodash/isPlainObject.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/_getPrototype.js"),i=n("./node_modules/lodash/isObjectLike.js"),u=Function.prototype,s=Object.prototype,a=u.toString,l=s.hasOwnProperty,c=a.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=o(e))return!1;var t=r(e);if(null===t)return!0;var n=l.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&a.call(n)==c}},"./node_modules/lodash/isSet.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIsSet.js"),r=n("./node_modules/lodash/_baseUnary.js"),i=n("./node_modules/lodash/_nodeUtil.js"),u=i&&i.isSet,s=u?r(u):o;e.exports=s},"./node_modules/lodash/isString.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/isArray.js"),i=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return"string"==typeof e||!r(e)&&i(e)&&"[object String]"==o(e)}},"./node_modules/lodash/isSymbol.js":function(e,t,n){var o=n("./node_modules/lodash/_baseGetTag.js"),r=n("./node_modules/lodash/isObjectLike.js");e.exports=function(e){return"symbol"==typeof e||r(e)&&"[object Symbol]"==o(e)}},"./node_modules/lodash/isTypedArray.js":function(e,t,n){var o=n("./node_modules/lodash/_baseIsTypedArray.js"),r=n("./node_modules/lodash/_baseUnary.js"),i=n("./node_modules/lodash/_nodeUtil.js"),u=i&&i.isTypedArray,s=u?r(u):o;e.exports=s},"./node_modules/lodash/keys.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayLikeKeys.js"),r=n("./node_modules/lodash/_baseKeys.js"),i=n("./node_modules/lodash/isArrayLike.js");e.exports=function(e){return i(e)?o(e):r(e)}},"./node_modules/lodash/keysIn.js":function(e,t,n){var o=n("./node_modules/lodash/_arrayLikeKeys.js"),r=n("./node_modules/lodash/_baseKeysIn.js"),i=n("./node_modules/lodash/isArrayLike.js");e.exports=function(e){return i(e)?o(e,!0):r(e)}},"./node_modules/lodash/merge.js":function(e,t,n){var o=n("./node_modules/lodash/_baseMerge.js"),r=n("./node_modules/lodash/_createAssigner.js")((function(e,t,n){o(e,t,n)}));e.exports=r},"./node_modules/lodash/stubArray.js":function(e,t){e.exports=function(){return[]}},"./node_modules/lodash/stubFalse.js":function(e,t){e.exports=function(){return!1}},"./node_modules/lodash/toFinite.js":function(e,t,n){var o=n("./node_modules/lodash/toNumber.js");e.exports=function(e){return e?(e=o(e))===1/0||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}},"./node_modules/lodash/toInteger.js":function(e,t,n){var o=n("./node_modules/lodash/toFinite.js");e.exports=function(e){var t=o(e),n=t%1;return t==t?n?t-n:t:0}},"./node_modules/lodash/toNumber.js":function(e,t,n){var o=n("./node_modules/lodash/_baseTrim.js"),r=n("./node_modules/lodash/isObject.js"),i=n("./node_modules/lodash/isSymbol.js"),u=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,a=/^0o[0-7]+$/i,l=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return NaN;if(r(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=r(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=o(e);var n=s.test(e);return n||a.test(e)?l(e.slice(2),n?2:8):u.test(e)?NaN:+e}},"./node_modules/lodash/toPlainObject.js":function(e,t,n){var o=n("./node_modules/lodash/_copyObject.js"),r=n("./node_modules/lodash/keysIn.js");e.exports=function(e){return o(e,r(e))}},"./node_modules/lodash/toString.js":function(e,t,n){var o=n("./node_modules/lodash/_baseToString.js");e.exports=function(e){return null==e?"":o(e)}},"./node_modules/lodash/trim.js":function(e,t,n){var o=n("./node_modules/lodash/_baseToString.js"),r=n("./node_modules/lodash/_baseTrim.js"),i=n("./node_modules/lodash/_castSlice.js"),u=n("./node_modules/lodash/_charsEndIndex.js"),s=n("./node_modules/lodash/_charsStartIndex.js"),a=n("./node_modules/lodash/_stringToArray.js"),l=n("./node_modules/lodash/toString.js");e.exports=function(e,t,n){if((e=l(e))&&(n||void 0===t))return r(e);if(!e||!(t=o(t)))return e;var c=a(e),d=a(t),f=s(c,d),h=u(c,d)+1;return i(c,f,h).join("")}},"./node_modules/lodash/values.js":function(e,t,n){var o=n("./node_modules/lodash/_baseValues.js"),r=n("./node_modules/lodash/keys.js");e.exports=function(e){return null==e?[]:o(e,r(e))}},"./node_modules/webpack/buildin/global.js":function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(o){"object"==typeof window&&(n=window)}e.exports=n},"./node_modules/webpack/buildin/module.js":function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},"./src/namespace/cloudinary-core-shrinkwrap.js":function(e,t,n){"use strict";n.r(t),n.d(t,"ClientHintsMetaTag",(function(){return Zo})),n.d(t,"Cloudinary",(function(){return jr})),n.d(t,"Condition",(function(){return pt})),n.d(t,"Configuration",(function(){return wt})),n.d(t,"Expression",(function(){return st})),n.d(t,"crc32",(function(){return u})),n.d(t,"FetchLayer",(function(){return Kt})),n.d(t,"HtmlTag",(function(){return In})),n.d(t,"ImageTag",(function(){return co})),n.d(t,"Layer",(function(){return Et})),n.d(t,"PictureTag",(function(){return xo})),n.d(t,"SubtitlesLayer",(function(){return Nt})),n.d(t,"TextLayer",(function(){return Pt})),n.d(t,"Transformation",(function(){return Sn})),n.d(t,"utf8_encode",(function(){return i})),n.d(t,"Util",(function(){return r})),n.d(t,"VideoTag",(function(){return Wo}));var o={};n.r(o),n.d(o,"VERSION",(function(){return Y})),n.d(o,"CF_SHARED_CDN",(function(){return Q})),n.d(o,"OLD_AKAMAI_SHARED_CDN",(function(){return Z})),n.d(o,"AKAMAI_SHARED_CDN",(function(){return X})),n.d(o,"SHARED_CDN",(function(){return J})),n.d(o,"DEFAULT_TIMEOUT_MS",(function(){return ee})),n.d(o,"DEFAULT_POSTER_OPTIONS",(function(){return te})),n.d(o,"DEFAULT_VIDEO_SOURCE_TYPES",(function(){return ne})),n.d(o,"SEO_TYPES",(function(){return oe})),n.d(o,"DEFAULT_IMAGE_PARAMS",(function(){return re})),n.d(o,"DEFAULT_VIDEO_PARAMS",(function(){return ie})),n.d(o,"DEFAULT_VIDEO_SOURCES",(function(){return ue})),n.d(o,"DEFAULT_EXTERNAL_LIBRARIES",(function(){return se})),n.d(o,"PLACEHOLDER_IMAGE_MODES",(function(){return ae})),n.d(o,"ACCESSIBILITY_MODES",(function(){return le})),n.d(o,"URL_KEYS",(function(){return ce}));var r={};n.r(r),n.d(r,"getSDKAnalyticsSignature",(function(){return p})),n.d(r,"getAnalyticsOptions",(function(){return y})),n.d(r,"assign",(function(){return v.a})),n.d(r,"cloneDeep",(function(){return g.a})),n.d(r,"compact",(function(){return w.a})),n.d(r,"difference",(function(){return D.a})),n.d(r,"functions",(function(){return E.a})),n.d(r,"identity",(function(){return C.a})),n.d(r,"includes",(function(){return k.a})),n.d(r,"isArray",(function(){return F.a})),n.d(r,"isPlainObject",(function(){return T.a})),n.d(r,"isString",(function(){return R.a})),n.d(r,"merge",(function(){return z.a})),n.d(r,"contains",(function(){return k.a})),n.d(r,"isIntersectionObserverSupported",(function(){return G})),n.d(r,"isNativeLazyLoadSupported",(function(){return K})),n.d(r,"detectIntersection",(function(){return q})),n.d(r,"omit",(function(){return fe})),n.d(r,"allStrings",(function(){return pe})),n.d(r,"without",(function(){return me})),n.d(r,"isNumberLike",(function(){return ye})),n.d(r,"smartEscape",(function(){return _e})),n.d(r,"defaults",(function(){return ve})),n.d(r,"objectProto",(function(){return be})),n.d(r,"objToString",(function(){return ge})),n.d(r,"isObject",(function(){return je})),n.d(r,"funcTag",(function(){return we})),n.d(r,"reWords",(function(){return De})),n.d(r,"camelCase",(function(){return Oe})),n.d(r,"snakeCase",(function(){return Ee})),n.d(r,"convertKeys",(function(){return Be})),n.d(r,"withCamelCaseKeys",(function(){return Ce})),n.d(r,"withSnakeCaseKeys",(function(){return Se})),n.d(r,"base64Encode",(function(){return ke})),n.d(r,"base64EncodeURL",(function(){return xe})),n.d(r,"extractUrlParams",(function(){return Fe})),n.d(r,"patchFetchFormat",(function(){return Pe})),n.d(r,"optionConsume",(function(){return Te})),n.d(r,"isEmpty",(function(){return Ie})),n.d(r,"isAndroid",(function(){return Le})),n.d(r,"isEdge",(function(){return ze})),n.d(r,"isChrome",(function(){return Me})),n.d(r,"isSafari",(function(){return Ne})),n.d(r,"isElement",(function(){return N.a})),n.d(r,"isFunction",(function(){return U.a})),n.d(r,"trim",(function(){return W.a})),n.d(r,"getData",(function(){return Ve})),n.d(r,"setData",(function(){return Ue})),n.d(r,"getAttribute",(function(){return He})),n.d(r,"setAttribute",(function(){return We})),n.d(r,"removeAttribute",(function(){return $e})),n.d(r,"setAttributes",(function(){return Ge})),n.d(r,"hasClass",(function(){return Ke})),n.d(r,"addClass",(function(){return qe})),n.d(r,"getStyles",(function(){return Ye})),n.d(r,"cssExpand",(function(){return Qe})),n.d(r,"domStyle",(function(){return Ze})),n.d(r,"curCSS",(function(){return Xe})),n.d(r,"cssValue",(function(){return Je})),n.d(r,"augmentWidthOrHeight",(function(){return et})),n.d(r,"getWidthOrHeight",(function(){return nt})),n.d(r,"width",(function(){return ot}));var i=function(e){var t,n,o,r,i,u,s,a;if(null==e)return"";for(a="",i=void 0,o=void 0,0,i=o=0,s=(u=e+"").length,r=0;r<s;)n=null,(t=u.charCodeAt(r))<128?o++:n=t>127&&t<2048?String.fromCharCode(t>>6|192,63&t|128):String.fromCharCode(t>>12|224,t>>6&63|128,63&t|128),null!==n&&(o>i&&(a+=u.slice(i,o)),a+=n,i=o=r+1),r++;return o>i&&(a+=u.slice(i,s)),a};var u=function(e){var t,n,o,r;for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t=0,0,r=0,t^=-1,n=0,o=(e=i(e)).length;n<o;)r=255&(t^e.charCodeAt(n)),t=t>>>8^"0x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substr(9*r,8),n++;return(t^=-1)<0&&(t+=4294967296),t};function s(e,t,n){return t>>=0,n=String(void 0!==n?n:" "),e.length>t?String(e):((t-=e.length)>n.length&&(n+=function(e,t){var n="";for(;t>0;)n+=e,t--;return n}(n,t/n.length)),n.slice(0,t)+String(e))}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var l,c=0,d={};(l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",function(e){if(Array.isArray(e))return a(e)}(l)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(l)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,t):void 0}}(l)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).forEach((function(e){var t=c.toString(2);t=s(t,6,"0"),d[t]=e,c++}));var f=d;function h(e){var t="",n=6*e.split(".").length,o=function(e){if(e.split(".").length<2)throw new Error("invalid semVer, must have at least two segments");return e.split(".").reverse().map((function(e){return s(e,2,"0")})).join(".")}(e),r=parseInt(o.split(".").join("")).toString(2);if((r=s(r,n,"0")).length%6!=0)throw"Version must be smaller than 43.21.26)";return r.match(/.{1,6}/g).forEach((function(e){t+=f[e]})),t}function p(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{var t=m(e.techVersion),n=h(e.sdkSemver),o=h(t),r=e.feature,i=e.sdkCode,u="A";return"".concat(u).concat(i).concat(n).concat(o).concat(r)}catch(s){return"E"}}function m(e){var t=e.split(".");return"".concat(t[0],".").concat(t[1])}function y(e){var t={sdkSemver:e.sdkSemver,techVersion:e.techVersion,sdkCode:e.sdkCode,feature:"0"};return e.urlAnalytics?(e.accessibility&&(t.feature="D"),"lazy"===e.loading&&(t.feature="C"),e.responsive&&(t.feature="A"),e.placeholder&&(t.feature="B"),t):{}}var _=n("./node_modules/lodash/assign.js"),v=n.n(_),b=n("./node_modules/lodash/cloneDeep.js"),g=n.n(b),j=n("./node_modules/lodash/compact.js"),w=n.n(j),A=n("./node_modules/lodash/difference.js"),D=n.n(A),O=n("./node_modules/lodash/functions.js"),E=n.n(O),B=n("./node_modules/lodash/identity.js"),C=n.n(B),S=n("./node_modules/lodash/includes.js"),k=n.n(S),x=n("./node_modules/lodash/isArray.js"),F=n.n(x),P=n("./node_modules/lodash/isPlainObject.js"),T=n.n(P),I=n("./node_modules/lodash/isString.js"),R=n.n(I),L=n("./node_modules/lodash/merge.js"),z=n.n(L),M=n("./node_modules/lodash/isElement.js"),N=n.n(M),V=n("./node_modules/lodash/isFunction.js"),U=n.n(V),H=n("./node_modules/lodash/trim.js"),W=n.n(H);function $(e){return($="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function G(){return"object"===("undefined"==typeof window?"undefined":$(window))&&window.IntersectionObserver}function K(){return"object"===("undefined"==typeof HTMLImageElement?"undefined":$(HTMLImageElement))&&HTMLImageElement.prototype.loading}function q(e,t){try{if(K()||!G())return void t();var n=new IntersectionObserver((function(e){e.forEach((function(e){e.isIntersecting&&(t(),n.unobserve(e.target))}))}),{threshold:[0,.01]});n.observe(e)}catch(o){t()}}var Y="2.5.0",Q="d3jpl91pxevbkh.cloudfront.net",Z="cloudinary-a.akamaihd.net",X="res.cloudinary.com",J=X,ee=1e4,te={format:"jpg",resource_type:"video"},ne=["webm","mp4","ogv"],oe={"image/upload":"images","image/private":"private_images","image/authenticated":"authenticated_images","raw/upload":"files","video/upload":"videos"},re={resource_type:"image",transformation:[],type:"upload"},ie={fallback_content:"",resource_type:"video",source_transformation:{},source_types:ne,transformation:[],type:"upload"},ue=[{type:"mp4",codecs:"hev1",transformations:{video_codec:"h265"}},{type:"webm",codecs:"vp9",transformations:{video_codec:"vp9"}},{type:"mp4",transformations:{video_codec:"auto"}},{type:"webm",transformations:{video_codec:"auto"}}],se={seeThru:"https://unpkg.com/seethru@4/dist/seeThru.min.js"},ae={blur:[{effect:"blur:2000",quality:1,fetch_format:"auto"}],pixelate:[{effect:"pixelate",quality:1,fetch_format:"auto"}],"predominant-color-pixel":[{width:"iw_div_2",aspect_ratio:1,crop:"pad",background:"auto"},{crop:"crop",width:1,height:1,gravity:"north_east"},{fetch_format:"auto",quality:"auto"}],"predominant-color":[{variables:[["$currWidth","w"],["$currHeight","h"]]},{width:"iw_div_2",aspect_ratio:1,crop:"pad",background:"auto"},{crop:"crop",width:10,height:10,gravity:"north_east"},{width:"$currWidth",height:"$currHeight",crop:"fill"},{fetch_format:"auto",quality:"auto"}],vectorize:[{effect:"vectorize:3:0.1",fetch_format:"svg"}]},le={darkmode:"tint:75:black",brightmode:"tint:50:white",monochrome:"grayscale",colorblind:"assist_colorblind"},ce=["accessibility","api_secret","auth_token","cdn_subdomain","cloud_name","cname","format","placeholder","private_cdn","resource_type","secure","secure_cdn_subdomain","secure_distribution","shorten","sign_url","signature","ssl_detected","type","url_suffix","use_root_path","version"];function de(e){return(de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fe(e,t){e=e||{};var n=Object.keys(e).filter((function(e){return!k()(t,e)})),o={};return n.forEach((function(t){return o[t]=e[t]})),o}var he,pe=function(e){return e.length&&e.every(R.a)},me=function(e,t){return e.filter((function(e){return e!==t}))},ye=function(e){return null!=e&&!isNaN(parseFloat(e))},_e=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:/([^a-zA-Z0-9_.\-\/:]+)/g;return e.replace(t,(function(e){return e.split("").map((function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})).join("")}))},ve=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return n.reduce((function(e,t){var n,o;for(n in t)o=t[n],void 0===e[n]&&(e[n]=o);return e}),e)},be=Object.prototype,ge=be.toString,je=function(e){var t;return t=de(e),!!e&&("object"===t||"function"===t)},we="[object Function]",Ae=function(e){return je(e)&&ge.call(e)===we},De=RegExp("[A-Z]+(?=[A-Z][a-z]+)|[A-Z]?[a-z]+|[A-Z]+|[0-9]+","g"),Oe=function(e){var t=e.match(De);return(t=t.map((function(e){return e.charAt(0).toLocaleUpperCase()+e.slice(1).toLocaleLowerCase()})))[0]=t[0].toLocaleLowerCase(),t.join("")},Ee=function(e){var t=e.match(De);return(t=t.map((function(e){return e.toLocaleLowerCase()}))).join("_")},Be=function(e,t){var n,o;for(var r in n={},e)o=e[r],t&&(r=t(r)),Ie(r)||(n[r]=o);return n},Ce=function(e){return Be(e,Oe)},Se=function(e){return Be(e,Ee)},ke="undefined"!=typeof btoa&&Ae(btoa)?btoa:"undefined"!=typeof Buffer&&Ae(Buffer)?function(e){return e instanceof Buffer||(e=new Buffer.from(String(e),"binary")),e.toString("base64")}:function(e){throw new Error("No base64 encoding function found")},xe=function(e){try{e=decodeURI(e)}finally{e=encodeURI(e)}return ke(e)};function Fe(e){return ce.reduce((function(t,n){return null!=e[n]&&(t[n]=e[n]),t}),{})}function Pe(e){null==e&&(e={}),"fetch"===e.type&&null==e.fetch_format&&(e.fetch_format=Te(e,"format"))}function Te(e,t,n){var o=e[t];return delete e[t],null!=o?o:n}function Ie(e){if(null==e)return!0;if("number"==typeof e.length)return 0===e.length;if("number"==typeof e.size)return 0===e.size;if("object"==de(e)){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}return!0}function Re(){return navigator&&navigator.userAgent||""}function Le(){var e=Re();return/Android/i.test(e)}function ze(){var e=Re();return/Edg/i.test(e)}function Me(){var e=Re();return!ze()&&(/Chrome/i.test(e)||/CriOS/i.test(e))}function Ne(){var e=Re();return/Safari/i.test(e)&&!Me()&&!Le()&&!ze()}var Ve=function(e,t){switch(!1){case!(null==e):return;case!U()(e.getAttribute):return e.getAttribute("data-".concat(t));case!U()(e.getAttr):return e.getAttr("data-".concat(t));case!U()(e.data):return e.data(t);case!(U()("undefined"!=typeof jQuery&&jQuery.fn&&jQuery.fn.data)&&N()(e)):return jQuery(e).data(t)}},Ue=function(e,t,n){switch(!1){case!(null==e):return;case!U()(e.setAttribute):return e.setAttribute("data-".concat(t),n);case!U()(e.setAttr):return e.setAttr("data-".concat(t),n);case!U()(e.data):return e.data(t,n);case!(U()("undefined"!=typeof jQuery&&jQuery.fn&&jQuery.fn.data)&&N()(e)):return jQuery(e).data(t,n)}},He=function(e,t){switch(!1){case!(null==e):return;case!U()(e.getAttribute):return e.getAttribute(t);case!U()(e.attr):return e.attr(t);case!U()(e.getAttr):return e.getAttr(t)}},We=function(e,t,n){switch(!1){case!(null==e):return;case!U()(e.setAttribute):return e.setAttribute(t,n);case!U()(e.attr):return e.attr(t,n);case!U()(e.setAttr):return e.setAttr(t,n)}},$e=function(e,t){switch(!1){case!(null==e):return;case!U()(e.removeAttribute):return e.removeAttribute(t);default:return We(e,void 0)}},Ge=function(e,t){var n,o,r;for(n in o=[],t)null!=(r=t[n])?o.push(We(e,n,r)):o.push($e(e,n));return o},Ke=function(e,t){if(N()(e))return e.className.match(new RegExp("\\b".concat(t,"\\b")))},qe=function(e,t){if(!e.className.match(new RegExp("\\b".concat(t,"\\b"))))return e.className=W()("".concat(e.className," ").concat(t))},Ye=function(e){return e.ownerDocument.defaultView.opener?e.ownerDocument.defaultView.getComputedStyle(e,null):window.getComputedStyle(e,null)},Qe=["Top","Right","Bottom","Left"];he=function(e,t){var n,o;return n=9===e.nodeType?e.documentElement:e,e===(o=t&&t.parentNode)||!(!o||1!==o.nodeType||!n.contains(o))};var Ze=function(e,t){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style)return e.style[t]},Xe=function(e,t,n){var o,r,i,u,s,a;return u=/^margin/,a=void 0,r=void 0,o=void 0,i=void 0,s=e.style,(n=n||Ye(e))&&(i=n.getPropertyValue(t)||n[t]),n&&(""!==i||he(e.ownerDocument,e)||(i=Ze(e,t)),tt.test(i)&&u.test(t)&&(a=s.width,r=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=i,i=n.width,s.width=a,s.minWidth=r,s.maxWidth=o)),void 0!==i?i+"":i},Je=function(e,t,n,o){var r;return r=Xe(e,t,o),n?parseFloat(r):r},et=function(e,t,n,o,r){var i,u,s,a,l;if(n===(o?"border":"content"))return 0;for(l=0,i=0,u=(a="width"===t?["Right","Left"]:["Top","Bottom"]).length;i<u;i++)s=a[i],"margin"===n&&(l+=Je(e,n+s,!0,r)),o?("content"===n&&(l-=Je(e,"padding".concat(s),!0,r)),"margin"!==n&&(l-=Je(e,"border".concat(s,"Width"),!0,r))):(l+=Je(e,"padding".concat(s),!0,r),"padding"!==n&&(l+=Je(e,"border".concat(s,"Width"),!0,r)));return l},tt=new RegExp("^("+/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source+")(?!px)[a-z%]+$","i"),nt=function(e,t,n){var o,r,i,u;if(u=!0,i="width"===t?e.offsetWidth:e.offsetHeight,r=Ye(e),o="border-box"===Je(e,"boxSizing",!1,r),i<=0||null==i){if(((i=Xe(e,t,r))<0||null==i)&&(i=e.style[t]),tt.test(i))return i;u=o&&i===e.style[t],i=parseFloat(i)||0}return i+et(e,t,n||(o?"border":"content"),u,r)},ot=function(e){return nt(e,"width","content")};function rt(e){return(rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function it(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,ut(o.key),o)}}function ut(e){var t=function(e,t){if("object"!=rt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=rt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==rt(t)?t:t+""}var Expression=function(){function Expression(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Expression),this.expressions=[],null!=e&&this.expressions.push(Expression.normalize(e))}return function(e,t,n){return t&&it(e.prototype,t),n&&it(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(Expression,[{key:"serialize",value:function(){return Expression.normalize(this.expressions.join("_"))}},{key:"toString",value:function(){return this.serialize()}},{key:"getParent",value:function(){return this.parent}},{key:"setParent",value:function(e){return this.parent=e,this}},{key:"predicate",value:function(e,t,n){return null!=Expression.OPERATORS[t]&&(t=Expression.OPERATORS[t]),this.expressions.push("".concat(e,"_").concat(t,"_").concat(n)),this}},{key:"and",value:function(){return this.expressions.push("and"),this}},{key:"or",value:function(){return this.expressions.push("or"),this}},{key:"then",value:function(){return this.getParent().if(this.toString())}},{key:"height",value:function(e,t){return this.predicate("h",e,t)}},{key:"width",value:function(e,t){return this.predicate("w",e,t)}},{key:"aspectRatio",value:function(e,t){return this.predicate("ar",e,t)}},{key:"pageCount",value:function(e,t){return this.predicate("pc",e,t)}},{key:"faceCount",value:function(e,t){return this.predicate("fc",e,t)}},{key:"value",value:function(e){return this.expressions.push(e),this}}],[{key:"new",value:function(e){return new this(e)}},{key:"normalize",value:function(e){if(null==e)return e;e=String(e);var t=new RegExp("((\\|\\||>=|<=|&&|!=|>|=|<|/|-|\\+|\\*|\\^)(?=[ _]))","g");e=e.replace(t,(function(e){return Expression.OPERATORS[e]}));var n="("+Object.keys(Expression.PREDEFINED_VARS).map((function(e){return":".concat(e,"|").concat(e)})).join("|")+")",o=new RegExp("".concat("(\\$_*[^_ ]+)","|").concat(n),"g");return(e=e.replace(o,(function(e){return Expression.PREDEFINED_VARS[e]||e}))).replace(/[ _]+/g,"_")}},{key:"variable",value:function(e,t){return new this(e).value(t)}},{key:"width",value:function(){return new this("width")}},{key:"height",value:function(){return new this("height")}},{key:"initialWidth",value:function(){return new this("initialWidth")}},{key:"initialHeight",value:function(){return new this("initialHeight")}},{key:"aspectRatio",value:function(){return new this("aspectRatio")}},{key:"initialAspectRatio",value:function(){return new this("initialAspectRatio")}},{key:"pageCount",value:function(){return new this("pageCount")}},{key:"faceCount",value:function(){return new this("faceCount")}},{key:"currentPage",value:function(){return new this("currentPage")}},{key:"tags",value:function(){return new this("tags")}},{key:"pageX",value:function(){return new this("pageX")}},{key:"pageY",value:function(){return new this("pageY")}}])}();Expression.OPERATORS={"=":"eq","!=":"ne","<":"lt",">":"gt","<=":"lte",">=":"gte","&&":"and","||":"or","*":"mul","/":"div","+":"add","-":"sub","^":"pow"},Expression.PREDEFINED_VARS={aspect_ratio:"ar",aspectRatio:"ar",current_page:"cp",currentPage:"cp",duration:"du",face_count:"fc",faceCount:"fc",height:"h",initial_aspect_ratio:"iar",initial_duration:"idu",initial_height:"ih",initial_width:"iw",initialAspectRatio:"iar",initialDuration:"idu",initialHeight:"ih",initialWidth:"iw",page_count:"pc",page_x:"px",page_y:"py",pageCount:"pc",pageX:"px",pageY:"py",tags:"tags",width:"w"},Expression.BOUNDRY="[ _]+";var st=Expression;function at(e){return(at="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function lt(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,ct(o.key),o)}}function ct(e){var t=function(e,t){if("object"!=at(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=at(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==at(t)?t:t+""}function dt(e,t,n){return t=ft(t),function(e,t){if(t&&("object"==at(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],ft(e).constructor):t.apply(e,n))}function ft(e){return(ft=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ht(e,t){return(ht=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var pt=function(e){function Condition(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Condition),dt(this,Condition,[e])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ht(e,t)}(Condition,e),function(e,t,n){return t&&lt(e.prototype,t),n&&lt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(Condition,[{key:"height",value:function(e,t){return this.predicate("h",e,t)}},{key:"width",value:function(e,t){return this.predicate("w",e,t)}},{key:"aspectRatio",value:function(e,t){return this.predicate("ar",e,t)}},{key:"pageCount",value:function(e,t){return this.predicate("pc",e,t)}},{key:"faceCount",value:function(e,t){return this.predicate("fc",e,t)}},{key:"duration",value:function(e,t){return this.predicate("du",e,t)}},{key:"initialDuration",value:function(e,t){return this.predicate("idu",e,t)}}])}(st);function mt(e){return(mt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,u,s=[],a=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(o=i.call(n)).done)&&(s.push(o.value),s.length!==t);a=!0);}catch(e){l=!0,r=e}finally{try{if(!a&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(l)throw r}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return _t(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_t(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function vt(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,bt(o.key),o)}}function bt(e){var t=function(e,t){if("object"!=mt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=mt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mt(t)?t:t+""}var gt=function(){return function(e,t,n){return t&&vt(e.prototype,t),n&&vt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}((function Configuration(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Configuration),this.configuration=null==e?{}:g()(e),ve(this.configuration,jt)}),[{key:"init",value:function(){return this.fromEnvironment(),this.fromDocument(),this}},{key:"set",value:function(e,t){return this.configuration[e]=t,this}},{key:"get",value:function(e){return this.configuration[e]}},{key:"merge",value:function(e){return v()(this.configuration,g()(e)),this}},{key:"fromDocument",value:function(){var e,t,n,o;if(o="undefined"!=typeof document&&null!==document?document.querySelectorAll('meta[name^="cloudinary_"]'):void 0)for(t=0,n=o.length;t<n;t++)e=o[t],this.configuration[e.getAttribute("name").replace("cloudinary_","")]=e.getAttribute("content");return this}},{key:"fromEnvironment",value:function(){var e,t,n,o=this;return"undefined"!=typeof process&&null!==process&&process.env&&process.env.CLOUDINARY_URL&&(e=process.env.CLOUDINARY_URL,(n=/cloudinary:\/\/(?:(\w+)(?:\:([\w-]+))?@)?([\w\.-]+)(?:\/([^?]*))?(?:\?(.+))?/.exec(e))&&(null!=n[3]&&(this.configuration.cloud_name=n[3]),null!=n[1]&&(this.configuration.api_key=n[1]),null!=n[2]&&(this.configuration.api_secret=n[2]),null!=n[4]&&(this.configuration.private_cdn=null!=n[4]),null!=n[4]&&(this.configuration.secure_distribution=n[4]),null!=(t=n[5])&&t.split("&").forEach((function(e){var t=yt(e.split("="),2),n=t[0],r=t[1];null==r&&(r=!0),o.configuration[n]=r})))),this}},{key:"config",value:function(e,t){switch(!1){case void 0===t:return this.set(e,t),this.configuration;case!R()(e):return this.get(e);case!T()(e):return this.merge(e),this.configuration;default:return this.configuration}}},{key:"toOptions",value:function(){return g()(this.configuration)}}])}(),jt={responsive_class:"cld-responsive",responsive_use_breakpoints:!0,round_dpr:!0,secure:"https:"===("undefined"!=typeof window&&null!==window&&window.location?window.location.protocol:void 0)};gt.CONFIG_PARAMS=["api_key","api_secret","callback","cdn_subdomain","cloud_name","cname","private_cdn","protocol","resource_type","responsive","responsive_class","responsive_use_breakpoints","responsive_width","round_dpr","secure","secure_cdn_subdomain","secure_distribution","shorten","type","upload_preset","url_suffix","use_root_path","version","externalLibraries","max_timeout_ms"];var wt=gt;function At(e){return(At="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Dt(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Ot(o.key),o)}}function Ot(e){var t=function(e,t){if("object"!=At(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=At(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==At(t)?t:t+""}var Et=function(){return function(e,t,n){return t&&Dt(e.prototype,t),n&&Dt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}((function Layer(e){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Layer),this.options={},null!=e&&["resourceType","type","publicId","format"].forEach((function(n){var o;return t.options[n]=null!=(o=e[n])?o:e[Ee(n)]}))}),[{key:"resourceType",value:function(e){return this.options.resourceType=e,this}},{key:"type",value:function(e){return this.options.type=e,this}},{key:"publicId",value:function(e){return this.options.publicId=e,this}},{key:"getPublicId",value:function(){var e;return null!=(e=this.options.publicId)?e.replace(/\//g,":"):void 0}},{key:"getFullPublicId",value:function(){return null!=this.options.format?this.getPublicId()+"."+this.options.format:this.getPublicId()}},{key:"format",value:function(e){return this.options.format=e,this}},{key:"toString",value:function(){var e;if(e=[],null==this.options.publicId)throw"Must supply publicId";return"image"!==this.options.resourceType&&e.push(this.options.resourceType),"upload"!==this.options.type&&e.push(this.options.type),e.push(this.getFullPublicId()),w()(e).join(":")}},{key:"clone",value:function(){return new this.constructor(this.options)}}])}();function Bt(e){return(Bt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ct(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,St(o.key),o)}}function St(e){var t=function(e,t){if("object"!=Bt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Bt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Bt(t)?t:t+""}function kt(e,t,n){return t=xt(t),function(e,t){if(t&&("object"==Bt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],xt(e).constructor):t.apply(e,n))}function xt(e){return(xt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Ft(e,t){return(Ft=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var Pt=function(e){function TextLayer(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,TextLayer),t=kt(this,TextLayer,[e]),null!=e&&["resourceType","resourceType","fontFamily","fontSize","fontWeight","fontStyle","textDecoration","textAlign","stroke","letterSpacing","lineSpacing","fontHinting","fontAntialiasing","text","textStyle"].forEach((function(n){var o;return t.options[n]=null!=(o=e[n])?o:e[Ee(n)]})),t.options.resourceType="text",t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ft(e,t)}(TextLayer,e),function(e,t,n){return t&&Ct(e.prototype,t),n&&Ct(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(TextLayer,[{key:"resourceType",value:function(e){throw"Cannot modify resourceType for text layers"}},{key:"type",value:function(e){throw"Cannot modify type for text layers"}},{key:"format",value:function(e){throw"Cannot modify format for text layers"}},{key:"fontFamily",value:function(e){return this.options.fontFamily=e,this}},{key:"fontSize",value:function(e){return this.options.fontSize=e,this}},{key:"fontWeight",value:function(e){return this.options.fontWeight=e,this}},{key:"fontStyle",value:function(e){return this.options.fontStyle=e,this}},{key:"textDecoration",value:function(e){return this.options.textDecoration=e,this}},{key:"textAlign",value:function(e){return this.options.textAlign=e,this}},{key:"stroke",value:function(e){return this.options.stroke=e,this}},{key:"letterSpacing",value:function(e){return this.options.letterSpacing=e,this}},{key:"lineSpacing",value:function(e){return this.options.lineSpacing=e,this}},{key:"fontHinting",value:function(e){return this.options.fontHinting=e,this}},{key:"fontAntialiasing",value:function(e){return this.options.fontAntialiasing=e,this}},{key:"text",value:function(e){return this.options.text=e,this}},{key:"textStyle",value:function(e){return this.options.textStyle=e,this}},{key:"toString",value:function(){var e,t,n,o,r,i,u,s,a,l;if(s=this.textStyleIdentifier(),null!=this.options.publicId&&(o=this.getFullPublicId()),null!=this.options.text){if(t=!Ie(o),n=!Ie(s),t&&n||!t&&!n)throw"Must supply either style parameters or a public_id when providing text parameter in a text overlay/underlay, but not both!";for(r=/\$\([a-zA-Z]\w*\)/g,u=0,l=_e(this.options.text,/[,\/]/g),a="";i=r.exec(l);)a+=_e(l.slice(u,i.index)),a+=i[0],u=i.index+i[0].length;a+=_e(l.slice(u))}return e=[this.options.resourceType,s,o,a],w()(e).join(":")}},{key:"textStyleIdentifier",value:function(){if(!Ie(this.options.textStyle))return this.options.textStyle;var e;if(e=[],"normal"!==this.options.fontWeight&&e.push(this.options.fontWeight),"normal"!==this.options.fontStyle&&e.push(this.options.fontStyle),"none"!==this.options.textDecoration&&e.push(this.options.textDecoration),e.push(this.options.textAlign),"none"!==this.options.stroke&&e.push(this.options.stroke),Ie(this.options.letterSpacing)&&!ye(this.options.letterSpacing)||e.push("letter_spacing_"+this.options.letterSpacing),Ie(this.options.lineSpacing)&&!ye(this.options.lineSpacing)||e.push("line_spacing_"+this.options.lineSpacing),Ie(this.options.fontAntialiasing)||e.push("antialias_"+this.options.fontAntialiasing),Ie(this.options.fontHinting)||e.push("hinting_"+this.options.fontHinting),!Ie(w()(e))){if(Ie(this.options.fontFamily))throw"Must supply fontFamily. ".concat(e);if(Ie(this.options.fontSize)&&!ye(this.options.fontSize))throw"Must supply fontSize."}return e.unshift(this.options.fontFamily,this.options.fontSize),e=w()(e).join("_")}}])}(Et);function Tt(e){return(Tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function It(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Rt(o.key),o)}}function Rt(e){var t=function(e,t){if("object"!=Tt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Tt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Tt(t)?t:t+""}function Lt(e,t,n){return t=zt(t),function(e,t){if(t&&("object"==Tt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],zt(e).constructor):t.apply(e,n))}function zt(e){return(zt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Mt(e,t){return(Mt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var Nt=function(e){function SubtitlesLayer(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,SubtitlesLayer),(t=Lt(this,SubtitlesLayer,[e])).options.resourceType="subtitles",t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mt(e,t)}(SubtitlesLayer,e),function(e,t,n){return t&&It(e.prototype,t),n&&It(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(SubtitlesLayer)}(Pt);function Vt(e){return(Vt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ut(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Ht(o.key),o)}}function Ht(e){var t=function(e,t){if("object"!=Vt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Vt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Vt(t)?t:t+""}function Wt(e,t,n){return t=$t(t),function(e,t){if(t&&("object"==Vt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],$t(e).constructor):t.apply(e,n))}function $t(e){return($t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Gt(e,t){return(Gt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var Kt=function(e){function FetchLayer(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,FetchLayer),t=Wt(this,FetchLayer,[e]),R()(e)?t.options.url=e:(null!=e?e.url:void 0)&&(t.options.url=e.url),t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gt(e,t)}(FetchLayer,e),function(e,t,n){return t&&Ut(e.prototype,t),n&&Ut(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(FetchLayer,[{key:"url",value:function(e){return this.options.url=e,this}},{key:"toString",value:function(){return"fetch:".concat(xe(this.options.url))}}])}(Et);function qt(e,t,n){return t=Xt(t),function(e,t){if(t&&("object"==tn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],Xt(e).constructor):t.apply(e,n))}function Yt(e,t,n,o){var r=Qt(Xt(1&o?e.prototype:e),t,n);return 2&o&&"function"==typeof r?function(e){return r.apply(n,e)}:r}function Qt(){return(Qt="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=Zt(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}}).apply(null,arguments)}function Zt(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=Xt(e)););return e}function Xt(e){return(Xt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Jt(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&en(e,t)}function en(e,t){return(en=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tn(e){return(tn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function on(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,un(o.key),o)}}function rn(e,t,n){return t&&on(e.prototype,t),n&&on(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function un(e){var t=function(e,t){if("object"!=tn(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=tn(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tn(t)?t:t+""}var sn=function(){return rn((function e(t,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:C.a;nn(this,e),this.name=t,this.shortName=n,this.process=o}),[{key:"set",value:function(e){return this.origValue=e,this}},{key:"serialize",value:function(){var e,t;return e=this.value(),t=F()(e)||T()(e)||R()(e)?!Ie(e):null!=e,null!=this.shortName&&t?"".concat(this.shortName,"_").concat(e):""}},{key:"value",value:function(){return this.process(this.origValue)}}],[{key:"norm_color",value:function(e){return null!=e?e.replace(/^#/,"rgb:"):void 0}},{key:"build_array",value:function(e){return null==e?[]:F()(e)?e:[e]}},{key:"process_video_params",value:function(e){var t;switch(e.constructor){case Object:return t="","codec"in e&&(t=e.codec,"profile"in e&&(t+=":"+e.profile,"level"in e&&(t+=":"+e.level,"b_frames"in e&&!1===e.b_frames&&(t+=":bframes_no")))),t;case String:return e;default:return null}}}])}(),an=function(e){function t(e,n){var o,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".",i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;return nn(this,t),(o=qt(this,t,[e,n,i])).sep=r,o}return Jt(t,e),rn(t,[{key:"serialize",value:function(){if(null!=this.shortName){var e=this.value();if(Ie(e))return"";if(R()(e))return"".concat(this.shortName,"_").concat(e);var t=e.map((function(e){return U()(e.serialize)?e.serialize():e})).join(this.sep);return"".concat(this.shortName,"_").concat(t)}return""}},{key:"value",value:function(){var e=this;return F()(this.origValue)?this.origValue.map((function(t){return e.process(t)})):this.process(this.origValue)}},{key:"set",value:function(e){return null==e||F()(e)?Yt(t,"set",this,3)([e]):Yt(t,"set",this,3)([[e]])}}])}(sn),ln=function(e){function t(e){var n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"t",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".",i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;return nn(this,t),(n=qt(this,t,[e,o,i])).sep=r,n}return Jt(t,e),rn(t,[{key:"serialize",value:function(){var e=this,t="",n=this.value();if(Ie(n))return t;if(pe(n)){var o=n.join(this.sep);Ie(o)||(t="".concat(this.shortName,"_").concat(o))}else t=n.map((function(t){return R()(t)&&!Ie(t)?"".concat(e.shortName,"_").concat(t):U()(t.serialize)?t.serialize():T()(t)&&!Ie(t)?new Sn(t).serialize():void 0})).filter((function(e){return e}));return t}},{key:"set",value:function(e){return this.origValue=e,F()(this.origValue)?Yt(t,"set",this,3)([this.origValue]):Yt(t,"set",this,3)([[this.origValue]])}}])}(sn),cn=function(e){function t(e,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.norm_range_value;return nn(this,t),qt(this,t,[e,n,o])}return Jt(t,e),rn(t,null,[{key:"norm_range_value",value:function(e){var t=String(e).match(new RegExp("^(([0-9]*)\\.([0-9]+)|([0-9]+))([%pP])?$"));if(t){var n=null!=t[5]?"p":"";e=(t[1]||t[4])+n}return st.normalize(e)}}])}(sn),dn=function(e){function t(e,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:C.a;return nn(this,t),qt(this,t,[e,n,o])}return Jt(t,e),rn(t,[{key:"serialize",value:function(){return this.value()}}])}(sn),fn=function(e){function t(){return nn(this,t),qt(this,t,arguments)}return Jt(t,e),rn(t,[{key:"value",value:function(){if(null==this.origValue)return"";var e;if(this.origValue instanceof Et)e=this.origValue;else if(T()(this.origValue)){var t=Ce(this.origValue);e="text"===t.resourceType||null!=t.text?new Pt(t):"subtitles"===t.resourceType?new Nt(t):"fetch"===t.resourceType||null!=t.url?new Kt(t):new Et(t)}else e=R()(this.origValue)?/^fetch:.+/.test(this.origValue)?new Kt(this.origValue.substr(6)):this.origValue:"";return e.toString()}}],[{key:"textStyle",value:function(e){return new Pt(e).textStyleIdentifier()}}])}(sn);function hn(e,t,n){return t=pn(t),function(e,t){if(t&&("object"==vn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],pn(e).constructor):t.apply(e,n))}function pn(e){return(pn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function mn(e,t){return(mn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,u,s=[],a=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(o=i.call(n)).done)&&(s.push(o.value),s.length!==t);a=!0);}catch(e){l=!0,r=e}finally{try{if(!a&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(l)throw r}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return _n(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_n(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function vn(e){return(vn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function bn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function gn(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,wn(o.key),o)}}function jn(e,t,n){return t&&gn(e.prototype,t),n&&gn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function wn(e){var t=function(e,t){if("object"!=vn(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=vn(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==vn(t)?t:t+""}function An(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return n.forEach((function(t){Object.keys(t).forEach((function(n){null!=t[n]&&(e[n]=t[n])}))})),e}var Dn=function(){function e(t){var n,o;bn(this,e),n=void 0,o={},this.toOptions=function(e){var t={};if(null==e&&(e=!0),Object.keys(o).forEach((function(e){return t[e]=o[e].origValue})),An(t,this.otherOptions),e&&!Ie(this.chained)){var n=this.chained.map((function(e){return e.toOptions()}));n.push(t),An(t={},this.otherOptions),t.transformation=n}return t},this.setParent=function(e){return n=e,null!=e&&this.fromOptions("function"==typeof e.toOptions?e.toOptions():void 0),this},this.getParent=function(){return n},this.param=function(e,t,n,r,i){return null==i&&(i=U()(r)?r:C.a),o[t]=new sn(t,n,i).set(e),this},this.rawParam=function(e,t,n,r,i){return i=En(arguments),o[t]=new dn(t,n,i).set(e),this},this.rangeParam=function(e,t,n,r,i){return i=En(arguments),o[t]=new cn(t,n,i).set(e),this},this.arrayParam=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:":",i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:void 0;return i=En(arguments),o[t]=new an(t,n,r,i).set(e),this},this.transformationParam=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:void 0;return i=En(arguments),o[t]=new ln(t,n,r,i).set(e),this},this.layerParam=function(e,t,n){return o[t]=new fn(t,n).set(e),this},this.getValue=function(e){var t=o[e]&&o[e].value();return null!=t?t:this.otherOptions[e]},this.get=function(e){return o[e]},this.remove=function(e){var t;switch(!1){case null==o[e]:return t=o[e],delete o[e],t.origValue;case null==this.otherOptions[e]:return t=this.otherOptions[e],delete this.otherOptions[e],t;default:return null}},this.keys=function(){var e;return function(){var t;for(e in t=[],o)null!=e&&t.push(e.match(On)?e:Ee(e));return t}().sort()},this.toPlainObject=function(){var e,t,n;for(t in e={},o)e[t]=o[t].value(),T()(e[t])&&(e[t]=g()(e[t]));return Ie(this.chained)||((n=this.chained.map((function(e){return e.toPlainObject()}))).push(e),e={transformation:n}),e},this.chain=function(){var e;return 0!==Object.getOwnPropertyNames(o).length&&(e=new this.constructor(this.toOptions(!1)),this.resetTransformations(),this.chained.push(e)),this},this.resetTransformations=function(){return o={},this},this.otherOptions={},this.chained=[],this.fromOptions(t)}return jn(e,[{key:"fromOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(t instanceof e)this.fromTransformation(t);else for(var n in(R()(t)||F()(t))&&(t={transformation:t}),(t=g()(t,(function(t){if(t instanceof e||t instanceof Layer)return new t.clone}))).if&&(this.set("if",t.if),delete t.if),t){var o=t[n];null!=o&&(n.match(On)?"$attr"!==n&&this.set("variable",n,o):this.set(n,o))}return this}},{key:"fromTransformation",value:function(t){var n=this;return t instanceof e&&t.keys().forEach((function(e){return n.set(e,t.get(e).origValue)})),this}},{key:"set",value:function(e){var t;t=Oe(e);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return k()(Cn.methods,t)?this[t].apply(this,o):this.otherOptions[e]=o[0],this}},{key:"hasLayer",value:function(){return this.getValue("overlay")||this.getValue("underlay")}},{key:"serialize",value:function(){var e,t,n,o,r,i,u,s,a,l,c,d,f,h,p,m,y;for(l=this.chained.map((function(e){return e.serialize()})),o=this.keys(),h=null!=(r=this.get("transformation"))?r.serialize():void 0,e=null!=(i=this.get("if"))?i.serialize():void 0,m=function(e){var t,n,o,r,i;if(F()(e)){for(r=[],t=0,n=e.length;t<n;t++){var u=yn(e[t],2);o=u[0],i=u[1],r.push("".concat(o,"_").concat(st.normalize(i)))}return r}return e}(null!=(u=this.get("variables"))?u.value():void 0),o=D()(o,["transformation","if","variables"]),y=[],d=[],t=0,n=o.length;t<n;t++)(c=o[t]).match(On)?y.push(c+"_"+st.normalize(null!=(s=this.get(c))?s.value():void 0)):d.push(null!=(a=this.get(c))?a.serialize():void 0);switch(!1){case!R()(h):d.push(h);break;case!F()(h):l=l.concat(h)}return d=function(){var e,t,n;for(n=[],e=0,t=d.length;e<t;e++)p=d[e],(F()(p)&&!Ie(p)||!F()(p)&&p)&&n.push(p);return n}(),d=y.sort().concat(m).concat(d.sort()),"if_end"===e?d.push(e):Ie(e)||d.unshift(e),Ie(f=w()(d).join(this.param_separator))||l.push(f),w()(l).join(this.trans_separator)}},{key:"toHtmlAttributes",value:function(){var e,t,n,o,r,i,u,s,a=this;return n={},Object.keys(this.otherOptions).forEach((function(t){i=a.otherOptions[t],s=Ee(t),k()(Cn.PARAM_NAMES,s)||k()(ce,s)||(e=/^html_/.test(t)?t.slice(5):t,n[e]=i)})),this.keys().forEach((function(e){/^html_/.test(e)&&(n[Oe(e.slice(5))]=a.getValue(e))})),this.hasLayer()||this.getValue("angle")||k()(["fit","limit","lfill"],this.getValue("crop"))||(u=null!=(o=this.get("width"))?o.origValue:void 0,t=null!=(r=this.get("height"))?r.origValue:void 0,parseFloat(u)>=1&&null==n.width&&(n.width=u),parseFloat(t)>=1&&null==n.height&&(n.height=t)),n}},{key:"toHtml",value:function(){var e;return null!=(e=this.getParent())&&"function"==typeof e.toHtml?e.toHtml():void 0}},{key:"toString",value:function(){return this.serialize()}},{key:"clone",value:function(){return new this.constructor(this.toOptions(!0))}}],[{key:"listNames",value:function(){return Cn.methods}},{key:"isValidParamName",value:function(e){return Cn.methods.indexOf(Oe(e))>=0}}])}(),On=/^\$[a-zA-Z0-9]+$/;function En(e){var t;return t=null!=e?e[e.length-1]:void 0,U()(t)?t:void 0}function Bn(e){var t=e.function_type,n=e.source;return"remote"===t?[t,btoa(n)].join(":"):"wasm"===t?[t,n].join(":"):void 0}Dn.prototype.trans_separator="/",Dn.prototype.param_separator=",";var Cn=function(e){function Transformation(e){return bn(this,Transformation),hn(this,Transformation,[e])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mn(e,t)}(Transformation,e),jn(Transformation,[{key:"angle",value:function(e){return this.arrayParam(e,"angle","a",".",st.normalize)}},{key:"audioCodec",value:function(e){return this.param(e,"audio_codec","ac")}},{key:"audioFrequency",value:function(e){return this.param(e,"audio_frequency","af")}},{key:"aspectRatio",value:function(e){return this.param(e,"aspect_ratio","ar",st.normalize)}},{key:"background",value:function(e){return this.param(e,"background","b",sn.norm_color)}},{key:"bitRate",value:function(e){return this.param(e,"bit_rate","br")}},{key:"border",value:function(e){return this.param(e,"border","bo",(function(e){return T()(e)?(e=v()({},{color:"black",width:2},e),"".concat(e.width,"px_solid_").concat(sn.norm_color(e.color))):e}))}},{key:"color",value:function(e){return this.param(e,"color","co",sn.norm_color)}},{key:"colorSpace",value:function(e){return this.param(e,"color_space","cs")}},{key:"crop",value:function(e){return this.param(e,"crop","c")}},{key:"customFunction",value:function(e){return this.param(e,"custom_function","fn",(function(){return Bn(e)}))}},{key:"customPreFunction",value:function(e){if(!this.get("custom_function"))return this.rawParam(e,"custom_function","",(function(){return(e=Bn(e))?"fn_pre:".concat(e):e}))}},{key:"defaultImage",value:function(e){return this.param(e,"default_image","d")}},{key:"delay",value:function(e){return this.param(e,"delay","dl")}},{key:"density",value:function(e){return this.param(e,"density","dn")}},{key:"duration",value:function(e){return this.rangeParam(e,"duration","du")}},{key:"dpr",value:function(e){return this.param(e,"dpr","dpr",(function(e){return(null!=(e=e.toString())?e.match(/^\d+$/):void 0)?e+".0":st.normalize(e)}))}},{key:"effect",value:function(e){return this.arrayParam(e,"effect","e",":",st.normalize)}},{key:"else",value:function(){return this.if("else")}},{key:"endIf",value:function(){return this.if("end")}},{key:"endOffset",value:function(e){return this.rangeParam(e,"end_offset","eo")}},{key:"fallbackContent",value:function(e){return this.param(e,"fallback_content")}},{key:"fetchFormat",value:function(e){return this.param(e,"fetch_format","f")}},{key:"format",value:function(e){return this.param(e,"format")}},{key:"flags",value:function(e){return this.arrayParam(e,"flags","fl",".")}},{key:"gravity",value:function(e){return this.param(e,"gravity","g")}},{key:"fps",value:function(e){return this.param(e,"fps","fps",(function(e){return R()(e)?e:F()(e)?e.join("-"):e}))}},{key:"height",value:function(e){var t=this;return this.param(e,"height","h",(function(){return t.getValue("crop")||t.getValue("overlay")||t.getValue("underlay")?st.normalize(e):null}))}},{key:"htmlHeight",value:function(e){return this.param(e,"html_height")}},{key:"htmlWidth",value:function(e){return this.param(e,"html_width")}},{key:"if",value:function(){var e,t,n,o,r,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";switch(i){case"else":return this.chain(),this.param(i,"if","if");case"end":for(this.chain(),e=n=this.chained.length-1;n>=0&&"end"!==(t=this.chained[e].getValue("if"))&&(null==t||(o=Transformation.new().if(t),this.chained[e].remove("if"),r=this.chained[e],this.chained[e]=Transformation.new().transformation([o,r]),"else"===t));e=n+=-1);return this.param(i,"if","if");case"":return pt.new().setParent(this);default:return this.param(i,"if","if",(function(e){return pt.new(e).toString()}))}}},{key:"keyframeInterval",value:function(e){return this.param(e,"keyframe_interval","ki")}},{key:"ocr",value:function(e){return this.param(e,"ocr","ocr")}},{key:"offset",value:function(e){var t,n,o=yn(U()(null!=e?e.split:void 0)?e.split(".."):F()(e)?e:[null,null],2);if(n=o[0],t=o[1],null!=n&&this.startOffset(n),null!=t)return this.endOffset(t)}},{key:"opacity",value:function(e){return this.param(e,"opacity","o",st.normalize)}},{key:"overlay",value:function(e){return this.layerParam(e,"overlay","l")}},{key:"page",value:function(e){return this.param(e,"page","pg")}},{key:"poster",value:function(e){return this.param(e,"poster")}},{key:"prefix",value:function(e){return this.param(e,"prefix","p")}},{key:"quality",value:function(e){return this.param(e,"quality","q",st.normalize)}},{key:"radius",value:function(e){return this.arrayParam(e,"radius","r",":",st.normalize)}},{key:"rawTransformation",value:function(e){return this.rawParam(e,"raw_transformation")}},{key:"size",value:function(e){var t,n;if(U()(null!=e?e.split:void 0)){var o=yn(e.split("x"),2);return n=o[0],t=o[1],this.width(n),this.height(t)}}},{key:"sourceTypes",value:function(e){return this.param(e,"source_types")}},{key:"sourceTransformation",value:function(e){return this.param(e,"source_transformation")}},{key:"startOffset",value:function(e){return this.rangeParam(e,"start_offset","so")}},{key:"streamingProfile",value:function(e){return this.param(e,"streaming_profile","sp")}},{key:"transformation",value:function(e){return this.transformationParam(e,"transformation","t")}},{key:"underlay",value:function(e){return this.layerParam(e,"underlay","u")}},{key:"variable",value:function(e,t){return this.param(t,e,e)}},{key:"variables",value:function(e){return this.arrayParam(e,"variables")}},{key:"videoCodec",value:function(e){return this.param(e,"video_codec","vc",sn.process_video_params)}},{key:"videoSampling",value:function(e){return this.param(e,"video_sampling","vs")}},{key:"width",value:function(e){var t=this;return this.param(e,"width","w",(function(){return t.getValue("crop")||t.getValue("overlay")||t.getValue("underlay")?st.normalize(e):null}))}},{key:"x",value:function(e){return this.param(e,"x","x",st.normalize)}},{key:"y",value:function(e){return this.param(e,"y","y",st.normalize)}},{key:"zoom",value:function(e){return this.param(e,"zoom","z",st.normalize)}}],[{key:"new",value:function(e){return new Transformation(e)}}])}(Dn);Cn.methods=["angle","audioCodec","audioFrequency","aspectRatio","background","bitRate","border","color","colorSpace","crop","customFunction","customPreFunction","defaultImage","delay","density","duration","dpr","effect","else","endIf","endOffset","fallbackContent","fetchFormat","format","flags","gravity","fps","height","htmlHeight","htmlWidth","if","keyframeInterval","ocr","offset","opacity","overlay","page","poster","prefix","quality","radius","rawTransformation","size","sourceTypes","sourceTransformation","startOffset","streamingProfile","transformation","underlay","variable","variables","videoCodec","videoSampling","width","x","y","zoom"],Cn.PARAM_NAMES=Cn.methods.map(Ee).concat(wt.CONFIG_PARAMS);var Sn=Cn;function kn(e){return(kn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function xn(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Fn(o.key),o)}}function Fn(e){var t=function(e,t){if("object"!=kn(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=kn(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==kn(t)?t:t+""}function Pn(e,t){return t?!0===t?e:"".concat(e,'="').concat(t,'"'):void 0}function Tn(e){return R()(e)?e.replace('"',"&#34;").replace("'","&#39;"):e}var In=function(){return function(e,t,n){return t&&xn(e.prototype,t),n&&xn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}((function HtmlTag(e,t,n){var o;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,HtmlTag),this.name=e,this.publicId=t,null==n&&(T()(t)?(n=t,this.publicId=void 0):n={}),(o=new Sn(n)).setParent(this),this.transformation=function(){return o}}),[{key:"htmlAttrs",value:function(e){var t,n;return function(){var o;for(t in o=[],e)(n=Tn(e[t]))&&o.push(Pn(t,n));return o}().sort().join(" ")}},{key:"getOptions",value:function(){return this.transformation().toOptions()}},{key:"getOption",value:function(e){return this.transformation().getValue(e)}},{key:"attributes",value:function(){var e=this.transformation().toHtmlAttributes();return Object.keys(e).forEach((function(t){T()(e[t])&&delete e[t]})),e.attributes&&(z()(e,e.attributes),delete e.attributes),e}},{key:"setAttr",value:function(e,t){return this.transformation().set("html_".concat(e),t),this}},{key:"getAttr",value:function(e){return this.attributes()["html_".concat(e)]||this.attributes()[e]}},{key:"removeAttr",value:function(e){var t;return null!=(t=this.transformation().remove("html_".concat(e)))?t:this.transformation().remove(e)}},{key:"content",value:function(){return""}},{key:"openTag",value:function(){var e="<"+this.name,t=this.htmlAttrs(this.attributes());return t&&t.length>0&&(e+=" "+t),e+">"}},{key:"closeTag",value:function(){return"</".concat(this.name,">")}},{key:"toHtml",value:function(){return this.openTag()+this.content()+this.closeTag()}},{key:"toDOM",value:function(){var e,t,n,o;if(!U()("undefined"!=typeof document&&null!==document?document.createElement:void 0))throw"Can't create DOM if document is not present!";for(t in e=document.createElement(this.name),n=this.attributes())o=n[t],e.setAttribute(t,o);return e}}],[{key:"new",value:function(e,t,n){return new this(e,t,n)}},{key:"isResponsive",value:function(e,t){var n;return n=Ve(e,"src-cache")||Ve(e,"src"),Ke(e,t)&&/\bw_auto\b/.exec(n)}}])}(),Rn=["placeholder","accessibility"];function Ln(e){return!!e&&!!e.match(/^https?:\//)}function zn(e,t){if(t.cloud_name&&"/"===t.cloud_name[0])return"/res"+t.cloud_name;var n="http://",o="",r="res",i=".cloudinary.com",s="/"+t.cloud_name;return t.protocol&&(n=t.protocol+"//"),t.private_cdn&&(o=t.cloud_name+"-",s=""),t.cdn_subdomain&&(r="res-"+function(e){return u(e)%5+1}(e)),t.secure?(n="https://",!1===t.secure_cdn_subdomain&&(r="res"),null!=t.secure_distribution&&t.secure_distribution!==Z&&t.secure_distribution!==J&&(o="",r="",i=t.secure_distribution)):t.cname&&(n="http://",o="",r=t.cdn_subdomain?"a"+(u(e)%5+1)+".":"",i=t.cname),[n,o,r,i,s].join("")}function Mn(e){return encodeURIComponent(e).replace(/%3A/g,":").replace(/%2F/g,"/")}function Nn(e){var t=e.cloud_name,n=e.url_suffix;return t?n&&n.match(/[\.\/]/)?"url_suffix should not include . or /":void 0:"Unknown cloud_name"}function Vn(e){var t=e||{},n=t.placeholder,o=t.accessibility,r=function(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(-1!==t.indexOf(o))continue;n[o]=e[o]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}(t,Rn),i=new Sn(r);(o&&le[o]&&i.chain().effect(le[o]),n)&&("predominant-color"===n&&i.getValue("width")&&i.getValue("height")&&(n+="-pixel"),(ae[n]||ae.blur).forEach((function(e){return i.chain().transformation(e)})));return i.serialize()}function Un(e,t){var n,o,r=t.type;return Ln(e)||"fetch"!==r?e:(n=e,o=document.location.protocol+"//"+document.location.host,"?"===n[0]?o+=document.location.pathname:"/"!==n[0]&&(o+=document.location.pathname.replace(/\/[^\/]*$/,"/")),o+n)}function Hn(e,t){if(Ln(e)&&("upload"===t.type||"asset"===t.type))return e;var n=function(e,t){var n=t.force_version||void 0===t.force_version,o=e.indexOf("/")<0||e.match(/^v[0-9]+/)||Ln(e)||t.version;return n&&!o&&(t.version=1),t.version?"v".concat(t.version):""}(e,t),o=Vn(t),r=zn(e,t),i=function(e){var t=e.signature,n=!t||0===t.indexOf("s--")&&"--"===t.substr(-2);return delete e.signature,n?t:"s--".concat(t,"--")}(t),u=function(e){var t,n=e.resource_type,o=void 0===n?"image":n,r=e.type,i=void 0===r?"upload":r,u=e.url_suffix,s=e.use_root_path,a=e.shorten,l=o;if(T()(l)&&(l=(t=l).resource_type,i=t.type,a=t.shorten),null==i&&(i="upload"),null!=u&&(l=oe["".concat(l,"/").concat(i)],i=null,null==l))throw new Error("URL Suffix only supported for ".concat(Object.keys(oe).join(", ")));if(s){if(("image"!==l||"upload"!==i)&&"images"!==l)throw new Error("Root path only supported for image/upload");l=null,i=null}return a&&"image"===l&&"upload"===i&&(l="iu",i=null),[l,i].join("/")}(t);return e=function(e,t){if(Ln(e))e=Mn(e);else{try{e=decodeURIComponent(e)}catch(n){}e=Mn(e),t.url_suffix&&(e=e+"/"+t.url_suffix),t.format&&(t.trust_public_id||(e=e.replace(/\.(jpg|png|gif|webp)$/,"")),e=e+"."+t.format)}return e}(e,t),w()([r,u,i,o,n,e]).join("/").replace(/([^:])\/+/g,"$1/").replace(" ","%20")}function Wn(e,t){return e instanceof Sn&&(e=e.toOptions()),"fetch"===(e=ve({},e,t,re)).type&&(e.fetch_format=e.fetch_format||e.format),e}function $n(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!e)return e;e=Un(e,t=Wn(t,n));var o=Nn(t);if(o)throw o;var r=Hn(e,t);if(t.urlAnalytics){var i=y(t),u=p(i),s="?";r.indexOf("?")>=0&&(s="&"),r="".concat(r).concat(s,"_a=").concat(u)}if(t.auth_token){var a=r.indexOf("?")>=0?"&":"?";r="".concat(r).concat(a,"__cld_token__=").concat(t.auth_token)}return r}function Gn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,u,s=[],a=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(o=i.call(n)).done)&&(s.push(o.value),s.length!==t);a=!0);}catch(e){l=!0,r=e}finally{try{if(!a&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(l)throw r}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Kn(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Kn(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function qn(e){var t=e.breakpoints||[];if(t.length)return t;var n=Gn([e.min_width,e.max_width,e.max_images].map(Number),3),o=n[0],r=n[1],i=n[2];if([o,r,i].some(isNaN))throw"Either (min_width, max_width, max_images) or breakpoints must be provided to the image srcset attribute";if(o>r)throw"min_width must be less than max_width";if(i<=0)throw"max_images must be a positive integer";1===i&&(o=r);for(var u=Math.ceil((r-o)/Math.max(i-1,1)),s=o;s<r;s+=u)t.push(s);return t.push(r),t}var Yn=Ie;function Qn(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=Fe(o);return n=n||o,r.raw_transformation=new Sn([z.a({},n),{crop:"scale",width:t}]).toString(),$n(e,r)}function Zn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return qn(t)}function Xn(e,t,n,o){return Pe(o=g.a(o)),t.map((function(t){return"".concat(Qn(e,t,n,o)," ").concat(t,"w")})).join(", ")}function Jn(e){return null==e?"":e.map((function(e){return"(max-width: ".concat(e,"px) ").concat(e,"px")})).join(", ")}function eo(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r={};if(Yn(n))return r;var i=!t.sizes&&!0===n.sizes,u=!t.srcset;if(u||i){var s=Zn(e,n,o);if(u){var a=n.transformation,l=Xn(e,s,a,o);Yn(l)||(r.srcset=l)}if(i){var c=Jn(s);Yn(c)||(r.sizes=c)}}return r}function to(e){return(to="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function no(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function oo(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,ro(o.key),o)}}function ro(e){var t=function(e,t){if("object"!=to(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=to(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==to(t)?t:t+""}function io(e,t,n){return t=ao(t),function(e,t){if(t&&("object"==to(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],ao(e).constructor):t.apply(e,n))}function uo(){return(uo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=so(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}}).apply(null,arguments)}function so(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=ao(e)););return e}function ao(e){return(ao=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function lo(e,t){return(lo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var co=function(e){function ImageTag(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return no(this,ImageTag),io(this,ImageTag,["img",e,t])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&lo(e,t)}(ImageTag,e),function(e,t,n){return t&&oo(e.prototype,t),n&&oo(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(ImageTag,[{key:"closeTag",value:function(){return""}},{key:"attributes",value:function(){var e,t,n;e=function(e,t,n,o){var r=uo(ao(1&o?e.prototype:e),t,n);return 2&o&&"function"==typeof r?function(e){return r.apply(n,e)}:r}(ImageTag,"attributes",this,3)([])||{},t=this.getOptions();var o=this.getOption("attributes")||{},r=this.getOption("srcset")||o.srcset,i={};return R()(r)?i.srcset=r:i=eo(this.publicId,o,r,t),Ie(i)||(delete e.width,delete e.height),z()(e,i),null==e[n=t.responsive&&!t.client_hints?"data-src":"src"]&&(e[n]=$n(this.publicId,this.getOptions())),e}}])}(In);function fo(e){return(fo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ho(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function po(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,mo(o.key),o)}}function mo(e){var t=function(e,t){if("object"!=fo(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=fo(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fo(t)?t:t+""}function yo(e,t,n){return t=bo(t),function(e,t){if(t&&("object"==fo(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],bo(e).constructor):t.apply(e,n))}function _o(){return(_o="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=vo(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}}).apply(null,arguments)}function vo(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=bo(e)););return e}function bo(e){return(bo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function go(e,t){return(go=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var jo=function(e){function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return ho(this,t),yo(this,t,["source",e,n])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&go(e,t)}(t,e),function(e,t,n){return t&&po(e.prototype,t),n&&po(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(t,[{key:"closeTag",value:function(){return""}},{key:"attributes",value:function(){var e=this.getOption("srcset"),n=function(e,t,n,o){var r=_o(bo(1&o?e.prototype:e),t,n);return 2&o&&"function"==typeof r?function(e){return r.apply(n,e)}:r}(t,"attributes",this,3)([])||{},o=this.getOptions();return z()(n,eo(this.publicId,n,e,o)),n.srcset||(n.srcset=$n(this.publicId,o)),!n.media&&o.media&&(n.media=function(e){var t=[];return null!=e&&(null!=e.min_width&&t.push("(min-width: ".concat(e.min_width,"px)")),null!=e.max_width&&t.push("(max-width: ".concat(e.max_width,"px)"))),t.join(" and ")}(o.media)),n}}])}(In);function wo(e){return(wo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ao(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Do(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Oo(o.key),o)}}function Oo(e){var t=function(e,t){if("object"!=wo(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=wo(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==wo(t)?t:t+""}function Eo(e,t,n){return t=So(t),function(e,t){if(t&&("object"==wo(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],So(e).constructor):t.apply(e,n))}function Bo(){return(Bo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=Co(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}}).apply(null,arguments)}function Co(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=So(e)););return e}function So(e){return(So=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ko(e,t){return(ko=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var xo=function(e){function PictureTag(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return Ao(this,PictureTag),(t=Eo(this,PictureTag,["picture",e,n])).widthList=o,t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ko(e,t)}(PictureTag,e),function(e,t,n){return t&&Do(e.prototype,t),n&&Do(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(PictureTag,[{key:"content",value:function(){var e=this;return this.widthList.map((function(t){var n=t.min_width,o=t.max_width,r=t.transformation,i=e.getOptions(),u=new Sn(i);return u.chain().fromOptions("string"==typeof r?{raw_transformation:r}:r),(i=Fe(i)).media={min_width:n,max_width:o},i.transformation=u,new jo(e.publicId,i).toHtml()})).join("")+new co(this.publicId,this.getOptions()).toHtml()}},{key:"attributes",value:function(){var e=function(e,t,n,o){var r=Bo(So(1&o?e.prototype:e),t,n);return 2&o&&"function"==typeof r?function(e){return r.apply(n,e)}:r}(PictureTag,"attributes",this,3)([]);return delete e.width,delete e.height,e}},{key:"closeTag",value:function(){return"</"+this.name+">"}}])}(In);function Fo(e){return(Fo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Po(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function To(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Io(o.key),o)}}function Io(e){var t=function(e,t){if("object"!=Fo(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Fo(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Fo(t)?t:t+""}function Ro(e,t,n){return t=Mo(t),function(e,t){if(t&&("object"==Fo(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],Mo(e).constructor):t.apply(e,n))}function Lo(){return(Lo="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var o=zo(e,t);if(o){var r=Object.getOwnPropertyDescriptor(o,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}}).apply(null,arguments)}function zo(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=Mo(e)););return e}function Mo(e){return(Mo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function No(e,t){return(No=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var Vo=["source_types","source_transformation","fallback_content","poster","sources"],Uo=["webm","mp4","ogv"],Ho={format:"jpg",resource_type:"video"},Wo=function(e){function VideoTag(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Po(this,VideoTag),t=ve({},t,ie),Ro(this,VideoTag,["video",e.replace(/\.(mp4|ogv|webm)$/,""),t])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&No(e,t)}(VideoTag,e),function(e,t,n){return t&&To(e.prototype,t),n&&To(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(VideoTag,[{key:"setSourceTransformation",value:function(e){return this.transformation().sourceTransformation(e),this}},{key:"setSourceTypes",value:function(e){return this.transformation().sourceTypes(e),this}},{key:"setPoster",value:function(e){return this.transformation().poster(e),this}},{key:"setFallbackContent",value:function(e){return this.transformation().fallbackContent(e),this}},{key:"content",value:function(){var e=this,t=this.transformation().getValue("source_types"),n=this.transformation().getValue("source_transformation"),o=this.transformation().getValue("fallback_content"),r=this.getOption("sources"),i=[];return F()(r)&&!Ie(r)?i=r.map((function(t){var n=$n(e.publicId,ve({},t.transformations||{},{resource_type:"video",format:t.type}),e.getOptions());return e.createSourceTag(n,t.type,t.codecs)})):(Ie(t)&&(t=Uo),F()(t)&&(i=t.map((function(t){var o=$n(e.publicId,ve({},n[t]||{},{resource_type:"video",format:t}),e.getOptions());return e.createSourceTag(o,t)})))),i.join("")+o}},{key:"attributes",value:function(){var e=this.getOption("source_types"),t=this.getOption("poster");if(void 0===t&&(t={}),T()(t)){var n=null!=t.public_id?re:Ho;t=$n(t.public_id||this.publicId,ve({},t,n,this.getOptions()))}var o=function(e,t,n,o){var r=Lo(Mo(1&o?e.prototype:e),t,n);return 2&o&&"function"==typeof r?function(e){return r.apply(n,e)}:r}(VideoTag,"attributes",this,3)([])||{};return o=fe(o,Vo),!Ie(this.getOption("sources"))||Ie(e)||F()(e)||(o.src=$n(this.publicId,this.getOptions(),{resource_type:"video",format:e})),null!=t&&(o.poster=t),o}},{key:"createSourceTag",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=null;if(!Ie(t)){var r="ogv"===t?"ogg":t;if(o="video/"+r,!Ie(n)){var i=F()(n)?n.join(", "):n;o+="; codecs="+i}}return"<source "+this.htmlAttrs({src:e,type:o})+">"}}])}(In);function $o(e){return($o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Go(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Ko(o.key),o)}}function Ko(e){var t=function(e,t){if("object"!=$o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=$o(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==$o(t)?t:t+""}function qo(e,t,n){return t=Yo(t),function(e,t){if(t&&("object"==$o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],Yo(e).constructor):t.apply(e,n))}function Yo(e){return(Yo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Qo(e,t){return(Qo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var Zo=function(e){function ClientHintsMetaTag(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,ClientHintsMetaTag),qo(this,ClientHintsMetaTag,["meta",void 0,v()({"http-equiv":"Accept-CH",content:"DPR, Viewport-Width, Width"},e)])}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qo(e,t)}(ClientHintsMetaTag,e),function(e,t,n){return t&&Go(e.prototype,t),n&&Go(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(ClientHintsMetaTag,[{key:"closeTag",value:function(){return""}}])}(In);function Xo(e){return function(e){if(Array.isArray(e))return Jo(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Jo(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Jo(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jo(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var er=function(e,t,n,o){return new Promise((function(r,i){e.innerHTML=t.videoTag(n,o).toHtml(),e.querySelector(".cld-transparent-video").style.width="100%",r(e)}))};var tr=function(e,t){e.transformation?e.transformation.push({flags:[t]}):(e.flags||(e.flags=[]),"string"==typeof e.flags&&(e.flags=[e.flags]),e.flags.push(t))};var nr=function(e){e.autoplay=!0,e.muted=!0,e.controls=!1,e.max_timeout_ms=e.max_timeout_ms||ee,e.class=e.class||"",e.class+=" cld-transparent-video",e.externalLibraries=e.externalLibraries||{},e.externalLibraries.seeThru||(e.externalLibraries.seeThru=se.seeThru),tr(e,"alpha")};var or=function(e,t,n){return new Promise((function(o,r){if(n)o();else{var i=document.createElement("script");i.src=e;var u=setTimeout((function(){r({status:"error",message:"Timeout loading script ".concat(e)})}),t);i.onerror=function(){clearTimeout(u),r({status:"error",message:"Error loading ".concat(e)})},i.onload=function(){clearTimeout(u),o()},document.head.appendChild(i)}}))};function rr(e){return new Promise((function(t,n){fetch(e).then((function(e){e.blob().then((function(e){t(e)}))})).catch((function(){n("error")}))}))}function ir(e){return new Promise((function(t,n){var o=new XMLHttpRequest;o.responseType="blob",o.onload=function(e){t(o.response)},o.onerror=function(){n("error")},o.open("GET",e,!0),o.send()}))}var ur=function(e,t){return new Promise((function(n,o){var r=function(e,t){return setTimeout((function(){t({status:"error",message:"Timeout loading Blob URL"})}),e)}(t,o);("undefined"!=typeof fetch&&fetch?rr:ir)(e).then((function(e){n({status:"success",payload:{blobURL:URL.createObjectURL(e)}})})).catch((function(){o({status:"error",message:"Error loading Blob URL"})})).finally((function(){clearTimeout(r)}))}))};var sr=function(e){var t=e.autoplay,n=e.playsinline,o=e.loop,r=e.muted,i=e.poster,u=e.blobURL,s=e.videoURL,a=document.createElement("video");return a.style.visibility="hidden",a.position="absolute",a.x=0,a.y=0,a.src=u,a.setAttribute("data-video-url",s),t&&a.setAttribute("autoplay",t),n&&a.setAttribute("playsinline",n),o&&a.setAttribute("loop",o),r&&a.setAttribute("muted",r),r&&(a.muted=r),i&&a.setAttribute("poster",i),a.onload=function(){URL.revokeObjectURL(u)},a};var ar=function(e,t,n,o){var r=window,i=r.seeThru,u=r.setTimeout,s=r.clearTimeout;return new Promise((function(r,a){var l=u((function(){a({status:"error",message:"Timeout instantiating seeThru instance"})}),t);if(i)var c=i.create(e).ready((function(){s(l);var e=c.getCanvas();e.style.width="100%",e.className+=" "+n,o&&c.play(),r(c)}));else a({status:"error",message:"Error instantiating seeThru instance"})}))};var lr=function(e,t,n){var o=n.poster,r=n.autoplay,i=n.playsinline,u=n.loop,s=n.muted;return t+=".mp4",new Promise((function(a,l){or(n.externalLibraries.seeThru,n.max_timeout_ms,window.seeThru).then((function(){ur(t,n.max_timeout_ms).then((function(c){var d=c.payload,f=sr({blobURL:d.blobURL,videoURL:t,poster:o,autoplay:r,playsinline:i,loop:u,muted:s});e.appendChild(f),ar(f,n.max_timeout_ms,n.class,n.autoplay).then((function(){a(e)})).catch((function(e){l(e)}))})).catch((function(e){var t=e.status,n=e.message;l({status:t,message:n})}))})).catch((function(e){var t=e.status,n=e.message;l({status:t,message:n})}))}))};var cr,dr,fr,hr,pr,mr,yr=function(){return new Promise((function(e,t){Ne()&&e(!1);var n=document.createElement("video"),o=n.canPlayType&&n.canPlayType('video/webm; codecs="vp9"');e("maybe"===o||"probably"===o)}))};function _r(e){return(_r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function vr(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,br(o.key),o)}}function br(e){var t=function(e,t){if("object"!=_r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==_r(t)?t:t+""}fr=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return t*Math.ceil(e/t)},dr=function(e,t){var n;for(n=e.length-2;n>=0&&e[n]>=t;)n--;return e[n+1]},cr=function(e,t,n,o){var r,i,u,s;return!(s=null!=(r=null!=(i=null!=(u=o.responsive_use_breakpoints)?u:o.responsive_use_stoppoints)?i:this.config("responsive_use_breakpoints"))?r:this.config("responsive_use_stoppoints"))||"resize"===s&&!o.resizing?t:this.calc_breakpoint(e,t,n)},hr=function(e){var t,n;for(t=0;(e=null!=e?e.parentNode:void 0)instanceof Element&&!t;)n=window.getComputedStyle(e),/^inline/.test(n.display)||(t=ot(e));return t},mr=function(e,t){return e.replace(/\bdpr_(1\.0|auto)\b/g,"dpr_"+this.device_pixel_ratio(t))},pr=function(e,t){var n;return e>(n=Ve(t,"width")||0)&&(n=e,Ue(t,"width",e)),n};var gr=function(){return function(e,t,n){return t&&vr(e.prototype,t),n&&vr(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}((function Cloudinary(e){var t;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Cloudinary),this.devicePixelRatioCache={},this.responsiveConfig={},this.responsiveResizeInitialized=!1,t=new wt(e),this.config=function(e,n){return t.config(e,n)},this.fromDocument=function(){return t.fromDocument(),this},this.fromEnvironment=function(){return t.fromEnvironment(),this},this.init=function(){return t.init(),this}}),[{key:"url",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return $n(e,t,this.config())}},{key:"video_url",value:function(e,t){return t=v()({resource_type:"video"},t),this.url(e,t)}},{key:"video_thumbnail_url",value:function(e,t){return t=v()({},te,t),this.url(e,t)}},{key:"transformation_string",value:function(e){return new Sn(e).serialize()}},{key:"image",value:function(e){var t,n,o,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n=this.imageTag(e,r),t=null!=(o=null!=r.client_hints?r.client_hints:this.config("client_hints"))&&o,null!=r.src||t||n.setAttr("src",""),n=n.toDOM(),t||(Ue(n,"src-cache",this.url(e,r)),this.cloudinary_update(n,r)),n}},{key:"imageTag",value:function(e,t){var n;return(n=new co(e,this.config())).transformation().fromOptions(t),n}},{key:"pictureTag",value:function(e,t,n){var o;return(o=new xo(e,this.config(),n)).transformation().fromOptions(t),o}},{key:"sourceTag",value:function(e,t){var n;return(n=new jo(e,this.config())).transformation().fromOptions(t),n}},{key:"video_thumbnail",value:function(e,t){return this.image(e,z()({},te,t))}},{key:"facebook_profile_image",value:function(e,t){return this.image(e,v()({type:"facebook"},t))}},{key:"twitter_profile_image",value:function(e,t){return this.image(e,v()({type:"twitter"},t))}},{key:"twitter_name_profile_image",value:function(e,t){return this.image(e,v()({type:"twitter_name"},t))}},{key:"gravatar_image",value:function(e,t){return this.image(e,v()({type:"gravatar"},t))}},{key:"fetch_image",value:function(e,t){return this.image(e,v()({type:"fetch"},t))}},{key:"video",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.videoTag(e,t).toHtml()}},{key:"videoTag",value:function(e,t){return t=ve({},t,this.config()),new Wo(e,t)}},{key:"sprite_css",value:function(e,t){return t=v()({type:"sprite"},t),e.match(/.css$/)||(t.format="css"),this.url(e,t)}},{key:"responsive",value:function(e){var t,n,o,r,i,u=this,s=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this.responsiveConfig=z()(this.responsiveConfig||{},e),r=null!=(t=this.responsiveConfig.responsive_class)?t:this.config("responsive_class"),s&&this.cloudinary_update("img.".concat(r,", img.cld-hidpi"),this.responsiveConfig),(null==(n=null!=(o=this.responsiveConfig.responsive_resize)?o:this.config("responsive_resize"))||n)&&!this.responsiveResizeInitialized){this.responsiveConfig.resizing=this.responsiveResizeInitialized=!0,i=null;var a=function(){var e,t,n,o,s,a;return e=null!=(t=null!=(n=u.responsiveConfig.responsive_debounce)?n:u.config("responsive_debounce"))?t:100,o=function(){i&&(clearTimeout(i),i=null)},s=function(){return u.cloudinary_update("img.".concat(r),u.responsiveConfig)},a=function(){return o(),s()},e?function(){o(),i=setTimeout(a,e)}():s()};return window.addEventListener("resize",a),function(){return window.removeEventListener("resize",a)}}}},{key:"calc_breakpoint",value:function(e,t,n){var o=Ve(e,"breakpoints")||Ve(e,"stoppoints")||this.config("breakpoints")||this.config("stoppoints")||fr;return U()(o)?o(t,n):(R()(o)&&(o=o.split(",").map((function(e){return parseInt(e)})).sort((function(e,t){return e-t}))),dr(o,t))}},{key:"calc_stoppoint",value:function(e,t,n){return this.calc_breakpoint(e,t,n)}},{key:"device_pixel_ratio",value:function(e){e=null==e||e;var t=("undefined"!=typeof window&&null!==window?window.devicePixelRatio:void 0)||1;e&&(t=Math.ceil(t)),(t<=0||NaN===t)&&(t=1);var n=t.toString();return n.match(/^\d+$/)&&(n+=".0"),n}},{key:"processImageTags",value:function(e,t){if(Ie(e))return this;t=ve({},t||{},this.config());var n=e.filter((function(e){return/^img$/i.test(e.tagName)})).map((function(e){var n=v()({width:e.getAttribute("width"),height:e.getAttribute("height"),src:e.getAttribute("src")},t),o=n.source||n.src;delete n.source,delete n.src;var r=new Sn(n).toHtmlAttributes();return Ue(e,"src-cache",$n(o,n)),e.setAttribute("width",r.width),e.setAttribute("height",r.height),e}));return this.cloudinary_update(n,t),this}},{key:"cloudinary_update",value:function(e,t){var n,o,r,i,u=this;if(null===e)return this;null==t&&(t={});var s,a=null!=t.responsive?t.responsive:this.config("responsive");e=function(e){return F()(e)?e:"NodeList"===e.constructor.name?Xo(e):R()(e)?Array.prototype.slice.call(document.querySelectorAll(e),0):[e]}(e),s=this.responsiveConfig&&null!=this.responsiveConfig.responsive_class?this.responsiveConfig.responsive_class:null!=t.responsive_class?t.responsive_class:this.config("responsive_class");var l=null!=t.round_dpr?t.round_dpr:this.config("round_dpr");return e.forEach((function(c){if(/img/i.test(c.tagName)){var d=!0;if(a&&qe(c,s),!Ie(o=Ve(c,"src-cache")||Ve(c,"src"))){o=mr.call(u,o,l),In.isResponsive(c,s)&&(0!==(n=hr(c))?(/w_auto:breakpoints/.test(o)?(i=pr(n,c))?o=o.replace(/w_auto:breakpoints([_0-9]*)(:[0-9]+)?/,"w_auto:breakpoints$1:".concat(i)):d=!1:(r=/w_auto(:(\d+))?/.exec(o))&&(i=cr.call(u,c,n,r[2],t),(i=pr(i,c))?o=o.replace(/w_auto[^,\/]*/g,"w_".concat(i)):d=!1),$e(c,"width"),t.responsive_preserve_height||$e(c,"height")):d=!1);var f="lazy"===t.loading&&!u.isNativeLazyLoadSupported()&&u.isLazyLoadSupported()&&!e[0].getAttribute("src");(d||f)&&u.setAttributeIfExists(e[0],"width","data-width"),d&&!f&&We(c,"src",o)}}})),this}},{key:"setAttributeIfExists",value:function(e,t,n){var o=e.getAttribute(n);null!=o&&We(e,t,o)}},{key:"isLazyLoadSupported",value:function(){return window&&"IntersectionObserver"in window}},{key:"isNativeLazyLoadSupported",value:function(){return"loading"in HTMLImageElement.prototype}},{key:"transformation",value:function(e){return Sn.new(this.config()).fromOptions(e).setParent(this)}},{key:"injectTransparentVideoElement",value:function(e,t){var n=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return new Promise((function(r,i){e||i({status:"error",message:"Expecting htmlElContainer to be HTMLElement"}),nr(o);var u=n.video_url(t,o);yr().then((function(s){var a;s?(a=er(e,n,t,o),r(e)):a=lr(e,u,o),a.then((function(){r(e)})).catch((function(e){var t=e.status,n=e.message;i({status:t,message:n})}))})).catch((function(e){var t=e.status,n=e.message;i({status:t,message:n})}))}))}}],[{key:"new",value:function(e){return new this(e)}}])}();v()(gr,o);var jr=gr;t.default={ClientHintsMetaTag:Zo,Cloudinary:jr,Condition:pt,Configuration:wt,Expression:st,crc32:u,FetchLayer:Kt,HtmlTag:In,ImageTag:co,Layer:Et,PictureTag:xo,SubtitlesLayer:Nt,TextLayer:Pt,Transformation:Sn,utf8_encode:i,Util:r,VideoTag:Wo}}})}));