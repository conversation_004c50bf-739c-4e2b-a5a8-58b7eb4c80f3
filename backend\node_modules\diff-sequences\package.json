{"name": "diff-sequences", "version": "29.6.3", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/diff-sequences"}, "license": "MIT", "description": "Compare items in two sequences to find a longest common subsequence", "keywords": ["fast", "linear", "space", "callback", "diff"], "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "devDependencies": {"@fast-check/jest": "^1.3.0", "benchmark": "^2.1.4", "diff": "^5.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b"}