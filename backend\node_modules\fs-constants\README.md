# fs-constants

Small module that allows you to get the fs constants across
Node and the browser. 

```
npm install fs-constants
```

Previously you would use `require('constants')` for this in node but that has been
deprecated and changed to `require('fs').constants` which does not browserify.

This module uses `require('constants')` in the browser and `require('fs').constants` in node to work around this


## Usage

``` js
var constants = require('fs-constants')

console.log('constants:', constants)
```

## License

MIT
