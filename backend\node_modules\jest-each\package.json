{"name": "jest-each", "version": "29.7.0", "description": "Parameterised tests for Jest", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-each"}, "keywords": ["jest", "parameterised", "test", "each"], "author": "<PERSON> (mattphillips)", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "jest-util": "^29.7.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630"}