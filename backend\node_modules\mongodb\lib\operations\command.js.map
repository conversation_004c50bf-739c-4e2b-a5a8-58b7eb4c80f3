{"version": 3, "file": "command.js", "sourceRoot": "", "sources": ["../../src/operations/command.ts"], "names": [], "mappings": ";;;AACA,oCAAqD;AACrD,wCAA0D;AAC1D,kDAA8C;AAG9C,+DAA4E;AAE5E,oCAMkB;AAClB,oDAA0E;AAE1E,2CAAuF;AAoDvF,gBAAgB;AAChB,MAAsB,gBAAoB,SAAQ,qCAA4B;IAM5E,YAAY,MAAwB,EAAE,OAAiC;QACrE,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAE7B,kFAAkF;QAClF,kFAAkF;QAClF,qBAAqB;QACrB,MAAM,cAAc,GAAG,OAAO,EAAE,MAAM,IAAI,OAAO,EAAE,MAAM,CAAC;QAC1D,IAAI,cAAc,EAAE;YAClB,IAAI,CAAC,EAAE,GAAG,IAAI,wBAAgB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;SACxD;aAAM;YACL,IAAI,CAAC,EAAE,GAAG,MAAM;gBACd,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC;gBAC3C,CAAC,CAAC,IAAI,wBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,WAAW,GAAG,0BAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY,GAAG,4BAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,WAAW,CAAC,EAAE;YACtC,IAAI,CAAC,OAAO,GAAG,iBAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC7C;aAAM,IAAI,OAAO,EAAE,OAAO,IAAI,IAAI,EAAE;YACnC,MAAM,IAAI,iCAAyB,CAAC,mDAAmD,CAAC,CAAC;SAC1F;IACH,CAAC;IAED,IAAa,aAAa;QACxB,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,WAAW,CAAC,EAAE;YACtC,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;SAC7B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAAc,EACd,OAAkC,EAClC,GAAa;QAEb,uDAAuD;QACvD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,MAAM,OAAO,GAAG;YACd,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,IAAI,CAAC,WAAW;YACnB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,OAAO;SACR,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAA,sBAAc,EAAC,MAAM,CAAC,CAAC;QACjD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAEnE,IAAI,IAAI,CAAC,WAAW,IAAI,IAAA,kCAA0B,EAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE;YACzE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;SACvD;QAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,GAAG,mDAAgC,EAAE;YAClF,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE;YACjF,4BAAY,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;SAC5C;QAED,IACE,OAAO,CAAC,SAAS;YACjB,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ;YACrC,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,cAAc,CAAC,EACtC;YACA,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;SACtD;QAED,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE;YACzC,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;YACtD,GAAG,GAAG,IAAA,2BAAmB,EAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC9C;QAED,OAAO,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;CACF;AAvFD,4CAuFC;AAED,gBAAgB;AAChB,MAAsB,wBAAkC,SAAQ,gBAAmB;IACjF,YAAY,MAAwB,EAAE,OAAiC;QACrE,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACzB,CAAC;IAED,sBAAsB,CACpB,MAAc,EACd,OAAkC,EAClC,GAAa,EACb,QAAkB;QAElB,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,IAAI,CAC7C,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,EAC/B,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,CAAC,CAChC,CAAC;IACJ,CAAC;CACF;AAhBD,4DAgBC"}