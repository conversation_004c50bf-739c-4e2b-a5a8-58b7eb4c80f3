{"name": "parse-json", "version": "5.2.0", "description": "Parse JSO<PERSON> with more helpful errors", "license": "MIT", "repository": "sindresorhus/parse-json", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js", "vendor"], "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "devDependencies": {"ava": "^1.4.1", "nyc": "^14.1.1", "xo": "^0.24.0"}}