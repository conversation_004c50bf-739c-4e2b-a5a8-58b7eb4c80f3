{"version": 3, "file": "sift.csp.min.js", "sources": ["node_modules/tslib/tslib.es6.js", "src/utils.ts", "src/core.ts", "src/operations.ts", "src/index.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n", null, null, null, null], "names": [], "mappings": ";;;;;;IAAA;IACA;AACA;IACA;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;IACA,IAAI,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;IACnC,IAAI,aAAa,GAAG,MAAM,CAAC,cAAc;IACzC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,KAAK,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;IACpF,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1G,IAAI,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC;AACF;IACO,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;IAChC,IAAI,IAAI,OAAO,CAAC,KAAK,UAAU,IAAI,CAAC,KAAK,IAAI;IAC7C,QAAQ,MAAM,IAAI,SAAS,CAAC,sBAAsB,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,+BAA+B,CAAC,CAAC;IAClG,IAAI,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,IAAI,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzF;;IC3BO,IAAM,WAAW,GAAG,UAAQ,IAAI;QACrC,IAAM,UAAU,GAAG,UAAU,GAAG,IAAI,GAAG,GAAG,CAAC;QAC3C,OAAO,UAAS,KAAK;YACnB,OAAO,YAAY,CAAC,KAAK,CAAC,KAAK,UAAU,CAAC;SAC3C,CAAC;IACJ,CAAC,CAAC;IAEF,IAAM,YAAY,GAAG,UAAA,KAAK,IAAI,OAAA,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAA,CAAC;IAE7D,IAAM,UAAU,GAAG,UAAC,KAAU;QACnC,IAAI,KAAK,YAAY,IAAI,EAAE;YACzB,OAAO,KAAK,CAAC,OAAO,EAAE,CAAC;SACxB;aAAM,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;YACzB,OAAO,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SAC9B;aAAM,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,EAAE;YACtD,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;SACvB;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEK,IAAM,OAAO,GAAG,WAAW,CAAa,OAAO,CAAC,CAAC;IACjD,IAAM,QAAQ,GAAG,WAAW,CAAS,QAAQ,CAAC,CAAC;IAC/C,IAAM,UAAU,GAAG,WAAW,CAAW,UAAU,CAAC,CAAC;IACrD,IAAM,eAAe,GAAG,UAAA,KAAK;QAClC,QACE,KAAK;aACJ,KAAK,CAAC,WAAW,KAAK,MAAM;gBAC3B,KAAK,CAAC,WAAW,KAAK,KAAK;gBAC3B,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,qCAAqC;gBACtE,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,oCAAoC,CAAC;YACxE,CAAC,KAAK,CAAC,MAAM,EACb;IACJ,CAAC,CAAC;IAEK,IAAM,MAAM,GAAG,UAAC,CAAC,EAAE,CAAC;QACzB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,KAAK,CAAC,EAAE;YACX,OAAO,IAAI,CAAC;SACb;QAED,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YAC3E,OAAO,KAAK,CAAC;SACd;QAED,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;gBACzB,OAAO,KAAK,CAAC;aACd;YACD,KAAS,IAAA,CAAC,GAAG,CAAC,EAAI,QAAM,GAAK,CAAC,OAAN,EAAQ,CAAC,GAAG,QAAM,EAAE,CAAC,EAAE,EAAE;gBAC/C,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAAE,OAAO,KAAK,CAAC;aACvC;YACD,OAAO,IAAI,CAAC;SACb;aAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;YACtB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;gBACnD,OAAO,KAAK,CAAC;aACd;YACD,KAAK,IAAM,GAAG,IAAI,CAAC,EAAE;gBACnB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;oBAAE,OAAO,KAAK,CAAC;aAC3C;YACD,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;;ICcD;;;;IAKA,IAAM,iBAAiB,GAAG,UACxB,IAAS,EACT,OAAc,EACd,IAAY,EACZ,KAAa,EACb,GAAQ,EACR,KAAU;QAEV,IAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;;;QAIlC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE;YAC9C,KAAS,IAAA,CAAC,GAAG,CAAC,EAAI,QAAM,GAAK,IAAI,OAAT,EAAW,CAAC,GAAG,QAAM,EAAE,CAAC,EAAE,EAAE;;;gBAGlD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE;oBAC9D,OAAO,KAAK,CAAC;iBACd;aACF;SACF;QAED,IAAI,KAAK,KAAK,OAAO,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE;YAC5C,OAAO,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC;SAC5C;QAED,OAAO,iBAAiB,CACtB,IAAI,CAAC,UAAU,CAAC,EAChB,OAAO,EACP,IAAI,EACJ,KAAK,GAAG,CAAC,EACT,UAAU,EACV,IAAI,CACL,CAAC;IACJ,CAAC,CAAC;IAEF;QAKE,uBACW,MAAe,EACf,WAAgB,EAChB,OAAgB,EAChB,IAAa;YAHb,WAAM,GAAN,MAAM,CAAS;YACf,gBAAW,GAAX,WAAW,CAAK;YAChB,YAAO,GAAP,OAAO,CAAS;YAChB,SAAI,GAAJ,IAAI,CAAS;YAEtB,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;QACS,4BAAI,GAAd,eAAmB;QACnB,6BAAK,GAAL;YACE,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;YAClB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;SACnB;QAEH,oBAAC;IAAD,CAAC,IAAA;IAED;QAAsC,kCAAkB;QAItD,wBACE,MAAW,EACX,WAAgB,EAChB,OAAgB,EACA,QAA0B;YAJ5C,YAME,kBAAM,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,SACpC;YAHiB,cAAQ,GAAR,QAAQ,CAAkB;;SAG3C;;;QAKD,8BAAK,GAAL;YACE,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;YAClB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;YAClB,KAAS,IAAA,CAAC,GAAG,CAAC,EAAI,QAAM,GAAK,IAAI,CAAC,QAAQ,OAAlB,EAAoB,CAAC,GAAG,QAAM,EAAE,CAAC,EAAE,EAAE;gBAC3D,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;aAC1B;SACF;;;QAOS,qCAAY,GAAtB,UAAuB,IAAS,EAAE,GAAQ,EAAE,KAAU,EAAE,IAAa;YACnE,IAAI,IAAI,GAAG,IAAI,CAAC;YAChB,IAAI,IAAI,GAAG,IAAI,CAAC;YAChB,KAAS,IAAA,CAAC,GAAG,CAAC,EAAI,QAAM,GAAK,IAAI,CAAC,QAAQ,OAAlB,EAAoB,CAAC,GAAG,QAAM,EAAE,CAAC,EAAE,EAAE;gBAC3D,IAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;oBACxB,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;iBAC7C;gBACD,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;oBACxB,IAAI,GAAG,KAAK,CAAC;iBACd;gBACD,IAAI,cAAc,CAAC,IAAI,EAAE;oBACvB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;wBACxB,MAAM;qBACP;iBACF;qBAAM;oBACL,IAAI,GAAG,KAAK,CAAC;iBACd;aACF;YACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SAClB;QACH,qBAAC;IAAD,CAnDA,CAAsC,aAAa,GAmDlD;IAED;QAAkD,uCAAc;QAG9D,6BACE,MAAW,EACX,WAAgB,EAChB,OAAgB,EAChB,QAA0B,EACjB,IAAY;YALvB,YAOE,kBAAM,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC,SAC9C;YAHU,UAAI,GAAJ,IAAI,CAAQ;;SAGtB;QACH,0BAAC;IAAD,CAZA,CAAkD,cAAc,GAY/D;IAED;QAA2C,kCAAc;QAAzD;YAAA,qEAQC;YAPU,YAAM,GAAG,IAAI,CAAC;;SAOxB;;;QAHC,6BAAI,GAAJ,UAAK,IAAW,EAAE,GAAQ,EAAE,MAAW,EAAE,IAAa;YACpD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;SAC5C;QACH,qBAAC;IAAD,CARA,CAA2C,cAAc,GAQxD;IAED;QAAqC,mCAAc;QAEjD,yBACW,OAAc,EACvB,MAAW,EACX,WAAgB,EAChB,OAAgB,EAChB,QAA0B;YAL5B,YAOE,kBAAM,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC,SAC9C;YAPU,aAAO,GAAP,OAAO,CAAO;YAFhB,YAAM,GAAG,IAAI,CAAC;;;YA2Bf,sBAAgB,GAAG,UACzB,KAAU,EACV,GAAQ,EACR,KAAU,EACV,IAAa;gBAEb,KAAI,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC3C,OAAO,CAAC,KAAI,CAAC,IAAI,CAAC;aACnB,CAAC;;SA1BD;;;QAID,8BAAI,GAAJ,UAAK,IAAS,EAAE,GAAQ,EAAE,MAAW;YACnC,iBAAiB,CACf,IAAI,EACJ,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,gBAAgB,EACrB,CAAC,EACD,GAAG,EACH,MAAM,CACP,CAAC;SACH;QAcH,sBAAC;IAAD,CArCA,CAAqC,cAAc,GAqClD;IAEM,IAAM,YAAY,GAAG,UAAC,CAAC,EAAE,OAAmB;QACjD,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,OAAO,CAAC,CAAC;SACV;QACD,IAAI,CAAC,YAAY,MAAM,EAAE;YACvB,OAAO,UAAA,CAAC;gBACN,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClD,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC;gBAChB,OAAO,MAAM,CAAC;aACf,CAAC;SACH;QACD,IAAM,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAClC,OAAO,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,GAAA,CAAC;IAClD,CAAC,CAAC;;QAE2C,mCAAqB;QAAlE;YAAA,qEAcC;YAbU,YAAM,GAAG,IAAI,CAAC;;SAaxB;QAXC,8BAAI,GAAJ;YACE,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC9D;QACD,8BAAI,GAAJ,UAAK,IAAI,EAAE,GAAQ,EAAE,MAAW;YAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBACxD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE;oBACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;oBACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;iBAClB;aACF;SACF;QACH,sBAAC;IAAD,CAdA,CAA6C,aAAa,GAczD;QAEY,qBAAqB,GAAG,UACnC,MAAW,EACX,WAAgB,EAChB,OAAgB,IACb,OAAA,IAAI,eAAe,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,IAAC;IAEvD;QAA2C,iCAAqB;QAAhE;YAAA,qEAMC;YALU,YAAM,GAAG,IAAI,CAAC;;SAKxB;QAJC,4BAAI,GAAJ;YACE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;SACnB;QACH,oBAAC;IAAD,CANA,CAA2C,aAAa,GAMvD;IAEM,IAAM,yBAAyB,GAAG,UACvC,wBAA+C,IAC5C,OAAA,UAAC,MAAW,EAAE,WAAgB,EAAE,OAAgB,EAAE,IAAY;QACjE,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,OAAO,IAAI,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;SAC9D;QAED,OAAO,wBAAwB,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACtE,CAAC,GAAA,CAAC;IAEK,IAAM,kBAAkB,GAAG,UAAC,YAA6B;QAC9D,OAAA,yBAAyB,CACvB,UAAC,MAAW,EAAE,WAAuB,EAAE,OAAgB,EAAE,IAAY;YACnE,IAAM,YAAY,GAAG,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC;YAC/C,IAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YAClC,OAAO,IAAI,eAAe,CACxB,UAAA,CAAC;gBACC,OAAO,OAAO,UAAU,CAAC,CAAC,CAAC,KAAK,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;aACzD,EACD,WAAW,EACX,OAAO,EACP,IAAI,CACL,CAAC;SACH,CACF;IAbD,CAaC,CAAC;IASJ,IAAM,oBAAoB,GAAG,UAC3B,IAAY,EACZ,MAAW,EACX,WAAgB,EAChB,OAAgB;QAEhB,IAAM,gBAAgB,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,CAAC,gBAAgB,EAAE;YACrB,yBAAyB,CAAC,IAAI,CAAC,CAAC;SACjC;QACD,OAAO,gBAAgB,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC9D,CAAC,CAAC;IAEF,IAAM,yBAAyB,GAAG,UAAC,IAAY;QAC7C,MAAM,IAAI,KAAK,CAAC,4BAA0B,IAAM,CAAC,CAAC;IACpD,CAAC,CAAC;IAEK,IAAM,iBAAiB,GAAG,UAAC,KAAU,EAAE,OAAgB;QAC5D,KAAK,IAAM,GAAG,IAAI,KAAK,EAAE;YACvB,IAAI,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;gBACjE,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IACF,IAAM,qBAAqB,GAAG,UAC5B,OAAc,EACd,WAAgB,EAChB,SAAiB,EACjB,WAAgB,EAChB,OAAgB;QAEhB,IAAI,iBAAiB,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YACrC,IAAA,KAAqC,qBAAqB,CAC9D,WAAW,EACX,SAAS,EACT,OAAO,CACR,EAJM,cAAc,QAAA,EAAE,gBAAgB,QAItC,CAAC;YACF,IAAI,gBAAgB,CAAC,MAAM,EAAE;gBAC3B,MAAM,IAAI,KAAK,CACb,kEAAkE,CACnE,CAAC;aACH;YACD,OAAO,IAAI,eAAe,CACxB,OAAO,EACP,WAAW,EACX,WAAW,EACX,OAAO,EACP,cAAc,CACf,CAAC;SACH;QACD,OAAO,IAAI,eAAe,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE;YACrE,IAAI,eAAe,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,CAAC;SACvD,CAAC,CAAC;IACL,CAAC,CAAC;QAEW,oBAAoB,GAAG,UAClC,KAAqB,EACrB,WAAuB,EACvB,EAA8C;QAD9C,4BAAA,EAAA,kBAAuB;YACvB,qBAA4C,EAAE,KAAA,EAA5C,OAAO,aAAA,EAAE,UAAU,gBAAA;QAErB,IAAM,OAAO,GAAG;YACd,OAAO,EAAE,OAAO,IAAI,MAAM;YAC1B,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,IAAI,EAAE,CAAC;SAChD,CAAC;QAEI,IAAA,KAAqC,qBAAqB,CAC9D,KAAK,EACL,IAAI,EACJ,OAAO,CACR,EAJM,cAAc,QAAA,EAAE,gBAAgB,QAItC,CAAC;QAEF,IAAM,GAAG,GAAG,EAAE,CAAC;QAEf,IAAI,cAAc,CAAC,MAAM,EAAE;YACzB,GAAG,CAAC,IAAI,CACN,IAAI,eAAe,CAAC,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,cAAc,CAAC,CACrE,CAAC;SACH;QAED,GAAG,CAAC,IAAI,OAAR,GAAG,EAAS,gBAAgB,EAAE;QAE9B,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;SACf;QACD,OAAO,IAAI,cAAc,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAC9D,EAAE;IAEF,IAAM,qBAAqB,GAAG,UAC5B,KAAU,EACV,SAAiB,EACjB,OAAgB;QAEhB,IAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,IAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;YAC3B,cAAc,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,OAAO,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;SAC3C;QACD,KAAK,IAAM,GAAG,IAAI,KAAK,EAAE;YACvB,IAAI,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBAC1C,IAAM,EAAE,GAAG,oBAAoB,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAEjE,IAAI,EAAE,EAAE;oBACN,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;wBAC7D,MAAM,IAAI,KAAK,CACb,sBAAoB,GAAG,yCAAsC,CAC9D,CAAC;qBACH;iBACF;;gBAGD,IAAI,EAAE,IAAI,IAAI,EAAE;oBACd,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;iBACzB;aACF;iBAAM,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAChC,yBAAyB,CAAC,GAAG,CAAC,CAAC;aAChC;iBAAM;gBACL,gBAAgB,CAAC,IAAI,CACnB,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,CACvE,CAAC;aACH;SACF;QAED,OAAO,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;IAC5C,CAAC,CAAC;QAEW,qBAAqB,GAAG,UAAQ,SAA2B,IAAK,OAAA,UAC3E,IAAW,EACX,GAAS,EACT,KAAW;QAEX,SAAS,CAAC,KAAK,EAAE,CAAC;QAClB,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACjC,OAAO,SAAS,CAAC,IAAI,CAAC;IACxB,CAAC,IAAC;QAEW,iBAAiB,GAAG,UAC/B,KAAqB,EACrB,OAA8B;QAA9B,wBAAA,EAAA,YAA8B;QAE9B,OAAO,qBAAqB,CAC1B,oBAAoB,CAAiB,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAC3D,CAAC;IACJ;;IC/cA;QAAkB,uBAAkB;QAApC;YAAA,qEAgBC;YAfU,YAAM,GAAG,IAAI,CAAC;;SAexB;QAbC,kBAAI,GAAJ;YACE,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC9D;QACD,mBAAK,GAAL;YACE,iBAAM,KAAK,WAAE,CAAC;YACd,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SAClB;QACD,kBAAI,GAAJ,UAAK,IAAS;YACZ,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;aACnB;SACF;QACH,UAAC;IAAD,CAhBA,CAAkB,aAAa,GAgB9B;IACD;IACA;QAAyB,8BAAyB;QAAlD;YAAA,qEAkCC;YAjCU,YAAM,GAAG,IAAI,CAAC;;SAiCxB;QA/BC,yBAAI,GAAJ;YACE,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;gBACnD,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;aACnE;YACD,IAAI,CAAC,eAAe,GAAG,oBAAoB,CACzC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,OAAO,CACb,CAAC;SACH;QACD,0BAAK,GAAL;YACE,iBAAM,KAAK,WAAE,CAAC;YACd,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;QACD,yBAAI,GAAJ,UAAK,IAAS;YACZ,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;gBACjB,KAAS,IAAA,CAAC,GAAG,CAAC,EAAI,QAAM,GAAK,IAAI,OAAT,EAAW,CAAC,GAAG,QAAM,EAAE,CAAC,EAAE,EAAE;;;oBAGlD,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;oBAE7B,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;oBACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;oBACjD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;iBACpD;gBACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;aAClB;iBAAM;gBACL,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;gBAClB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;aACnB;SACF;QACH,iBAAC;IAAD,CAlCA,CAAyB,aAAa,GAkCrC;IAED;QAAmB,wBAAyB;QAA5C;YAAA,qEAmBC;YAlBU,YAAM,GAAG,IAAI,CAAC;;SAkBxB;QAhBC,mBAAI,GAAJ;YACE,IAAI,CAAC,eAAe,GAAG,oBAAoB,CACzC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,OAAO,CACb,CAAC;SACH;QACD,oBAAK,GAAL;YACE,iBAAM,KAAK,WAAE,CAAC;YACd,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;SAC9B;QACD,mBAAI,GAAJ,UAAK,IAAS,EAAE,GAAQ,EAAE,KAAU,EAAE,IAAa;YACjD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACtC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;SACxC;QACH,WAAC;IAAD,CAnBA,CAAmB,aAAa,GAmB/B;;QAE0B,yBAAkB;QAA7C;YAAA,qEAaC;YAZU,YAAM,GAAG,IAAI,CAAC;;SAYxB;QAXC,oBAAI,GAAJ,eAAS;QACT,oBAAI,GAAJ,UAAK,IAAI;YACP,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;gBAChD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;aAClB;;;;;SAKF;QACH,YAAC;IAAD,CAbA,CAA2B,aAAa,GAavC;IAED,IAAM,mBAAmB,GAAG,UAAC,MAAa;QACxC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;IACH,CAAC,CAAC;IAEF;QAAkB,uBAAkB;QAApC;YAAA,qEAgCC;YA/BU,YAAM,GAAG,KAAK,CAAC;;SA+BzB;QA7BC,kBAAI,GAAJ;YAAA,iBAKC;YAJC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,EAAE;gBAC5B,OAAA,oBAAoB,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,OAAO,CAAC;aAAA,CAC7C,CAAC;SACH;QACD,mBAAK,GAAL;YACE,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;YAClB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;YAClB,KAAS,IAAA,CAAC,GAAG,CAAC,EAAI,QAAM,GAAK,IAAI,CAAC,IAAI,OAAd,EAAgB,CAAC,GAAG,QAAM,EAAE,CAAC,EAAE,EAAE;gBACvD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;aACtB;SACF;QACD,kBAAI,GAAJ,UAAK,IAAS,EAAE,GAAQ,EAAE,KAAU;YAClC,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,KAAS,IAAA,CAAC,GAAG,CAAC,EAAI,QAAM,GAAK,IAAI,CAAC,IAAI,OAAd,EAAgB,CAAC,GAAG,QAAM,EAAE,CAAC,EAAE,EAAE;gBACvD,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxB,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC1B,IAAI,EAAE,CAAC,IAAI,EAAE;oBACX,IAAI,GAAG,IAAI,CAAC;oBACZ,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC;oBAClB,MAAM;iBACP;aACF;YAED,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;YACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SAClB;QACH,UAAC;IAAD,CAhCA,CAAkB,aAAa,GAgC9B;IAED;QAAmB,wBAAG;QAAtB;YAAA,qEAMC;YALU,YAAM,GAAG,KAAK,CAAC;;SAKzB;QAJC,mBAAI,GAAJ,UAAK,IAAS,EAAE,GAAQ,EAAE,KAAU;YAClC,iBAAM,IAAI,YAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7B,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;SACxB;QACH,WAAC;IAAD,CANA,CAAmB,GAAG,GAMrB;IAED;QAAkB,uBAAkB;QAApC;YAAA,qEA0BC;YAzBU,YAAM,GAAG,IAAI,CAAC;;SAyBxB;QAvBC,kBAAI,GAAJ;YAAA,iBAOC;YANC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK;gBACnC,IAAI,iBAAiB,CAAC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,EAAE;oBAC1C,MAAM,IAAI,KAAK,CAAC,yBAAuB,KAAI,CAAC,IAAI,CAAC,WAAW,EAAI,CAAC,CAAC;iBACnE;gBACD,OAAO,YAAY,CAAC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aAClD,CAAC,CAAC;SACJ;QACD,kBAAI,GAAJ,UAAK,IAAS,EAAE,GAAQ,EAAE,KAAU;YAClC,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,KAAS,IAAA,CAAC,GAAG,CAAC,EAAI,QAAM,GAAK,IAAI,CAAC,QAAQ,OAAlB,EAAoB,CAAC,GAAG,QAAM,EAAE,CAAC,EAAE,EAAE;gBAC3D,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;oBACd,IAAI,GAAG,IAAI,CAAC;oBACZ,OAAO,GAAG,IAAI,CAAC;oBACf,MAAM;iBACP;aACF;YAED,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;YACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SAClB;QACH,UAAC;IAAD,CA1BA,CAAkB,aAAa,GA0B9B;IAED;QAAmB,wBAAkB;QAGnC,cAAY,MAAW,EAAE,UAAe,EAAE,OAAgB,EAAE,IAAY;YAAxE,YACE,kBAAM,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,SAEzC;YALQ,YAAM,GAAG,IAAI,CAAC;YAIrB,KAAI,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;;SACvD;QACD,mBAAI,GAAJ,UAAK,IAAS,EAAE,GAAQ,EAAE,KAAU,EAAE,IAAa;YACjD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAEhC,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC3B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;oBACjB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;oBAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;iBAClB;qBAAM,IAAI,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;oBACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;iBAClB;aACF;iBAAM;gBACL,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;aAClB;SACF;QACD,oBAAK,GAAL;YACE,iBAAM,KAAK,WAAE,CAAC;YACd,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;SAClB;QACH,WAAC;IAAD,CA3BA,CAAmB,aAAa,GA2B/B;IAED;QAAsB,2BAAsB;QAA5C;YAAA,qEAQC;YAPU,YAAM,GAAG,IAAI,CAAC;;SAOxB;QANC,sBAAI,GAAJ,UAAK,IAAS,EAAE,GAAQ,EAAE,KAAU;YAClC,IAAI,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;gBAC7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;aAClB;SACF;QACH,cAAC;IAAD,CARA,CAAsB,aAAa,GAQlC;IAED;QAAmB,wBAAmB;QAEpC,cACE,MAAoB,EACpB,WAAuB,EACvB,OAAgB,EAChB,IAAY;YAJd,YAME,kBACE,MAAM,EACN,WAAW,EACX,OAAO,EACP,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,oBAAoB,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,GAAA,CAAC,EACtE,IAAI,CACL,SAGF;YAhBQ,YAAM,GAAG,KAAK,CAAC;YAetB,mBAAmB,CAAC,MAAM,CAAC,CAAC;;SAC7B;QACD,mBAAI,GAAJ,UAAK,IAAS,EAAE,GAAQ,EAAE,KAAU,EAAE,IAAa;YACjD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SAC3C;QACH,WAAC;IAAD,CArBA,CAAmB,mBAAmB,GAqBrC;IAED;QAAmB,wBAAmB;QAEpC,cACE,MAAoB,EACpB,WAAuB,EACvB,OAAgB,EAChB,IAAY;YAJd,YAME,kBACE,MAAM,EACN,WAAW,EACX,OAAO,EACP,MAAM,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,oBAAoB,CAAC,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,GAAA,CAAC,EACtE,IAAI,CACL,SACF;YAdQ,YAAM,GAAG,IAAI,CAAC;;SActB;QACD,mBAAI,GAAJ,UAAK,IAAS,EAAE,GAAQ,EAAE,KAAU,EAAE,IAAa;YACjD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SAC3C;QACH,WAAC;IAAD,CAnBA,CAAmB,mBAAmB,GAmBrC;QAEY,GAAG,GAAG,UAAC,MAAW,EAAE,WAAuB,EAAE,OAAgB;QACxE,OAAA,IAAI,eAAe,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC;IAAjD,EAAkD;QACvC,GAAG,GAAG,UACjB,MAAW,EACX,WAAuB,EACvB,OAAgB,EAChB,IAAY,IACT,OAAA,IAAI,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,IAAC;QACpC,GAAG,GAAG,UACjB,MAAoB,EACpB,WAAuB,EACvB,OAAgB,EAChB,IAAY,IACT,OAAA,IAAI,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,IAAC;QACpC,IAAI,GAAG,UAClB,MAAoB,EACpB,WAAuB,EACvB,OAAgB,EAChB,IAAY,IACT,OAAA,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,IAAC;QACrC,UAAU,GAAG,UACxB,MAAW,EACX,WAAuB,EACvB,OAAgB,EAChB,IAAY,IACT,OAAA,IAAI,UAAU,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,IAAC;QAC3C,IAAI,GAAG,UAClB,MAAW,EACX,WAAuB,EACvB,OAAgB,EAChB,IAAY,IACT,OAAA,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,IAAC;QACrC,GAAG,GAAG,UACjB,MAAW,EACX,WAAuB,EACvB,OAAgB,EAChB,IAAY;QAEZ,OAAO,IAAI,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACrD,EAAE;QAEW,GAAG,GAAG,kBAAkB,CAAC,UAAA,MAAM,IAAI,OAAA,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,MAAM,GAAA,GAAA,EAAE;QACpD,IAAI,GAAG,kBAAkB,CAAC,UAAA,MAAM,IAAI,OAAA,UAAA,CAAC,IAAI,OAAA,CAAC,IAAI,MAAM,GAAA,GAAA,EAAE;QACtD,GAAG,GAAG,kBAAkB,CAAC,UAAA,MAAM,IAAI,OAAA,UAAA,CAAC,IAAI,OAAA,CAAC,GAAG,MAAM,GAAA,GAAA,EAAE;QACpD,IAAI,GAAG,kBAAkB,CAAC,UAAA,MAAM,IAAI,OAAA,UAAA,CAAC,IAAI,OAAA,CAAC,IAAI,MAAM,GAAA,GAAA,EAAE;QACtD,IAAI,GAAG,UAClB,EAA4B,EAC5B,WAAuB,EACvB,OAAgB;YAFf,GAAG,QAAA,EAAE,WAAW,QAAA;QAIjB,OAAA,IAAI,eAAe,CACjB,UAAA,CAAC,IAAI,OAAA,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,WAAW,GAAA,EACxC,WAAW,EACX,OAAO,CACR;IAJD,EAIE;QACS,OAAO,GAAG,UACrB,MAAe,EACf,WAAuB,EACvB,OAAgB,EAChB,IAAY,IACT,OAAA,IAAI,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,IAAC;QACxC,MAAM,GAAG,UACpB,OAAe,EACf,WAAuB,EACvB,OAAgB;QAEhB,OAAA,IAAI,eAAe,CACjB,IAAI,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,EACzC,WAAW,EACX,OAAO,CACR;IAJD,EAIE;QACS,IAAI,GAAG,UAClB,MAAW,EACX,WAAuB,EACvB,OAAgB,EAChB,IAAY,IACT,OAAA,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,IAAC;IAElD,IAAM,WAAW,GAAG;QAClB,MAAM,EAAE,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,KAAK,QAAQ,GAAA;QAClC,MAAM,EAAE,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,KAAK,QAAQ,GAAA;QAClC,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,OAAO,CAAC,KAAK,SAAS,GAAA;QACjC,KAAK,EAAE,UAAA,CAAC,IAAI,OAAA,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAA;QAC5B,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,KAAK,IAAI,GAAA;QACrB,SAAS,EAAE,UAAA,CAAC,IAAI,OAAA,CAAC,YAAY,IAAI,GAAA;KAClC,CAAC;QAEW,KAAK,GAAG,UACnB,KAAwB,EACxB,WAAuB,EACvB,OAAgB;QAEhB,OAAA,IAAI,eAAe,CACjB,UAAA,CAAC;YACC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;oBACvB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;iBAC9C;gBAED,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9B;YAED,OAAO,CAAC,IAAI,IAAI,GAAG,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,GAAG,KAAK,CAAC;SAC1E,EACD,WAAW,EACX,OAAO,CACR;IAdD,EAcE;QACS,IAAI,GAAG,UAClB,MAAoB,EACpB,UAAsB,EACtB,OAAgB,EAChB,IAAY,IACT,OAAA,IAAI,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,IAAC;QAEpC,IAAI,GAAG,UAClB,MAAoB,EACpB,UAAsB,EACtB,OAAgB,EAChB,IAAY,IACT,OAAA,IAAI,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,IAAC;QACpC,KAAK,GAAG,UACnB,MAAc,EACd,UAAsB,EACtB,OAAgB,IACb,OAAA,IAAI,KAAK,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,IAAC;QACxC,QAAQ,GAAG,cAAM,OAAA,IAAI,IAAC;QACtB,MAAM,GAAG,UACpB,MAAyB,EACzB,UAAsB,EACtB,OAAgB;QAEhB,IAAI,IAAI,CAAC;QAET,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE;YACtB,IAAI,GAAG,MAAM,CAAC;SACf;aAEM;YACL,MAAM,IAAI,KAAK,CACb,oEAAkE,CACnE,CAAC;SACH;QAED,OAAO,IAAI,eAAe,CAAC,UAAA,CAAC,IAAI,OAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAA,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACxE;;;;;;;;;;;;;;;;;;;;;;;;;;;;QCzYM,2BAA2B,GAAG,UAClC,KAAqB,EACrB,UAAe,EACf,EAA8C;YAA9C,qBAA4C,EAAE,KAAA,EAA5C,OAAO,aAAA,EAAE,UAAU,gBAAA;QAErB,OAAO,oBAAoB,CAAC,KAAK,EAAE,UAAU,EAAE;YAC7C,OAAO,SAAA;YACP,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,iBAAiB,EAAE,UAAU,IAAI,EAAE,CAAC;SACnE,CAAC,CAAC;IACL,EAAE;IAEF,IAAM,wBAAwB,GAAG,UAC/B,KAAqB,EACrB,OAA8B;QAA9B,wBAAA,EAAA,YAA8B;QAE9B,IAAM,EAAE,GAAG,2BAA2B,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7D,OAAO,qBAAqB,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}