{"name": "citypulse-backend", "version": "1.0.0", "description": "Backend API for CityPulse - Real-Time Urban Problem Reporter", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["civic-tech", "urban-issues", "api", "mongodb"], "author": "CityPulse Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "joi": "^17.9.2", "axios": "^1.5.0", "sharp": "^0.32.5", "cloudinary": "^1.40.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3"}}