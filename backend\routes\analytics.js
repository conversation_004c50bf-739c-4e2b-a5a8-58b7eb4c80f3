const express = require('express');
const mongoose = require('mongoose');
const Issue = require('../models/Issue');
const User = require('../models/User');
const Vote = require('../models/Vote');
const { authenticateToken, requireModerator, optionalAuth } = require('../middleware/auth');
const { catchAsync, AppError } = require('../middleware/errorHandler');

const router = express.Router();

// @route   GET /api/analytics/overview
// @desc    Get general analytics overview
// @access  Public
router.get('/overview',
  catchAsync(async (req, res) => {
    const [
      totalIssues,
      openIssues,
      resolvedIssues,
      totalUsers,
      activeUsers,
      totalVotes
    ] = await Promise.all([
      Issue.countDocuments(),
      Issue.countDocuments({ status: 'open' }),
      Issue.countDocuments({ status: 'resolved' }),
      User.countDocuments(),
      User.countDocuments({ isActive: true }),
      Vote.countDocuments({ isActive: true })
    ]);

    // Calculate resolution rate
    const resolutionRate = totalIssues > 0 ? ((resolvedIssues / totalIssues) * 100).toFixed(1) : 0;

    // Get issues by category
    const issuesByCategory = await Issue.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get issues by status
    const issuesByStatus = await Issue.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      status: 'success',
      data: {
        overview: {
          totalIssues,
          openIssues,
          resolvedIssues,
          resolutionRate: parseFloat(resolutionRate),
          totalUsers,
          activeUsers,
          totalVotes
        },
        issuesByCategory,
        issuesByStatus
      }
    });
  })
);

// @route   GET /api/analytics/trends
// @desc    Get issue trends over time
// @access  Public
router.get('/trends',
  catchAsync(async (req, res) => {
    const days = parseInt(req.query.days) || 30;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Issues created over time
    const issuesTrend = await Issue.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 },
          categories: {
            $push: '$category'
          }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
      }
    ]);

    // Issues resolved over time
    const resolutionTrend = await Issue.aggregate([
      {
        $match: {
          resolvedAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$resolvedAt' },
            month: { $month: '$resolvedAt' },
            day: { $dayOfMonth: '$resolvedAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
      }
    ]);

    res.json({
      status: 'success',
      data: {
        period: `${days} days`,
        issuesTrend,
        resolutionTrend
      }
    });
  })
);

// @route   GET /api/analytics/geographic
// @desc    Get geographic distribution of issues
// @access  Public
router.get('/geographic',
  catchAsync(async (req, res) => {
    // Issues by city
    const issuesByCity = await Issue.aggregate([
      {
        $group: {
          _id: '$address.city',
          count: { $sum: 1 },
          categories: {
            $push: '$category'
          },
          statuses: {
            $push: '$status'
          }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 20 }
    ]);

    // Issues by state
    const issuesByState = await Issue.aggregate([
      {
        $group: {
          _id: '$address.state',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Hotspots (areas with high issue density)
    const hotspots = await Issue.aggregate([
      {
        $group: {
          _id: {
            city: '$address.city',
            state: '$address.state'
          },
          count: { $sum: 1 },
          openIssues: {
            $sum: {
              $cond: [{ $eq: ['$status', 'open'] }, 1, 0]
            }
          },
          avgVoteScore: { $avg: '$votes.score' }
        }
      },
      {
        $match: { count: { $gte: 5 } }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      status: 'success',
      data: {
        issuesByCity,
        issuesByState,
        hotspots
      }
    });
  })
);

// @route   GET /api/analytics/urban-health-score
// @desc    Calculate Urban Health Score for cities
// @access  Public
router.get('/urban-health-score',
  catchAsync(async (req, res) => {
    const city = req.query.city;
    const state = req.query.state;

    let matchCondition = {};
    if (city) matchCondition['address.city'] = new RegExp(city, 'i');
    if (state) matchCondition['address.state'] = new RegExp(state, 'i');

    const healthData = await Issue.aggregate([
      { $match: matchCondition },
      {
        $group: {
          _id: {
            city: '$address.city',
            state: '$address.state'
          },
          totalIssues: { $sum: 1 },
          openIssues: {
            $sum: {
              $cond: [{ $eq: ['$status', 'open'] }, 1, 0]
            }
          },
          resolvedIssues: {
            $sum: {
              $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0]
            }
          },
          avgResolutionTime: {
            $avg: {
              $cond: [
                { $ne: ['$resolvedAt', null] },
                {
                  $divide: [
                    { $subtract: ['$resolvedAt', '$createdAt'] },
                    1000 * 60 * 60 * 24 // Convert to days
                  ]
                },
                null
              ]
            }
          },
          avgVoteScore: { $avg: '$votes.score' },
          criticalIssues: {
            $sum: {
              $cond: [{ $eq: ['$priority', 'critical'] }, 1, 0]
            }
          }
        }
      },
      {
        $addFields: {
          resolutionRate: {
            $cond: [
              { $gt: ['$totalIssues', 0] },
              { $multiply: [{ $divide: ['$resolvedIssues', '$totalIssues'] }, 100] },
              0
            ]
          },
          healthScore: {
            $let: {
              vars: {
                resolutionFactor: {
                  $cond: [
                    { $gt: ['$totalIssues', 0] },
                    { $multiply: [{ $divide: ['$resolvedIssues', '$totalIssues'] }, 40] },
                    0
                  ]
                },
                responsivenessFactor: {
                  $cond: [
                    { $and: [{ $ne: ['$avgResolutionTime', null] }, { $gt: ['$avgResolutionTime', 0] }] },
                    { $multiply: [{ $divide: [30, '$avgResolutionTime'] }, 30] },
                    0
                  ]
                },
                communityFactor: {
                  $cond: [
                    { $gt: ['$avgVoteScore', 0] },
                    { $multiply: [{ $min: ['$avgVoteScore', 10] }, 2] },
                    0
                  ]
                },
                criticalPenalty: { $multiply: ['$criticalIssues', -5] }
              },
              in: {
                $max: [
                  0,
                  {
                    $min: [
                      100,
                      {
                        $add: [
                          '$$resolutionFactor',
                          '$$responsivenessFactor',
                          '$$communityFactor',
                          '$$criticalPenalty'
                        ]
                      }
                    ]
                  }
                ]
              }
            }
          }
        }
      },
      { $sort: { healthScore: -1 } }
    ]);

    res.json({
      status: 'success',
      data: {
        urbanHealthScores: healthData.map(item => ({
          ...item,
          healthScore: Math.round(item.healthScore * 10) / 10,
          resolutionRate: Math.round(item.resolutionRate * 10) / 10,
          avgResolutionTime: item.avgResolutionTime ? Math.round(item.avgResolutionTime * 10) / 10 : null
        }))
      }
    });
  })
);

// @route   GET /api/analytics/user-engagement
// @desc    Get user engagement statistics
// @access  Private/Moderator
router.get('/user-engagement',
  authenticateToken,
  requireModerator,
  catchAsync(async (req, res) => {
    const days = parseInt(req.query.days) || 30;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Active users (users who reported issues or voted)
    const activeUsers = await User.aggregate([
      {
        $lookup: {
          from: 'issues',
          localField: '_id',
          foreignField: 'reportedBy',
          as: 'reportedIssues',
          pipeline: [
            { $match: { createdAt: { $gte: startDate } } }
          ]
        }
      },
      {
        $lookup: {
          from: 'votes',
          localField: '_id',
          foreignField: 'user',
          as: 'votes',
          pipeline: [
            { $match: { createdAt: { $gte: startDate }, isActive: true } }
          ]
        }
      },
      {
        $addFields: {
          recentActivity: {
            $add: [
              { $size: '$reportedIssues' },
              { $size: '$votes' }
            ]
          }
        }
      },
      {
        $match: { recentActivity: { $gt: 0 } }
      },
      {
        $project: {
          username: 1,
          fullName: 1,
          credibilityScore: 1,
          recentIssues: { $size: '$reportedIssues' },
          recentVotes: { $size: '$votes' },
          recentActivity: 1
        }
      },
      { $sort: { recentActivity: -1 } },
      { $limit: 50 }
    ]);

    // Top contributors
    const topContributors = await User.find({ isActive: true })
      .select('username fullName credibilityScore statistics')
      .sort({ 'statistics.issuesReported': -1 })
      .limit(10);

    res.json({
      status: 'success',
      data: {
        period: `${days} days`,
        activeUsersCount: activeUsers.length,
        activeUsers,
        topContributors
      }
    });
  })
);

module.exports = router;
