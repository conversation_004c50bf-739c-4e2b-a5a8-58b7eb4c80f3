const express = require('express');
const axios = require('axios');
const Issue = require('../models/Issue');
const User = require('../models/User');
const { authenticateToken, requireModerator, optionalAuth } = require('../middleware/auth');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { validate, issueCreationSchema, issueUpdateSchema, queryParamsSchema, locationQuerySchema } = require('../utils/validation');
const { uploadMultiple, processImage, generateThumbnail, deleteFile, handleUploadError } = require('../utils/fileUpload');
const aiService = require('../services/aiService');

const router = express.Router();

// @route   POST /api/issues
// @desc    Create a new issue
// @access  Private
// Middleware to parse JSON strings from FormData
const parseFormDataJSON = (req, res, next) => {
  try {
    if (req.body.location) {
      req.body.location = JSON.parse(req.body.location);
    }
    if (req.body.address) {
      req.body.address = JSON.parse(req.body.address);
    }
    if (req.body.tags) {
      req.body.tags = JSON.parse(req.body.tags);
    }
  } catch (error) {
    return res.status(400).json({
      status: 'fail',
      message: 'Invalid JSON format in form data'
    });
  }
  next();
};

router.post('/',
  authenticateToken,
  uploadMultiple,
  handleUploadError,
  parseFormDataJSON,
  validate(issueCreationSchema),
  catchAsync(async (req, res) => {
    const { title, description, category, location, address, tags } = req.body;

    if (!req.files || req.files.length === 0) {
      throw new AppError('At least one image is required', 400);
    }

    // Process uploaded images
    const images = [];
    for (const file of req.files) {
      try {
        // Process main image
        const processedPath = await processImage(file.path);
        
        // Generate thumbnail
        const thumbnailPath = await generateThumbnail(processedPath);

        images.push({
          url: `/uploads/issues/${file.filename}`,
          filename: file.filename,
          size: file.size,
          mimetype: file.mimetype,
          uploadedAt: new Date()
        });
      } catch (error) {
        // Clean up uploaded files on error
        req.files.forEach(f => deleteFile(f.path));
        throw error;
      }
    }

    // Create issue
    const issue = new Issue({
      title,
      description,
      category,
      location: {
        type: 'Point',
        coordinates: location.coordinates
      },
      address,
      images,
      reportedBy: req.user._id,
      tags: tags || [],
      metadata: {
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip
      }
    });

    await issue.save();

    // Update user statistics
    await User.findByIdAndUpdate(req.user._id, {
      $inc: { 'statistics.issuesReported': 1 }
    });

    // Call AI service for smart classification
    try {
      const aiResult = await aiService.classifyIssue({
        title,
        description,
        imageUrl: images[0].url,
        category
      });

      if (aiResult.success) {
        // Update issue with AI classification
        issue.aiClassification = {
          suggestedCategory: aiResult.suggestedCategory,
          confidence: aiResult.confidence,
          severity: aiResult.severity,
          source: aiResult.source,
          processedAt: new Date()
        };

        // Add AI-generated tags
        if (aiResult.tags && aiResult.tags.length > 0) {
          issue.tags = [...new Set([...issue.tags, ...aiResult.tags])];
        }

        // Update category if AI suggests a different one with high confidence
        if (aiResult.confidence > 0.8 && aiResult.suggestedCategory !== category) {
          issue.metadata.originalCategory = category;
          issue.metadata.aiCategoryChange = true;
          // Note: We keep user's category but store AI suggestion for analysis
        }

        await issue.save();
      }
    } catch (aiError) {
      console.error('AI classification error:', aiError.message);
      // Continue without AI classification - not critical for issue creation
    }

    // Populate user info for response
    await issue.populate('reportedBy', 'username fullName avatar');

    res.status(201).json({
      status: 'success',
      message: 'Issue reported successfully',
      data: {
        issue
      }
    });
  })
);

// @route   GET /api/issues
// @desc    Get all issues with filtering and pagination
// @access  Public
router.get('/',
  optionalAuth,
  validate(queryParamsSchema, 'query'),
  catchAsync(async (req, res) => {
    const { page, limit, sort, status, category, city, state, search } = req.query;

    // Build filter object
    const filter = {};
    
    if (status) filter.status = status;
    if (category) filter.category = category;
    if (city) filter['address.city'] = new RegExp(city, 'i');
    if (state) filter['address.state'] = new RegExp(state, 'i');
    
    if (search) {
      filter.$or = [
        { title: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') },
        { tags: new RegExp(search, 'i') }
      ];
    }

    const skip = (page - 1) * limit;

    // Execute query
    const issues = await Issue.find(filter)
      .populate('reportedBy', 'username fullName avatar credibilityScore')
      .populate('assignedTo', 'username fullName')
      .populate('resolvedBy', 'username fullName')
      .sort(sort || '-createdAt')
      .skip(skip)
      .limit(limit);

    const total = await Issue.countDocuments(filter);

    res.json({
      status: 'success',
      data: {
        issues,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  })
);

// @route   GET /api/issues/nearby
// @desc    Get nearby issues
// @access  Public
router.get('/nearby',
  validate(locationQuerySchema, 'query'),
  catchAsync(async (req, res) => {
    const { longitude, latitude, radius } = req.query;

    const issues = await Issue.find({
      location: {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [parseFloat(longitude), parseFloat(latitude)]
          },
          $maxDistance: parseInt(radius)
        }
      }
    })
    .populate('reportedBy', 'username fullName avatar')
    .limit(50); // Limit for performance

    res.json({
      status: 'success',
      data: {
        issues,
        center: {
          longitude: parseFloat(longitude),
          latitude: parseFloat(latitude)
        },
        radius: parseInt(radius)
      }
    });
  })
);

// @route   GET /api/issues/:id
// @desc    Get single issue by ID
// @access  Public
router.get('/:id',
  optionalAuth,
  catchAsync(async (req, res) => {
    const issue = await Issue.findById(req.params.id)
      .populate('reportedBy', 'username fullName avatar credibilityScore')
      .populate('assignedTo', 'username fullName')
      .populate('resolvedBy', 'username fullName')
      .populate('verifiedBy', 'username fullName')
      .populate('duplicateOf', 'title status')
      .populate('relatedIssues', 'title category status');

    if (!issue) {
      throw new AppError('Issue not found', 404);
    }

    res.json({
      status: 'success',
      data: {
        issue
      }
    });
  })
);

// @route   PUT /api/issues/:id
// @desc    Update issue
// @access  Private (Owner or Moderator)
router.put('/:id',
  authenticateToken,
  validate(issueUpdateSchema),
  catchAsync(async (req, res) => {
    const issue = await Issue.findById(req.params.id);

    if (!issue) {
      throw new AppError('Issue not found', 404);
    }

    // Check permissions
    const isOwner = issue.reportedBy.toString() === req.user._id.toString();
    const isModerator = ['admin', 'moderator'].includes(req.user.role);

    if (!isOwner && !isModerator) {
      throw new AppError('Not authorized to update this issue', 403);
    }

    // Owners can only update certain fields
    const allowedOwnerUpdates = ['title', 'description', 'tags'];
    const allowedModeratorUpdates = ['status', 'priority', 'assignedTo', 'resolutionNotes'];

    const updates = {};
    Object.keys(req.body).forEach(key => {
      if (isOwner && allowedOwnerUpdates.includes(key)) {
        updates[key] = req.body[key];
      } else if (isModerator && (allowedOwnerUpdates.includes(key) || allowedModeratorUpdates.includes(key))) {
        updates[key] = req.body[key];
      }
    });

    // Handle status changes
    if (updates.status === 'resolved' && !issue.resolvedAt) {
      updates.resolvedAt = new Date();
      updates.resolvedBy = req.user._id;
      
      // Update user statistics
      await User.findByIdAndUpdate(req.user._id, {
        $inc: { 'statistics.issuesResolved': 1 }
      });
    }

    const updatedIssue = await Issue.findByIdAndUpdate(
      req.params.id,
      updates,
      { new: true, runValidators: true }
    ).populate('reportedBy', 'username fullName avatar')
     .populate('assignedTo', 'username fullName')
     .populate('resolvedBy', 'username fullName');

    res.json({
      status: 'success',
      message: 'Issue updated successfully',
      data: {
        issue: updatedIssue
      }
    });
  })
);

// @route   PATCH /api/issues/:id/ai-classification
// @desc    Update issue with AI classification results
// @access  Public (called by AI service)
router.patch('/:id/ai-classification',
  catchAsync(async (req, res) => {
    const { aiClassification } = req.body;

    const issue = await Issue.findById(req.params.id);

    if (!issue) {
      throw new AppError('Issue not found', 404);
    }

    // Update AI classification
    issue.aiClassification = aiClassification;
    await issue.save();

    res.json({
      status: 'success',
      message: 'AI classification updated successfully',
      data: {
        issue
      }
    });
  })
);

module.exports = router;
