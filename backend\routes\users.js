const express = require('express');
const User = require('../models/User');
const { authenticateToken, requireAdmin, optionalAuth, generateToken } = require('../middleware/auth');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { validate, userRegistrationSchema, userLoginSchema } = require('../utils/validation');

const router = express.Router();

// @route   POST /api/users/register
// @desc    Register a new user
// @access  Public
router.post('/register', 
  validate(userRegistrationSchema),
  catchAsync(async (req, res) => {
    const { username, email, password, firstName, lastName, location } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      const field = existingUser.email === email ? 'email' : 'username';
      throw new AppError(`User with this ${field} already exists`, 400);
    }

    // Create new user
    const user = new User({
      username,
      email,
      password,
      firstName,
      lastName,
      location
    });

    await user.save();

    // Generate JWT token
    const token = generateToken(user._id);

    res.status(201).json({
      status: 'success',
      message: 'User registered successfully',
      data: {
        user,
        token
      }
    });
  })
);

// @route   POST /api/users/login
// @desc    Login user
// @access  Public
router.post('/login',
  validate(userLoginSchema),
  catchAsync(async (req, res) => {
    const { email, password } = req.body;

    // Find user and include password for comparison
    const user = await User.findOne({ email }).select('+password');

    if (!user || !(await user.comparePassword(password))) {
      throw new AppError('Invalid email or password', 401);
    }

    if (!user.isActive) {
      throw new AppError('Account has been deactivated', 401);
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT token
    const token = generateToken(user._id);

    // Remove password from response
    user.password = undefined;

    res.json({
      status: 'success',
      message: 'Login successful',
      data: {
        user,
        token
      }
    });
  })
);

// @route   GET /api/users/profile
// @desc    Get current user profile
// @access  Private
router.get('/profile',
  authenticateToken,
  catchAsync(async (req, res) => {
    res.json({
      status: 'success',
      data: {
        user: req.user
      }
    });
  })
);

// @route   PUT /api/users/profile
// @desc    Update user profile
// @access  Private
router.put('/profile',
  authenticateToken,
  catchAsync(async (req, res) => {
    const allowedUpdates = ['firstName', 'lastName', 'location'];
    const updates = {};

    // Filter allowed updates
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    const user = await User.findByIdAndUpdate(
      req.user._id,
      updates,
      { new: true, runValidators: true }
    );

    res.json({
      status: 'success',
      message: 'Profile updated successfully',
      data: {
        user
      }
    });
  })
);

// @route   PUT /api/users/change-password
// @desc    Change user password
// @access  Private
router.put('/change-password',
  authenticateToken,
  catchAsync(async (req, res) => {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      throw new AppError('Current password and new password are required', 400);
    }

    if (newPassword.length < 6) {
      throw new AppError('New password must be at least 6 characters long', 400);
    }

    // Get user with password
    const user = await User.findById(req.user._id).select('+password');

    // Verify current password
    if (!(await user.comparePassword(currentPassword))) {
      throw new AppError('Current password is incorrect', 400);
    }

    // Update password
    user.password = newPassword;
    await user.save();

    res.json({
      status: 'success',
      message: 'Password changed successfully'
    });
  })
);

// @route   GET /api/users
// @desc    Get all users (admin only)
// @access  Private/Admin
router.get('/',
  authenticateToken,
  requireAdmin,
  catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const users = await User.find({ isActive: true })
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await User.countDocuments({ isActive: true });

    res.json({
      status: 'success',
      data: {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  })
);

// @route   GET /api/users/:id
// @desc    Get user by ID
// @access  Public (limited info) / Private (full info if own profile)
router.get('/:id',
  optionalAuth,
  catchAsync(async (req, res) => {
    const user = await User.findById(req.params.id);

    if (!user || !user.isActive) {
      throw new AppError('User not found', 404);
    }

    // Return limited info for public access
    let userData = {
      _id: user._id,
      username: user.username,
      fullName: user.fullName,
      credibilityScore: user.credibilityScore,
      statistics: user.statistics,
      createdAt: user.createdAt
    };

    // Return full info if user is viewing own profile or is admin
    if (req.user && (req.user._id.toString() === user._id.toString() || req.user.role === 'admin')) {
      userData = user.toJSON();
    }

    res.json({
      status: 'success',
      data: {
        user: userData
      }
    });
  })
);

// @route   GET /api/users/leaderboard
// @desc    Get users leaderboard by credibility score
// @access  Public
router.get('/leaderboard',
  catchAsync(async (req, res) => {
    const limit = parseInt(req.query.limit) || 10;

    const users = await User.find({ isActive: true })
      .select('username fullName credibilityScore statistics avatar')
      .sort({ credibilityScore: -1 })
      .limit(limit);

    res.json({
      status: 'success',
      data: {
        leaderboard: users
      }
    });
  })
);

// @route   PUT /api/users/:id/deactivate
// @desc    Deactivate user account (admin only)
// @access  Private/Admin
router.put('/:id/deactivate',
  authenticateToken,
  requireAdmin,
  catchAsync(async (req, res) => {
    const user = await User.findById(req.params.id);

    if (!user) {
      throw new AppError('User not found', 404);
    }

    user.isActive = false;
    await user.save();

    res.json({
      status: 'success',
      message: 'User account deactivated successfully'
    });
  })
);

module.exports = router;
