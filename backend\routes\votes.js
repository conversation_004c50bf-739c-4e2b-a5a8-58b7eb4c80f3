const express = require('express');
const mongoose = require('mongoose');
const Vote = require('../models/Vote');
const Issue = require('../models/Issue');
const User = require('../models/User');
const { authenticateToken } = require('../middleware/auth');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { validate, voteSchema } = require('../utils/validation');

const router = express.Router();

// @route   POST /api/votes/issues/:issueId
// @desc    Vote on an issue
// @access  Private
router.post('/issues/:issueId',
  authenticateToken,
  validate(voteSchema),
  catchAsync(async (req, res) => {
    const { issueId } = req.params;
    const { voteType, reason } = req.body;

    // Check if issue exists
    const issue = await Issue.findById(issueId);
    if (!issue) {
      throw new AppError('Issue not found', 404);
    }

    // Check if user is trying to vote on their own issue
    if (issue.reportedBy.toString() === req.user._id.toString()) {
      throw new AppError('You cannot vote on your own issue', 400);
    }

    // Check if user has already voted
    const existingVote = await Vote.findOne({
      user: req.user._id,
      issue: issueId,
      isActive: true
    });

    if (existingVote) {
      // If same vote type, remove the vote
      if (existingVote.voteType === voteType) {
        existingVote.isActive = false;
        await existingVote.save();

        return res.json({
          status: 'success',
          message: 'Vote removed successfully',
          data: {
            action: 'removed',
            voteType: null
          }
        });
      } else {
        // Change vote type
        existingVote.voteType = voteType;
        existingVote.reason = reason;
        await existingVote.save();

        return res.json({
          status: 'success',
          message: 'Vote updated successfully',
          data: {
            action: 'updated',
            voteType: voteType,
            vote: existingVote
          }
        });
      }
    }

    // Create new vote
    const vote = new Vote({
      user: req.user._id,
      issue: issueId,
      voteType,
      reason,
      metadata: {
        ipAddress: req.ip,
        userAgent: req.headers['user-agent']
      }
    });

    await vote.save();

    // Update user statistics
    await User.findByIdAndUpdate(req.user._id, {
      $inc: { 'statistics.votesGiven': 1 }
    });

    // Update user credibility based on vote activity
    const user = await User.findById(req.user._id);
    if (user.statistics.votesGiven % 10 === 0) {
      // Increase credibility every 10 votes
      await user.updateCredibilityScore(5);
    }

    res.status(201).json({
      status: 'success',
      message: 'Vote recorded successfully',
      data: {
        action: 'created',
        voteType: voteType,
        vote
      }
    });
  })
);

// @route   GET /api/votes/issues/:issueId
// @desc    Get votes for an issue
// @access  Public
router.get('/issues/:issueId',
  catchAsync(async (req, res) => {
    const { issueId } = req.params;

    // Check if issue exists
    const issue = await Issue.findById(issueId);
    if (!issue) {
      throw new AppError('Issue not found', 404);
    }

    // Get vote statistics
    const voteStats = await Vote.aggregate([
      { $match: { issue: issue._id, isActive: true } },
      {
        $group: {
          _id: '$voteType',
          count: { $sum: 1 },
          totalWeight: { $sum: '$weight' }
        }
      }
    ]);

    // Get recent votes with user info
    const recentVotes = await Vote.find({
      issue: issueId,
      isActive: true
    })
    .populate('user', 'username fullName avatar credibilityScore')
    .sort({ createdAt: -1 })
    .limit(20);

    // Format statistics
    const stats = {
      upvotes: 0,
      downvotes: 0,
      totalVotes: 0,
      score: 0
    };

    voteStats.forEach(stat => {
      if (stat._id === 'upvote') {
        stats.upvotes = Math.round(stat.totalWeight * 10) / 10;
      } else if (stat._id === 'downvote') {
        stats.downvotes = Math.round(stat.totalWeight * 10) / 10;
      }
    });

    stats.totalVotes = recentVotes.length;
    stats.score = Math.round((stats.upvotes - stats.downvotes) * 10) / 10;

    res.json({
      status: 'success',
      data: {
        statistics: stats,
        recentVotes,
        issueVotes: {
          upvotes: issue.votes.upvotes,
          downvotes: issue.votes.downvotes,
          score: issue.votes.score
        }
      }
    });
  })
);

// @route   GET /api/votes/users/:userId/issues/:issueId
// @desc    Get user's vote on a specific issue
// @access  Private
router.get('/users/:userId/issues/:issueId',
  authenticateToken,
  catchAsync(async (req, res) => {
    const { userId, issueId } = req.params;

    // Users can only check their own votes unless they're admin
    if (req.user._id.toString() !== userId && req.user.role !== 'admin') {
      throw new AppError('Not authorized to view this vote', 403);
    }

    const vote = await Vote.findOne({
      user: userId,
      issue: issueId,
      isActive: true
    });

    res.json({
      status: 'success',
      data: {
        vote: vote || null,
        hasVoted: !!vote,
        voteType: vote ? vote.voteType : null
      }
    });
  })
);

// @route   GET /api/votes/users/:userId
// @desc    Get user's voting history
// @access  Private
router.get('/users/:userId',
  authenticateToken,
  catchAsync(async (req, res) => {
    const { userId } = req.params;

    // Users can only view their own voting history unless they're admin
    if (req.user._id.toString() !== userId && req.user.role !== 'admin') {
      throw new AppError('Not authorized to view this voting history', 403);
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const votes = await Vote.find({
      user: userId,
      isActive: true
    })
    .populate('issue', 'title category status createdAt address')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

    const total = await Vote.countDocuments({
      user: userId,
      isActive: true
    });

    // Get voting statistics
    const voteStats = await Vote.aggregate([
      { $match: { user: mongoose.Types.ObjectId(userId), isActive: true } },
      {
        $group: {
          _id: '$voteType',
          count: { $sum: 1 }
        }
      }
    ]);

    const statistics = {
      totalVotes: total,
      upvotes: 0,
      downvotes: 0
    };

    voteStats.forEach(stat => {
      if (stat._id === 'upvote') {
        statistics.upvotes = stat.count;
      } else if (stat._id === 'downvote') {
        statistics.downvotes = stat.count;
      }
    });

    res.json({
      status: 'success',
      data: {
        votes,
        statistics,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  })
);

// @route   DELETE /api/votes/:voteId
// @desc    Delete a vote
// @access  Private
router.delete('/:voteId',
  authenticateToken,
  catchAsync(async (req, res) => {
    const vote = await Vote.findById(req.params.voteId);

    if (!vote) {
      throw new AppError('Vote not found', 404);
    }

    // Users can only delete their own votes unless they're admin
    if (vote.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      throw new AppError('Not authorized to delete this vote', 403);
    }

    vote.isActive = false;
    await vote.save();

    res.json({
      status: 'success',
      message: 'Vote deleted successfully'
    });
  })
);

module.exports = router;
