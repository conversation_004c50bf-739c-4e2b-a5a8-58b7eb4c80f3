const axios = require('axios');

/**
 * AI Service for classifying urban issues
 * This service provides smart classification of urban issues using AI
 */
class AIService {
  constructor() {
    this.isEnabled = process.env.AI_SERVICE_URL ? true : false;
    this.serviceUrl = process.env.AI_SERVICE_URL;
  }

  /**
   * Classify an issue based on image and text description
   * @param {Object} issueData - The issue data to classify
   * @param {string} issueData.title - Issue title
   * @param {string} issueData.description - Issue description
   * @param {string} issueData.imageUrl - URL of the main image
   * @param {string} issueData.category - User-selected category
   * @returns {Object} Classification result
   */
  async classifyIssue(issueData) {
    try {
      // If external AI service is available, use it
      if (this.isEnabled) {
        return await this.callExternalAI(issueData);
      }
      
      // Fallback to rule-based classification
      return this.ruleBasedClassification(issueData);
    } catch (error) {
      console.error('AI Classification error:', error);
      // Return fallback classification on error
      return this.ruleBasedClassification(issueData);
    }
  }

  /**
   * Call external AI service for classification
   * @param {Object} issueData - Issue data
   * @returns {Object} AI classification result
   */
  async callExternalAI(issueData) {
    try {
      const response = await axios.post(`${this.serviceUrl}/classify`, {
        title: issueData.title,
        description: issueData.description,
        imageUrl: issueData.imageUrl,
        category: issueData.category
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return {
        success: true,
        confidence: response.data.confidence || 0.8,
        suggestedCategory: response.data.category || issueData.category,
        tags: response.data.tags || [],
        severity: response.data.severity || 'medium',
        aiGenerated: true,
        source: 'external_ai'
      };
    } catch (error) {
      console.error('External AI service error:', error.message);
      throw error;
    }
  }

  /**
   * Rule-based classification as fallback
   * @param {Object} issueData - Issue data
   * @returns {Object} Classification result
   */
  ruleBasedClassification(issueData) {
    const { title, description, category } = issueData;
    const text = `${title} ${description}`.toLowerCase();

    // Define keywords for each category
    const categoryKeywords = {
      pothole: ['pothole', 'hole', 'crack', 'road damage', 'asphalt', 'pavement'],
      garbage: ['garbage', 'trash', 'waste', 'litter', 'dump', 'dirty', 'smell'],
      water_leak: ['water', 'leak', 'pipe', 'burst', 'flooding', 'drain', 'sewage'],
      broken_light: ['light', 'lamp', 'street light', 'bulb', 'dark', 'lighting'],
      damaged_road: ['road', 'street', 'path', 'sidewalk', 'construction', 'barrier'],
      other: []
    };

    // Severity keywords
    const severityKeywords = {
      critical: ['emergency', 'urgent', 'dangerous', 'hazard', 'accident', 'blocked'],
      high: ['major', 'serious', 'important', 'large', 'big'],
      medium: ['moderate', 'medium', 'normal'],
      low: ['minor', 'small', 'little']
    };

    // Calculate category confidence
    let suggestedCategory = category;
    let maxScore = 0;

    for (const [cat, keywords] of Object.entries(categoryKeywords)) {
      const score = keywords.reduce((acc, keyword) => {
        return acc + (text.includes(keyword) ? 1 : 0);
      }, 0);

      if (score > maxScore) {
        maxScore = score;
        suggestedCategory = cat;
      }
    }

    // Determine severity
    let severity = 'medium';
    for (const [sev, keywords] of Object.entries(severityKeywords)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        severity = sev;
        break;
      }
    }

    // Generate tags based on content
    const tags = this.generateTags(text, suggestedCategory);

    // Calculate confidence based on keyword matches
    const confidence = Math.min(0.9, Math.max(0.6, maxScore * 0.2 + 0.6));

    return {
      success: true,
      confidence,
      suggestedCategory,
      tags,
      severity,
      aiGenerated: true,
      source: 'rule_based'
    };
  }

  /**
   * Generate relevant tags based on text content
   * @param {string} text - Text to analyze
   * @param {string} category - Issue category
   * @returns {Array} Generated tags
   */
  generateTags(text, category) {
    const tags = [];

    // Category-specific tags
    const categoryTags = {
      pothole: ['road-safety', 'infrastructure'],
      garbage: ['cleanliness', 'sanitation', 'environment'],
      water_leak: ['utilities', 'infrastructure', 'water-supply'],
      broken_light: ['safety', 'lighting', 'infrastructure'],
      damaged_road: ['transportation', 'infrastructure', 'road-safety'],
      other: ['general']
    };

    // Add category-specific tags
    if (categoryTags[category]) {
      tags.push(...categoryTags[category]);
    }

    // Add urgency tags
    if (text.includes('urgent') || text.includes('emergency')) {
      tags.push('urgent');
    }

    // Add location-based tags
    if (text.includes('school') || text.includes('hospital')) {
      tags.push('public-facility');
    }

    if (text.includes('main road') || text.includes('highway')) {
      tags.push('major-road');
    }

    // Remove duplicates and limit to 5 tags
    return [...new Set(tags)].slice(0, 5);
  }

  /**
   * Get AI service status
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      enabled: this.isEnabled,
      serviceUrl: this.serviceUrl,
      type: this.isEnabled ? 'external' : 'rule_based'
    };
  }
}

module.exports = new AIService();
