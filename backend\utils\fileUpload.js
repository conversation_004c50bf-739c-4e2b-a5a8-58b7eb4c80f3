const multer = require('multer');
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');
const { AppError } = require('../middleware/errorHandler');

// Ensure upload directory exists
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure multer storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const issueDir = path.join(uploadDir, 'issues');
    if (!fs.existsSync(issueDir)) {
      fs.mkdirSync(issueDir, { recursive: true });
    }
    cb(null, issueDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, `issue-${uniqueSuffix}${extension}`);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new AppError('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.', 400), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
    files: 5 // Maximum 5 files per request
  }
});

// Middleware for single file upload
const uploadSingle = upload.single('image');

// Middleware for multiple file upload
const uploadMultiple = upload.array('images', 5);

// Image processing function
const processImage = async (filePath, options = {}) => {
  try {
    const {
      width = 1200,
      height = 800,
      quality = 80,
      format = 'jpeg'
    } = options;

    const processedPath = filePath.replace(/\.[^/.]+$/, `_processed.${format}`);
    
    await sharp(filePath)
      .resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .jpeg({ quality })
      .toFile(processedPath);

    // Delete original file
    fs.unlinkSync(filePath);
    
    return processedPath;
  } catch (error) {
    console.error('Image processing error:', error);
    throw new AppError('Failed to process image', 500);
  }
};

// Generate thumbnail
const generateThumbnail = async (filePath, options = {}) => {
  try {
    const {
      width = 300,
      height = 200,
      quality = 70
    } = options;

    const thumbnailPath = filePath.replace(/\.[^/.]+$/, '_thumb.jpeg');
    
    await sharp(filePath)
      .resize(width, height, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality })
      .toFile(thumbnailPath);

    return thumbnailPath;
  } catch (error) {
    console.error('Thumbnail generation error:', error);
    throw new AppError('Failed to generate thumbnail', 500);
  }
};

// Delete file function
const deleteFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('File deletion error:', error);
    return false;
  }
};

// Clean up old files (for maintenance)
const cleanupOldFiles = (directory, maxAge = 30) => {
  try {
    const files = fs.readdirSync(directory);
    const now = Date.now();
    const maxAgeMs = maxAge * 24 * 60 * 60 * 1000; // Convert days to milliseconds

    files.forEach(file => {
      const filePath = path.join(directory, file);
      const stats = fs.statSync(filePath);
      
      if (now - stats.mtime.getTime() > maxAgeMs) {
        fs.unlinkSync(filePath);
        console.log(`Deleted old file: ${file}`);
      }
    });
  } catch (error) {
    console.error('Cleanup error:', error);
  }
};

// Get file info
const getFileInfo = (filePath) => {
  try {
    const stats = fs.statSync(filePath);
    return {
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      extension: path.extname(filePath),
      filename: path.basename(filePath)
    };
  } catch (error) {
    return null;
  }
};

// Validate image dimensions
const validateImageDimensions = async (filePath, minWidth = 100, minHeight = 100) => {
  try {
    const metadata = await sharp(filePath).metadata();
    
    if (metadata.width < minWidth || metadata.height < minHeight) {
      throw new AppError(`Image must be at least ${minWidth}x${minHeight} pixels`, 400);
    }
    
    return metadata;
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('Invalid image file', 400);
  }
};

// Middleware to handle file upload errors
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        status: 'fail',
        message: 'File too large. Maximum size is 10MB.'
      });
    }
    
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        status: 'fail',
        message: 'Too many files. Maximum 5 files allowed.'
      });
    }
    
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        status: 'fail',
        message: 'Unexpected file field.'
      });
    }
  }
  
  next(error);
};

module.exports = {
  uploadSingle,
  uploadMultiple,
  processImage,
  generateThumbnail,
  deleteFile,
  cleanupOldFiles,
  getFileInfo,
  validateImageDimensions,
  handleUploadError
};
