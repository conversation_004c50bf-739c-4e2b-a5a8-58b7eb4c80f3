const Joi = require('joi');

// User registration validation schema
const userRegistrationSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .messages({
      'string.alphanum': 'Username must contain only alphanumeric characters',
      'string.min': 'Username must be at least 3 characters long',
      'string.max': 'Username cannot exceed 30 characters',
      'any.required': 'Username is required'
    }),
  
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  
  password: Joi.string()
    .min(6)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)'))
    .required()
    .messages({
      'string.min': 'Password must be at least 6 characters long',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      'any.required': 'Password is required'
    }),
  
  firstName: Joi.string()
    .trim()
    .max(50)
    .required()
    .messages({
      'string.max': 'First name cannot exceed 50 characters',
      'any.required': 'First name is required'
    }),
  
  lastName: Joi.string()
    .trim()
    .max(50)
    .required()
    .messages({
      'string.max': 'Last name cannot exceed 50 characters',
      'any.required': 'Last name is required'
    }),
  
  location: Joi.object({
    city: Joi.string().trim().max(100),
    state: Joi.string().trim().max(100),
    country: Joi.string().trim().max(100).default('India')
  }).optional()
});

// User login validation schema
const userLoginSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required'
    })
});

// Issue creation validation schema
const issueCreationSchema = Joi.object({
  title: Joi.string()
    .trim()
    .max(100)
    .required()
    .messages({
      'string.max': 'Title cannot exceed 100 characters',
      'any.required': 'Issue title is required'
    }),
  
  description: Joi.string()
    .trim()
    .max(1000)
    .required()
    .messages({
      'string.max': 'Description cannot exceed 1000 characters',
      'any.required': 'Issue description is required'
    }),
  
  category: Joi.string()
    .valid('pothole', 'garbage', 'water_leak', 'broken_light', 'damaged_road', 'other')
    .required()
    .messages({
      'any.only': 'Category must be one of: pothole, garbage, water_leak, broken_light, damaged_road, other',
      'any.required': 'Issue category is required'
    }),
  
  location: Joi.object({
    coordinates: Joi.array()
      .items(Joi.number().min(-180).max(180))
      .length(2)
      .required()
      .messages({
        'array.length': 'Coordinates must contain exactly 2 values [longitude, latitude]',
        'any.required': 'Location coordinates are required'
      })
  }).required(),
  
  address: Joi.object({
    street: Joi.string().trim().max(200),
    city: Joi.string().trim().max(100).required(),
    state: Joi.string().trim().max(100).required(),
    pincode: Joi.string().pattern(/^\d{6}$/).messages({
      'string.pattern.base': 'Please enter a valid 6-digit pincode'
    }),
    country: Joi.string().trim().max(100).default('India')
  }).required(),
  
  tags: Joi.array()
    .items(Joi.string().trim().max(50))
    .max(10)
    .messages({
      'array.max': 'Maximum 10 tags allowed'
    })
});

// Issue update validation schema
const issueUpdateSchema = Joi.object({
  title: Joi.string().trim().max(100),
  description: Joi.string().trim().max(1000),
  category: Joi.string().valid('pothole', 'garbage', 'water_leak', 'broken_light', 'damaged_road', 'other'),
  status: Joi.string().valid('open', 'in_progress', 'resolved', 'closed', 'duplicate'),
  priority: Joi.string().valid('low', 'medium', 'high', 'critical'),
  resolutionNotes: Joi.string().trim().max(500),
  tags: Joi.array().items(Joi.string().trim().max(50)).max(10)
});

// Vote validation schema
const voteSchema = Joi.object({
  voteType: Joi.string()
    .valid('upvote', 'downvote')
    .required()
    .messages({
      'any.only': 'Vote type must be either upvote or downvote',
      'any.required': 'Vote type is required'
    }),
  
  reason: Joi.string()
    .trim()
    .max(200)
    .messages({
      'string.max': 'Reason cannot exceed 200 characters'
    })
});

// Query parameters validation schema
const queryParamsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  sort: Joi.string().valid('createdAt', '-createdAt', 'votes.score', '-votes.score', 'title', '-title'),
  status: Joi.string().valid('open', 'in_progress', 'resolved', 'closed', 'duplicate'),
  category: Joi.string().valid('pothole', 'garbage', 'water_leak', 'broken_light', 'damaged_road', 'other'),
  city: Joi.string().trim(),
  state: Joi.string().trim(),
  search: Joi.string().trim().max(100)
});

// Location query validation schema
const locationQuerySchema = Joi.object({
  longitude: Joi.number().min(-180).max(180).required(),
  latitude: Joi.number().min(-90).max(90).required(),
  radius: Joi.number().min(100).max(50000).default(1000) // in meters
});

// Validation middleware factory
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        status: 'fail',
        message: 'Validation error',
        errors: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }

    req[property] = value;
    next();
  };
};

module.exports = {
  userRegistrationSchema,
  userLoginSchema,
  issueCreationSchema,
  issueUpdateSchema,
  voteSchema,
  queryParamsSchema,
  locationQuerySchema,
  validate
};
