(function(){const u=document.createElement("link").relList;if(u&&u.supports&&u.supports("modulepreload"))return;for(const m of document.querySelectorAll('link[rel="modulepreload"]'))h(m);new MutationObserver(m=>{for(const _ of m)if(_.type==="childList")for(const v of _.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&h(v)}).observe(document,{childList:!0,subtree:!0});function c(m){const _={};return m.integrity&&(_.integrity=m.integrity),m.referrerPolicy&&(_.referrerPolicy=m.referrerPolicy),m.crossOrigin==="use-credentials"?_.credentials="include":m.crossOrigin==="anonymous"?_.credentials="omit":_.credentials="same-origin",_}function h(m){if(m.ep)return;m.ep=!0;const _=c(m);fetch(m.href,_)}})();function Cp(o){return o&&o.__esModule&&Object.prototype.hasOwnProperty.call(o,"default")?o.default:o}var Lf={exports:{}},ps={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Im;function Vg(){if(Im)return ps;Im=1;var o=Symbol.for("react.transitional.element"),u=Symbol.for("react.fragment");function c(h,m,_){var v=null;if(_!==void 0&&(v=""+_),m.key!==void 0&&(v=""+m.key),"key"in m){_={};for(var S in m)S!=="key"&&(_[S]=m[S])}else _=m;return m=_.ref,{$$typeof:o,type:h,key:v,ref:m!==void 0?m:null,props:_}}return ps.Fragment=u,ps.jsx=c,ps.jsxs=c,ps}var $m;function Xg(){return $m||($m=1,Lf.exports=Vg()),Lf.exports}var T=Xg(),Of={exports:{}},St={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tp;function Qg(){if(tp)return St;tp=1;var o=Symbol.for("react.transitional.element"),u=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),h=Symbol.for("react.strict_mode"),m=Symbol.for("react.profiler"),_=Symbol.for("react.consumer"),v=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),w=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),E=Symbol.for("react.lazy"),z=Symbol.iterator;function B(O){return O===null||typeof O!="object"?null:(O=z&&O[z]||O["@@iterator"],typeof O=="function"?O:null)}var J={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},N=Object.assign,U={};function Y(O,Q,nt){this.props=O,this.context=Q,this.refs=U,this.updater=nt||J}Y.prototype.isReactComponent={},Y.prototype.setState=function(O,Q){if(typeof O!="object"&&typeof O!="function"&&O!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,O,Q,"setState")},Y.prototype.forceUpdate=function(O){this.updater.enqueueForceUpdate(this,O,"forceUpdate")};function X(){}X.prototype=Y.prototype;function it(O,Q,nt){this.props=O,this.context=Q,this.refs=U,this.updater=nt||J}var et=it.prototype=new X;et.constructor=it,N(et,Y.prototype),et.isPureReactComponent=!0;var xt=Array.isArray,lt={H:null,A:null,T:null,S:null,V:null},Ct=Object.prototype.hasOwnProperty;function Nt(O,Q,nt,tt,st,ft){return nt=ft.ref,{$$typeof:o,type:O,key:Q,ref:nt!==void 0?nt:null,props:ft}}function Pt(O,Q){return Nt(O.type,Q,void 0,void 0,void 0,O.props)}function jt(O){return typeof O=="object"&&O!==null&&O.$$typeof===o}function me(O){var Q={"=":"=0",":":"=2"};return"$"+O.replace(/[=:]/g,function(nt){return Q[nt]})}var Xt=/\/+/g;function Zt(O,Q){return typeof O=="object"&&O!==null&&O.key!=null?me(""+O.key):Q.toString(36)}function ti(){}function Se(O){switch(O.status){case"fulfilled":return O.value;case"rejected":throw O.reason;default:switch(typeof O.status=="string"?O.then(ti,ti):(O.status="pending",O.then(function(Q){O.status==="pending"&&(O.status="fulfilled",O.value=Q)},function(Q){O.status==="pending"&&(O.status="rejected",O.reason=Q)})),O.status){case"fulfilled":return O.value;case"rejected":throw O.reason}}throw O}function pe(O,Q,nt,tt,st){var ft=typeof O;(ft==="undefined"||ft==="boolean")&&(O=null);var at=!1;if(O===null)at=!0;else switch(ft){case"bigint":case"string":case"number":at=!0;break;case"object":switch(O.$$typeof){case o:case u:at=!0;break;case E:return at=O._init,pe(at(O._payload),Q,nt,tt,st)}}if(at)return st=st(O),at=tt===""?"."+Zt(O,0):tt,xt(st)?(nt="",at!=null&&(nt=at.replace(Xt,"$&/")+"/"),pe(st,Q,nt,"",function(En){return En})):st!=null&&(jt(st)&&(st=Pt(st,nt+(st.key==null||O&&O.key===st.key?"":(""+st.key).replace(Xt,"$&/")+"/")+at)),Q.push(st)),1;at=0;var te=tt===""?".":tt+":";if(xt(O))for(var zt=0;zt<O.length;zt++)tt=O[zt],ft=te+Zt(tt,zt),at+=pe(tt,Q,nt,ft,st);else if(zt=B(O),typeof zt=="function")for(O=zt.call(O),zt=0;!(tt=O.next()).done;)tt=tt.value,ft=te+Zt(tt,zt++),at+=pe(tt,Q,nt,ft,st);else if(ft==="object"){if(typeof O.then=="function")return pe(Se(O),Q,nt,tt,st);throw Q=String(O),Error("Objects are not valid as a React child (found: "+(Q==="[object Object]"?"object with keys {"+Object.keys(O).join(", ")+"}":Q)+"). If you meant to render a collection of children, use an array instead.")}return at}function V(O,Q,nt){if(O==null)return O;var tt=[],st=0;return pe(O,tt,"","",function(ft){return Q.call(nt,ft,st++)}),tt}function rt(O){if(O._status===-1){var Q=O._result;Q=Q(),Q.then(function(nt){(O._status===0||O._status===-1)&&(O._status=1,O._result=nt)},function(nt){(O._status===0||O._status===-1)&&(O._status=2,O._result=nt)}),O._status===-1&&(O._status=0,O._result=Q)}if(O._status===1)return O._result.default;throw O._result}var $=typeof reportError=="function"?reportError:function(O){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof O=="object"&&O!==null&&typeof O.message=="string"?String(O.message):String(O),error:O});if(!window.dispatchEvent(Q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",O);return}console.error(O)};function Ut(){}return St.Children={map:V,forEach:function(O,Q,nt){V(O,function(){Q.apply(this,arguments)},nt)},count:function(O){var Q=0;return V(O,function(){Q++}),Q},toArray:function(O){return V(O,function(Q){return Q})||[]},only:function(O){if(!jt(O))throw Error("React.Children.only expected to receive a single React element child.");return O}},St.Component=Y,St.Fragment=c,St.Profiler=m,St.PureComponent=it,St.StrictMode=h,St.Suspense=w,St.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=lt,St.__COMPILER_RUNTIME={__proto__:null,c:function(O){return lt.H.useMemoCache(O)}},St.cache=function(O){return function(){return O.apply(null,arguments)}},St.cloneElement=function(O,Q,nt){if(O==null)throw Error("The argument must be a React element, but you passed "+O+".");var tt=N({},O.props),st=O.key,ft=void 0;if(Q!=null)for(at in Q.ref!==void 0&&(ft=void 0),Q.key!==void 0&&(st=""+Q.key),Q)!Ct.call(Q,at)||at==="key"||at==="__self"||at==="__source"||at==="ref"&&Q.ref===void 0||(tt[at]=Q[at]);var at=arguments.length-2;if(at===1)tt.children=nt;else if(1<at){for(var te=Array(at),zt=0;zt<at;zt++)te[zt]=arguments[zt+2];tt.children=te}return Nt(O.type,st,void 0,void 0,ft,tt)},St.createContext=function(O){return O={$$typeof:v,_currentValue:O,_currentValue2:O,_threadCount:0,Provider:null,Consumer:null},O.Provider=O,O.Consumer={$$typeof:_,_context:O},O},St.createElement=function(O,Q,nt){var tt,st={},ft=null;if(Q!=null)for(tt in Q.key!==void 0&&(ft=""+Q.key),Q)Ct.call(Q,tt)&&tt!=="key"&&tt!=="__self"&&tt!=="__source"&&(st[tt]=Q[tt]);var at=arguments.length-2;if(at===1)st.children=nt;else if(1<at){for(var te=Array(at),zt=0;zt<at;zt++)te[zt]=arguments[zt+2];st.children=te}if(O&&O.defaultProps)for(tt in at=O.defaultProps,at)st[tt]===void 0&&(st[tt]=at[tt]);return Nt(O,ft,void 0,void 0,null,st)},St.createRef=function(){return{current:null}},St.forwardRef=function(O){return{$$typeof:S,render:O}},St.isValidElement=jt,St.lazy=function(O){return{$$typeof:E,_payload:{_status:-1,_result:O},_init:rt}},St.memo=function(O,Q){return{$$typeof:y,type:O,compare:Q===void 0?null:Q}},St.startTransition=function(O){var Q=lt.T,nt={};lt.T=nt;try{var tt=O(),st=lt.S;st!==null&&st(nt,tt),typeof tt=="object"&&tt!==null&&typeof tt.then=="function"&&tt.then(Ut,$)}catch(ft){$(ft)}finally{lt.T=Q}},St.unstable_useCacheRefresh=function(){return lt.H.useCacheRefresh()},St.use=function(O){return lt.H.use(O)},St.useActionState=function(O,Q,nt){return lt.H.useActionState(O,Q,nt)},St.useCallback=function(O,Q){return lt.H.useCallback(O,Q)},St.useContext=function(O){return lt.H.useContext(O)},St.useDebugValue=function(){},St.useDeferredValue=function(O,Q){return lt.H.useDeferredValue(O,Q)},St.useEffect=function(O,Q,nt){var tt=lt.H;if(typeof nt=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return tt.useEffect(O,Q)},St.useId=function(){return lt.H.useId()},St.useImperativeHandle=function(O,Q,nt){return lt.H.useImperativeHandle(O,Q,nt)},St.useInsertionEffect=function(O,Q){return lt.H.useInsertionEffect(O,Q)},St.useLayoutEffect=function(O,Q){return lt.H.useLayoutEffect(O,Q)},St.useMemo=function(O,Q){return lt.H.useMemo(O,Q)},St.useOptimistic=function(O,Q){return lt.H.useOptimistic(O,Q)},St.useReducer=function(O,Q,nt){return lt.H.useReducer(O,Q,nt)},St.useRef=function(O){return lt.H.useRef(O)},St.useState=function(O){return lt.H.useState(O)},St.useSyncExternalStore=function(O,Q,nt){return lt.H.useSyncExternalStore(O,Q,nt)},St.useTransition=function(){return lt.H.useTransition()},St.version="19.1.0",St}var ep;function Wf(){return ep||(ep=1,Of.exports=Qg()),Of.exports}var M=Wf();const Uf=Cp(M);var Af={exports:{}},_s={},Rf={exports:{}},Mf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var np;function Kg(){return np||(np=1,function(o){function u(V,rt){var $=V.length;V.push(rt);t:for(;0<$;){var Ut=$-1>>>1,O=V[Ut];if(0<m(O,rt))V[Ut]=rt,V[$]=O,$=Ut;else break t}}function c(V){return V.length===0?null:V[0]}function h(V){if(V.length===0)return null;var rt=V[0],$=V.pop();if($!==rt){V[0]=$;t:for(var Ut=0,O=V.length,Q=O>>>1;Ut<Q;){var nt=2*(Ut+1)-1,tt=V[nt],st=nt+1,ft=V[st];if(0>m(tt,$))st<O&&0>m(ft,tt)?(V[Ut]=ft,V[st]=$,Ut=st):(V[Ut]=tt,V[nt]=$,Ut=nt);else if(st<O&&0>m(ft,$))V[Ut]=ft,V[st]=$,Ut=st;else break t}}return rt}function m(V,rt){var $=V.sortIndex-rt.sortIndex;return $!==0?$:V.id-rt.id}if(o.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var _=performance;o.unstable_now=function(){return _.now()}}else{var v=Date,S=v.now();o.unstable_now=function(){return v.now()-S}}var w=[],y=[],E=1,z=null,B=3,J=!1,N=!1,U=!1,Y=!1,X=typeof setTimeout=="function"?setTimeout:null,it=typeof clearTimeout=="function"?clearTimeout:null,et=typeof setImmediate<"u"?setImmediate:null;function xt(V){for(var rt=c(y);rt!==null;){if(rt.callback===null)h(y);else if(rt.startTime<=V)h(y),rt.sortIndex=rt.expirationTime,u(w,rt);else break;rt=c(y)}}function lt(V){if(U=!1,xt(V),!N)if(c(w)!==null)N=!0,Ct||(Ct=!0,Zt());else{var rt=c(y);rt!==null&&pe(lt,rt.startTime-V)}}var Ct=!1,Nt=-1,Pt=5,jt=-1;function me(){return Y?!0:!(o.unstable_now()-jt<Pt)}function Xt(){if(Y=!1,Ct){var V=o.unstable_now();jt=V;var rt=!0;try{t:{N=!1,U&&(U=!1,it(Nt),Nt=-1),J=!0;var $=B;try{e:{for(xt(V),z=c(w);z!==null&&!(z.expirationTime>V&&me());){var Ut=z.callback;if(typeof Ut=="function"){z.callback=null,B=z.priorityLevel;var O=Ut(z.expirationTime<=V);if(V=o.unstable_now(),typeof O=="function"){z.callback=O,xt(V),rt=!0;break e}z===c(w)&&h(w),xt(V)}else h(w);z=c(w)}if(z!==null)rt=!0;else{var Q=c(y);Q!==null&&pe(lt,Q.startTime-V),rt=!1}}break t}finally{z=null,B=$,J=!1}rt=void 0}}finally{rt?Zt():Ct=!1}}}var Zt;if(typeof et=="function")Zt=function(){et(Xt)};else if(typeof MessageChannel<"u"){var ti=new MessageChannel,Se=ti.port2;ti.port1.onmessage=Xt,Zt=function(){Se.postMessage(null)}}else Zt=function(){X(Xt,0)};function pe(V,rt){Nt=X(function(){V(o.unstable_now())},rt)}o.unstable_IdlePriority=5,o.unstable_ImmediatePriority=1,o.unstable_LowPriority=4,o.unstable_NormalPriority=3,o.unstable_Profiling=null,o.unstable_UserBlockingPriority=2,o.unstable_cancelCallback=function(V){V.callback=null},o.unstable_forceFrameRate=function(V){0>V||125<V?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Pt=0<V?Math.floor(1e3/V):5},o.unstable_getCurrentPriorityLevel=function(){return B},o.unstable_next=function(V){switch(B){case 1:case 2:case 3:var rt=3;break;default:rt=B}var $=B;B=rt;try{return V()}finally{B=$}},o.unstable_requestPaint=function(){Y=!0},o.unstable_runWithPriority=function(V,rt){switch(V){case 1:case 2:case 3:case 4:case 5:break;default:V=3}var $=B;B=V;try{return rt()}finally{B=$}},o.unstable_scheduleCallback=function(V,rt,$){var Ut=o.unstable_now();switch(typeof $=="object"&&$!==null?($=$.delay,$=typeof $=="number"&&0<$?Ut+$:Ut):$=Ut,V){case 1:var O=-1;break;case 2:O=250;break;case 5:O=1073741823;break;case 4:O=1e4;break;default:O=5e3}return O=$+O,V={id:E++,callback:rt,priorityLevel:V,startTime:$,expirationTime:O,sortIndex:-1},$>Ut?(V.sortIndex=$,u(y,V),c(w)===null&&V===c(y)&&(U?(it(Nt),Nt=-1):U=!0,pe(lt,$-Ut))):(V.sortIndex=O,u(w,V),N||J||(N=!0,Ct||(Ct=!0,Zt()))),V},o.unstable_shouldYield=me,o.unstable_wrapCallback=function(V){var rt=B;return function(){var $=B;B=rt;try{return V.apply(this,arguments)}finally{B=$}}}}(Mf)),Mf}var ip;function Jg(){return ip||(ip=1,Rf.exports=Kg()),Rf.exports}var Cf={exports:{}},Be={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ap;function Fg(){if(ap)return Be;ap=1;var o=Wf();function u(w){var y="https://react.dev/errors/"+w;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var E=2;E<arguments.length;E++)y+="&args[]="+encodeURIComponent(arguments[E])}return"Minified React error #"+w+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var h={d:{f:c,r:function(){throw Error(u(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},m=Symbol.for("react.portal");function _(w,y,E){var z=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:m,key:z==null?null:""+z,children:w,containerInfo:y,implementation:E}}var v=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function S(w,y){if(w==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return Be.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=h,Be.createPortal=function(w,y){var E=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(u(299));return _(w,y,null,E)},Be.flushSync=function(w){var y=v.T,E=h.p;try{if(v.T=null,h.p=2,w)return w()}finally{v.T=y,h.p=E,h.d.f()}},Be.preconnect=function(w,y){typeof w=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,h.d.C(w,y))},Be.prefetchDNS=function(w){typeof w=="string"&&h.d.D(w)},Be.preinit=function(w,y){if(typeof w=="string"&&y&&typeof y.as=="string"){var E=y.as,z=S(E,y.crossOrigin),B=typeof y.integrity=="string"?y.integrity:void 0,J=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;E==="style"?h.d.S(w,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:z,integrity:B,fetchPriority:J}):E==="script"&&h.d.X(w,{crossOrigin:z,integrity:B,fetchPriority:J,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},Be.preinitModule=function(w,y){if(typeof w=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var E=S(y.as,y.crossOrigin);h.d.M(w,{crossOrigin:E,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&h.d.M(w)},Be.preload=function(w,y){if(typeof w=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var E=y.as,z=S(E,y.crossOrigin);h.d.L(w,E,{crossOrigin:z,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},Be.preloadModule=function(w,y){if(typeof w=="string")if(y){var E=S(y.as,y.crossOrigin);h.d.m(w,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:E,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else h.d.m(w)},Be.requestFormReset=function(w){h.d.r(w)},Be.unstable_batchedUpdates=function(w,y){return w(y)},Be.useFormState=function(w,y,E){return v.H.useFormState(w,y,E)},Be.useFormStatus=function(){return v.H.useHostTransitionStatus()},Be.version="19.1.0",Be}var rp;function Np(){if(rp)return Cf.exports;rp=1;function o(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(o)}catch(u){console.error(u)}}return o(),Cf.exports=Fg(),Cf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var op;function Wg(){if(op)return _s;op=1;var o=Jg(),u=Wf(),c=Np();function h(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)e+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function m(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function _(t){var e=t,i=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(i=e.return),t=e.return;while(t)}return e.tag===3?i:null}function v(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function S(t){if(_(t)!==t)throw Error(h(188))}function w(t){var e=t.alternate;if(!e){if(e=_(t),e===null)throw Error(h(188));return e!==t?null:t}for(var i=t,r=e;;){var l=i.return;if(l===null)break;var f=l.alternate;if(f===null){if(r=l.return,r!==null){i=r;continue}break}if(l.child===f.child){for(f=l.child;f;){if(f===i)return S(l),t;if(f===r)return S(l),e;f=f.sibling}throw Error(h(188))}if(i.return!==r.return)i=l,r=f;else{for(var g=!1,x=l.child;x;){if(x===i){g=!0,i=l,r=f;break}if(x===r){g=!0,r=l,i=f;break}x=x.sibling}if(!g){for(x=f.child;x;){if(x===i){g=!0,i=f,r=l;break}if(x===r){g=!0,r=f,i=l;break}x=x.sibling}if(!g)throw Error(h(189))}}if(i.alternate!==r)throw Error(h(190))}if(i.tag!==3)throw Error(h(188));return i.stateNode.current===i?t:e}function y(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=y(t),e!==null)return e;t=t.sibling}return null}var E=Object.assign,z=Symbol.for("react.element"),B=Symbol.for("react.transitional.element"),J=Symbol.for("react.portal"),N=Symbol.for("react.fragment"),U=Symbol.for("react.strict_mode"),Y=Symbol.for("react.profiler"),X=Symbol.for("react.provider"),it=Symbol.for("react.consumer"),et=Symbol.for("react.context"),xt=Symbol.for("react.forward_ref"),lt=Symbol.for("react.suspense"),Ct=Symbol.for("react.suspense_list"),Nt=Symbol.for("react.memo"),Pt=Symbol.for("react.lazy"),jt=Symbol.for("react.activity"),me=Symbol.for("react.memo_cache_sentinel"),Xt=Symbol.iterator;function Zt(t){return t===null||typeof t!="object"?null:(t=Xt&&t[Xt]||t["@@iterator"],typeof t=="function"?t:null)}var ti=Symbol.for("react.client.reference");function Se(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ti?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case N:return"Fragment";case Y:return"Profiler";case U:return"StrictMode";case lt:return"Suspense";case Ct:return"SuspenseList";case jt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case J:return"Portal";case et:return(t.displayName||"Context")+".Provider";case it:return(t._context.displayName||"Context")+".Consumer";case xt:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Nt:return e=t.displayName||null,e!==null?e:Se(t.type)||"Memo";case Pt:e=t._payload,t=t._init;try{return Se(t(e))}catch{}}return null}var pe=Array.isArray,V=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,rt=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$={pending:!1,data:null,method:null,action:null},Ut=[],O=-1;function Q(t){return{current:t}}function nt(t){0>O||(t.current=Ut[O],Ut[O]=null,O--)}function tt(t,e){O++,Ut[O]=t.current,t.current=e}var st=Q(null),ft=Q(null),at=Q(null),te=Q(null);function zt(t,e){switch(tt(at,e),tt(ft,t),tt(st,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Lm(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Lm(e),t=Om(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}nt(st),tt(st,t)}function En(){nt(st),nt(ft),nt(at)}function za(t){t.memoizedState!==null&&tt(te,t);var e=st.current,i=Om(e,t.type);e!==i&&(tt(ft,t),tt(st,i))}function ji(t){ft.current===t&&(nt(st),nt(ft)),te.current===t&&(nt(te),cs._currentValue=$)}var ei=Object.prototype.hasOwnProperty,Da=o.unstable_scheduleCallback,co=o.unstable_cancelCallback,Cs=o.unstable_shouldYield,Ns=o.unstable_requestPaint,Ve=o.unstable_now,_r=o.unstable_getCurrentPriorityLevel,zs=o.unstable_ImmediatePriority,fo=o.unstable_UserBlockingPriority,Ui=o.unstable_NormalPriority,Ds=o.unstable_LowPriority,ho=o.unstable_IdlePriority,Mu=o.log,Cu=o.unstable_setDisableYieldValue,ni=null,Me=null;function Tn(t){if(typeof Mu=="function"&&Cu(t),Me&&typeof Me.setStrictMode=="function")try{Me.setStrictMode(ni,t)}catch{}}var Ue=Math.clz32?Math.clz32:Nu,Bs=Math.log,js=Math.LN2;function Nu(t){return t>>>=0,t===0?32:31-(Bs(t)/js|0)|0}var Ba=256,Pi=4194304;function jn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function gr(t,e,i){var r=t.pendingLanes;if(r===0)return 0;var l=0,f=t.suspendedLanes,g=t.pingedLanes;t=t.warmLanes;var x=r&134217727;return x!==0?(r=x&~f,r!==0?l=jn(r):(g&=x,g!==0?l=jn(g):i||(i=x&~t,i!==0&&(l=jn(i))))):(x=r&~f,x!==0?l=jn(x):g!==0?l=jn(g):i||(i=r&~t,i!==0&&(l=jn(i)))),l===0?0:e!==0&&e!==l&&(e&f)===0&&(f=l&-l,i=e&-e,f>=i||f===32&&(i&4194048)!==0)?e:l}function Ln(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function zu(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Us(){var t=Ba;return Ba<<=1,(Ba&4194048)===0&&(Ba=256),t}function mo(){var t=Pi;return Pi<<=1,(Pi&62914560)===0&&(Pi=4194304),t}function vr(t){for(var e=[],i=0;31>i;i++)e.push(t);return e}function Zi(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Du(t,e,i,r,l,f){var g=t.pendingLanes;t.pendingLanes=i,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=i,t.entangledLanes&=i,t.errorRecoveryDisabledLanes&=i,t.shellSuspendCounter=0;var x=t.entanglements,R=t.expirationTimes,Z=t.hiddenUpdates;for(i=g&~i;0<i;){var K=31-Ue(i),W=1<<K;x[K]=0,R[K]=-1;var H=Z[K];if(H!==null)for(Z[K]=null,K=0;K<H.length;K++){var k=H[K];k!==null&&(k.lane&=-536870913)}i&=~W}r!==0&&Ps(t,r,0),f!==0&&l===0&&t.tag!==0&&(t.suspendedLanes|=f&~(g&~e))}function Ps(t,e,i){t.pendingLanes|=e,t.suspendedLanes&=~e;var r=31-Ue(e);t.entangledLanes|=e,t.entanglements[r]=t.entanglements[r]|1073741824|i&4194090}function Zs(t,e){var i=t.entangledLanes|=e;for(t=t.entanglements;i;){var r=31-Ue(i),l=1<<r;l&e|t[r]&e&&(t[r]|=e),i&=~l}}function po(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function _o(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Hs(){var t=rt.p;return t!==0?t:(t=window.event,t===void 0?32:Xm(t.type))}function go(t,e){var i=rt.p;try{return rt.p=t,e()}finally{rt.p=i}}var Un=Math.random().toString(36).slice(2),_e="__reactFiber$"+Un,Ce="__reactProps$"+Un,Hi="__reactContainer$"+Un,Xe="__reactEvents$"+Un,dt="__reactListeners$"+Un,ks="__reactHandles$"+Un,vo="__reactResources$"+Un,ki="__reactMarker$"+Un;function yr(t){delete t[_e],delete t[Ce],delete t[Xe],delete t[dt],delete t[ks]}function Pn(t){var e=t[_e];if(e)return e;for(var i=t.parentNode;i;){if(e=i[Hi]||i[_e]){if(i=e.alternate,e.child!==null||i!==null&&i.child!==null)for(t=Cm(t);t!==null;){if(i=t[_e])return i;t=Cm(t)}return e}t=i,i=t.parentNode}return null}function ii(t){if(t=t[_e]||t[Hi]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function dn(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(h(33))}function ai(t){var e=t[vo];return e||(e=t[vo]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function ue(t){t[ki]=!0}var qs=new Set,Ys={};function ri(t,e){oi(t,e),oi(t+"Capture",e)}function oi(t,e){for(Ys[t]=e,t=0;t<e.length;t++)qs.add(e[t])}var Bu=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ja={},Gs={};function ju(t){return ei.call(Gs,t)?!0:ei.call(ja,t)?!1:Bu.test(t)?Gs[t]=!0:(ja[t]=!0,!1)}function br(t,e,i){if(ju(e))if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var r=e.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+i)}}function xr(t,e,i){if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+i)}}function On(t,e,i,r){if(r===null)t.removeAttribute(i);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(i);return}t.setAttributeNS(e,i,""+r)}}var Ua,qi;function si(t){if(Ua===void 0)try{throw Error()}catch(i){var e=i.stack.trim().match(/\n( *(at )?)/);Ua=e&&e[1]||"",qi=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Ua+t+qi}var wr=!1;function li(t,e){if(!t||wr)return"";wr=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(e){var W=function(){throw Error()};if(Object.defineProperty(W.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(W,[])}catch(k){var H=k}Reflect.construct(t,[],W)}else{try{W.call()}catch(k){H=k}t.call(W.prototype)}}else{try{throw Error()}catch(k){H=k}(W=t())&&typeof W.catch=="function"&&W.catch(function(){})}}catch(k){if(k&&H&&typeof k.stack=="string")return[k.stack,H.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var l=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");l&&l.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var f=r.DetermineComponentFrameRoot(),g=f[0],x=f[1];if(g&&x){var R=g.split(`
`),Z=x.split(`
`);for(l=r=0;r<R.length&&!R[r].includes("DetermineComponentFrameRoot");)r++;for(;l<Z.length&&!Z[l].includes("DetermineComponentFrameRoot");)l++;if(r===R.length||l===Z.length)for(r=R.length-1,l=Z.length-1;1<=r&&0<=l&&R[r]!==Z[l];)l--;for(;1<=r&&0<=l;r--,l--)if(R[r]!==Z[l]){if(r!==1||l!==1)do if(r--,l--,0>l||R[r]!==Z[l]){var K=`
`+R[r].replace(" at new "," at ");return t.displayName&&K.includes("<anonymous>")&&(K=K.replace("<anonymous>",t.displayName)),K}while(1<=r&&0<=l);break}}}finally{wr=!1,Error.prepareStackTrace=i}return(i=t?t.displayName||t.name:"")?si(i):""}function Rt(t){switch(t.tag){case 26:case 27:case 5:return si(t.type);case 16:return si("Lazy");case 13:return si("Suspense");case 19:return si("SuspenseList");case 0:case 15:return li(t.type,!1);case 11:return li(t.type.render,!1);case 1:return li(t.type,!0);case 31:return si("Activity");default:return""}}function Kt(t){try{var e="";do e+=Rt(t),t=t.return;while(t);return e}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function Ee(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function ui(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Yi(t){var e=ui(t)?"checked":"value",i=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var l=i.get,f=i.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return l.call(this)},set:function(g){r=""+g,f.call(this,g)}}),Object.defineProperty(t,e,{enumerable:i.enumerable}),{getValue:function(){return r},setValue:function(g){r=""+g},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Gi(t){t._valueTracker||(t._valueTracker=Yi(t))}function bt(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var i=e.getValue(),r="";return t&&(r=ui(t)?t.checked?"true":"false":t.value),t=r,t!==i?(e.setValue(t),!0):!1}function Jt(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var yo=/[\n"\\]/g;function Te(t){return t.replace(yo,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ne(t,e,i,r,l,f,g,x){t.name="",g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?t.type=g:t.removeAttribute("type"),e!=null?g==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Ee(e)):t.value!==""+Ee(e)&&(t.value=""+Ee(e)):g!=="submit"&&g!=="reset"||t.removeAttribute("value"),e!=null?Vi(t,g,Ee(e)):i!=null?Vi(t,g,Ee(i)):r!=null&&t.removeAttribute("value"),l==null&&f!=null&&(t.defaultChecked=!!f),l!=null&&(t.checked=l&&typeof l!="function"&&typeof l!="symbol"),x!=null&&typeof x!="function"&&typeof x!="symbol"&&typeof x!="boolean"?t.name=""+Ee(x):t.removeAttribute("name")}function Vs(t,e,i,r,l,f,g,x){if(f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.type=f),e!=null||i!=null){if(!(f!=="submit"&&f!=="reset"||e!=null))return;i=i!=null?""+Ee(i):"",e=e!=null?""+Ee(e):i,x||e===t.value||(t.value=e),t.defaultValue=e}r=r??l,r=typeof r!="function"&&typeof r!="symbol"&&!!r,t.checked=x?t.checked:!!r,t.defaultChecked=!!r,g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(t.name=g)}function Vi(t,e,i){e==="number"&&Jt(t.ownerDocument)===t||t.defaultValue===""+i||(t.defaultValue=""+i)}function Qe(t,e,i,r){if(t=t.options,e){e={};for(var l=0;l<i.length;l++)e["$"+i[l]]=!0;for(i=0;i<t.length;i++)l=e.hasOwnProperty("$"+t[i].value),t[i].selected!==l&&(t[i].selected=l),l&&r&&(t[i].defaultSelected=!0)}else{for(i=""+Ee(i),e=null,l=0;l<t.length;l++){if(t[l].value===i){t[l].selected=!0,r&&(t[l].defaultSelected=!0);return}e!==null||t[l].disabled||(e=t[l])}e!==null&&(e.selected=!0)}}function ee(t,e,i){if(e!=null&&(e=""+Ee(e),e!==t.value&&(t.value=e),i==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=i!=null?""+Ee(i):""}function Zn(t,e,i,r){if(e==null){if(r!=null){if(i!=null)throw Error(h(92));if(pe(r)){if(1<r.length)throw Error(h(93));r=r[0]}i=r}i==null&&(i=""),e=i}i=Ee(e),t.defaultValue=i,r=t.textContent,r===i&&r!==""&&r!==null&&(t.value=r)}function mn(t,e){if(e){var i=t.firstChild;if(i&&i===t.lastChild&&i.nodeType===3){i.nodeValue=e;return}}t.textContent=e}var Pa=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Sr(t,e,i){var r=e.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?r?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":r?t.setProperty(e,i):typeof i!="number"||i===0||Pa.has(e)?e==="float"?t.cssFloat=i:t[e]=(""+i).trim():t[e]=i+"px"}function Xi(t,e,i){if(e!=null&&typeof e!="object")throw Error(h(62));if(t=t.style,i!=null){for(var r in i)!i.hasOwnProperty(r)||e!=null&&e.hasOwnProperty(r)||(r.indexOf("--")===0?t.setProperty(r,""):r==="float"?t.cssFloat="":t[r]="");for(var l in e)r=e[l],e.hasOwnProperty(l)&&i[l]!==r&&Sr(t,l,r)}else for(var f in e)e.hasOwnProperty(f)&&Sr(t,f,e[f])}function Za(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var bo=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Er=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Qi(t){return Er.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ha=null;function Ki(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ci=null,Hn=null;function Xs(t){var e=ii(t);if(e&&(t=e.stateNode)){var i=t[Ce]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ne(t,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),e=i.name,i.type==="radio"&&e!=null){for(i=t;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+Te(""+e)+'"][type="radio"]'),e=0;e<i.length;e++){var r=i[e];if(r!==t&&r.form===t.form){var l=r[Ce]||null;if(!l)throw Error(h(90));Ne(r,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name)}}for(e=0;e<i.length;e++)r=i[e],r.form===t.form&&bt(r)}break t;case"textarea":ee(t,i.value,i.defaultValue);break t;case"select":e=i.value,e!=null&&Qe(t,!!i.multiple,e,!1)}}}var yt=!1;function tn(t,e,i){if(yt)return t(e,i);yt=!0;try{var r=t(e);return r}finally{if(yt=!1,(ci!==null||Hn!==null)&&(Hl(),ci&&(e=ci,t=Hn,Hn=ci=null,Xs(e),t)))for(e=0;e<t.length;e++)Xs(t[e])}}function Dt(t,e){var i=t.stateNode;if(i===null)return null;var r=i[Ce]||null;if(r===null)return null;i=r[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break t;default:t=!1}if(t)return null;if(i&&typeof i!="function")throw Error(h(231,e,typeof i));return i}var pn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ka=!1;if(pn)try{var fi={};Object.defineProperty(fi,"passive",{get:function(){ka=!0}}),window.addEventListener("test",fi,fi),window.removeEventListener("test",fi,fi)}catch{ka=!1}var _n=null,An=null,Ji=null;function Fi(){if(Ji)return Ji;var t,e=An,i=e.length,r,l="value"in _n?_n.value:_n.textContent,f=l.length;for(t=0;t<i&&e[t]===l[t];t++);var g=i-t;for(r=1;r<=g&&e[i-r]===l[f-r];r++);return Ji=l.slice(t,1<r?1-r:void 0)}function re(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function gn(){return!0}function xo(){return!1}function Le(t){function e(i,r,l,f,g){this._reactName=i,this._targetInst=l,this.type=r,this.nativeEvent=f,this.target=g,this.currentTarget=null;for(var x in t)t.hasOwnProperty(x)&&(i=t[x],this[x]=i?i(f):f[x]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?gn:xo,this.isPropagationStopped=xo,this}return E(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=gn)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=gn)},persist:function(){},isPersistent:gn}),e}var hi={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},qa=Le(hi),di=E({},hi,{view:0,detail:0}),Uu=Le(di),Tr,Tt,Ya,ze=E({},di,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Lr,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ya&&(Ya&&t.type==="mousemove"?(Tr=t.screenX-Ya.screenX,Tt=t.screenY-Ya.screenY):Tt=Tr=0,Ya=t),Tr)},movementY:function(t){return"movementY"in t?t.movementY:Tt}}),Wi=Le(ze),Qs=E({},ze,{dataTransfer:0}),Pu=Le(Qs),wo=E({},di,{relatedTarget:0}),So=Le(wo),Ks=E({},hi,{animationName:0,elapsedTime:0,pseudoElement:0}),Zu=Le(Ks),Hu=E({},hi,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Eo=Le(Hu),ku=E({},hi,{data:0}),en=Le(ku),qu={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Js={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Fs(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=kn[t])?!!e[t]:!1}function Lr(){return Fs}var To=E({},di,{key:function(t){if(t.key){var e=qu[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=re(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Js[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Lr,charCode:function(t){return t.type==="keypress"?re(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?re(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Yu=Le(To),Ws=E({},ze,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Lo=Le(Ws),Gu=E({},di,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Lr}),Vu=Le(Gu),Oo=E({},hi,{propertyName:0,elapsedTime:0,pseudoElement:0}),Xu=Le(Oo),Is=E({},ze,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),$s=Le(Is),Or=E({},hi,{newState:0,oldState:0}),mi=Le(Or),Qu=[9,13,27,32],pi=pn&&"CompositionEvent"in window,ge=null;pn&&"documentMode"in document&&(ge=document.documentMode);var tl=pn&&"TextEvent"in window&&!ge,Ao=pn&&(!pi||ge&&8<ge&&11>=ge),el=" ",Ar=!1;function Rr(t,e){switch(t){case"keyup":return Qu.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function nl(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ii=!1;function il(t,e){switch(t){case"compositionend":return nl(e);case"keypress":return e.which!==32?null:(Ar=!0,el);case"textInput":return t=e.data,t===el&&Ar?null:t;default:return null}}function Ku(t,e){if(Ii)return t==="compositionend"||!pi&&Rr(t,e)?(t=Fi(),Ji=An=_n=null,Ii=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Ao&&e.locale!=="ko"?null:e.data;default:return null}}var nn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _i(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!nn[t.type]:e==="textarea"}function al(t,e,i,r){ci?Hn?Hn.push(r):Hn=[r]:ci=r,e=Xl(e,"onChange"),0<e.length&&(i=new qa("onChange","change",null,i,r),t.push({event:i,listeners:e}))}var Pe=null,Ga=null;function $i(t){xm(t,0)}function Mr(t){var e=dn(t);if(bt(e))return t}function ta(t,e){if(t==="change")return e}var Ro=!1;if(pn){var ea;if(pn){var Mo="oninput"in document;if(!Mo){var Rn=document.createElement("div");Rn.setAttribute("oninput","return;"),Mo=typeof Rn.oninput=="function"}ea=Mo}else ea=!1;Ro=ea&&(!document.documentMode||9<document.documentMode)}function Va(){Pe&&(Pe.detachEvent("onpropertychange",rl),Ga=Pe=null)}function rl(t){if(t.propertyName==="value"&&Mr(Ga)){var e=[];al(e,Ga,t,Ki(t)),tn($i,e)}}function Co(t,e,i){t==="focusin"?(Va(),Pe=e,Ga=i,Pe.attachEvent("onpropertychange",rl)):t==="focusout"&&Va()}function Ju(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Mr(Ga)}function Mn(t,e){if(t==="click")return Mr(e)}function Fu(t,e){if(t==="input"||t==="change")return Mr(e)}function na(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ze=typeof Object.is=="function"?Object.is:na;function He(t,e){if(Ze(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var i=Object.keys(t),r=Object.keys(e);if(i.length!==r.length)return!1;for(r=0;r<i.length;r++){var l=i[r];if(!ei.call(e,l)||!Ze(t[l],e[l]))return!1}return!0}function Xa(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function No(t,e){var i=Xa(t);t=0;for(var r;i;){if(i.nodeType===3){if(r=t+i.textContent.length,t<=e&&r>=e)return{node:i,offset:e-t};t=r}t:{for(;i;){if(i.nextSibling){i=i.nextSibling;break t}i=i.parentNode}i=void 0}i=Xa(i)}}function Cr(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Cr(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Qa(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Jt(t.document);e instanceof t.HTMLIFrameElement;){try{var i=typeof e.contentWindow.location.href=="string"}catch{i=!1}if(i)t=e.contentWindow;else break;e=Jt(t.document)}return e}function Ka(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Nr=pn&&"documentMode"in document&&11>=document.documentMode,an=null,ia=null,gi=null,zr=!1;function ol(t,e,i){var r=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;zr||an==null||an!==Jt(r)||(r=an,"selectionStart"in r&&Ka(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),gi&&He(gi,r)||(gi=r,r=Xl(ia,"onSelect"),0<r.length&&(e=new qa("onSelect","select",null,e,i),t.push({event:e,listeners:r}),e.target=an)))}function vn(t,e){var i={};return i[t.toLowerCase()]=e.toLowerCase(),i["Webkit"+t]="webkit"+e,i["Moz"+t]="moz"+e,i}var aa={animationend:vn("Animation","AnimationEnd"),animationiteration:vn("Animation","AnimationIteration"),animationstart:vn("Animation","AnimationStart"),transitionrun:vn("Transition","TransitionRun"),transitionstart:vn("Transition","TransitionStart"),transitioncancel:vn("Transition","TransitionCancel"),transitionend:vn("Transition","TransitionEnd")},Dr={},sl={};pn&&(sl=document.createElement("div").style,"AnimationEvent"in window||(delete aa.animationend.animation,delete aa.animationiteration.animation,delete aa.animationstart.animation),"TransitionEvent"in window||delete aa.transitionend.transition);function qn(t){if(Dr[t])return Dr[t];if(!aa[t])return t;var e=aa[t],i;for(i in e)if(e.hasOwnProperty(i)&&i in sl)return Dr[t]=e[i];return t}var ll=qn("animationend"),rn=qn("animationiteration"),Ja=qn("animationstart"),Wu=qn("transitionrun"),Br=qn("transitionstart"),Iu=qn("transitioncancel"),zo=qn("transitionend"),ul=new Map,vi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");vi.push("scrollEnd");function on(t,e){ul.set(t,e),ri(e,[t])}var yi=new WeakMap;function ke(t,e){if(typeof t=="object"&&t!==null){var i=yi.get(t);return i!==void 0?i:(e={value:t,source:e,stack:Kt(e)},yi.set(t,e),e)}return{value:t,source:e,stack:Kt(e)}}var qe=[],ra=0,sn=0;function Fa(){for(var t=ra,e=sn=ra=0;e<t;){var i=qe[e];qe[e++]=null;var r=qe[e];qe[e++]=null;var l=qe[e];qe[e++]=null;var f=qe[e];if(qe[e++]=null,r!==null&&l!==null){var g=r.pending;g===null?l.next=l:(l.next=g.next,g.next=l),r.pending=l}f!==0&&Ia(i,l,f)}}function Wa(t,e,i,r){qe[ra++]=t,qe[ra++]=e,qe[ra++]=i,qe[ra++]=r,sn|=r,t.lanes|=r,t=t.alternate,t!==null&&(t.lanes|=r)}function bi(t,e,i,r){return Wa(t,e,i,r),Yn(t)}function oa(t,e){return Wa(t,null,null,e),Yn(t)}function Ia(t,e,i){t.lanes|=i;var r=t.alternate;r!==null&&(r.lanes|=i);for(var l=!1,f=t.return;f!==null;)f.childLanes|=i,r=f.alternate,r!==null&&(r.childLanes|=i),f.tag===22&&(t=f.stateNode,t===null||t._visibility&1||(l=!0)),t=f,f=f.return;return t.tag===3?(f=t.stateNode,l&&e!==null&&(l=31-Ue(i),t=f.hiddenUpdates,r=t[l],r===null?t[l]=[e]:r.push(e),e.lane=i|536870912),f):null}function Yn(t){if(50<ns)throw ns=0,Jc=null,Error(h(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var xi={};function cl(t,e,i,r){this.tag=t,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ye(t,e,i,r){return new cl(t,e,i,r)}function jr(t){return t=t.prototype,!(!t||!t.isReactComponent)}function yn(t,e){var i=t.alternate;return i===null?(i=Ye(t.tag,e,t.key,t.mode),i.elementType=t.elementType,i.type=t.type,i.stateNode=t.stateNode,i.alternate=t,t.alternate=i):(i.pendingProps=e,i.type=t.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=t.flags&65011712,i.childLanes=t.childLanes,i.lanes=t.lanes,i.child=t.child,i.memoizedProps=t.memoizedProps,i.memoizedState=t.memoizedState,i.updateQueue=t.updateQueue,e=t.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},i.sibling=t.sibling,i.index=t.index,i.ref=t.ref,i.refCleanup=t.refCleanup,i}function Do(t,e){t.flags&=65011714;var i=t.alternate;return i===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=i.childLanes,t.lanes=i.lanes,t.child=i.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=i.memoizedProps,t.memoizedState=i.memoizedState,t.updateQueue=i.updateQueue,t.type=i.type,e=i.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function $a(t,e,i,r,l,f){var g=0;if(r=t,typeof t=="function")jr(t)&&(g=1);else if(typeof t=="string")g=Ng(t,i,st.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case jt:return t=Ye(31,i,e,l),t.elementType=jt,t.lanes=f,t;case N:return Gn(i.children,l,f,e);case U:g=8,l|=24;break;case Y:return t=Ye(12,i,e,l|2),t.elementType=Y,t.lanes=f,t;case lt:return t=Ye(13,i,e,l),t.elementType=lt,t.lanes=f,t;case Ct:return t=Ye(19,i,e,l),t.elementType=Ct,t.lanes=f,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case X:case et:g=10;break t;case it:g=9;break t;case xt:g=11;break t;case Nt:g=14;break t;case Pt:g=16,r=null;break t}g=29,i=Error(h(130,t===null?"null":typeof t,"")),r=null}return e=Ye(g,i,e,l),e.elementType=t,e.type=r,e.lanes=f,e}function Gn(t,e,i,r){return t=Ye(7,t,r,e),t.lanes=i,t}function Bo(t,e,i){return t=Ye(6,t,null,e),t.lanes=i,t}function Ur(t,e,i){return e=Ye(4,t.children!==null?t.children:[],t.key,e),e.lanes=i,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var wi=[],sa=0,n=null,a=0,s=[],d=0,p=null,b=1,A="";function j(t,e){wi[sa++]=a,wi[sa++]=n,n=t,a=e}function q(t,e,i){s[d++]=b,s[d++]=A,s[d++]=p,p=t;var r=b;t=A;var l=32-Ue(r)-1;r&=~(1<<l),i+=1;var f=32-Ue(e)+l;if(30<f){var g=l-l%5;f=(r&(1<<g)-1).toString(32),r>>=g,l-=g,b=1<<32-Ue(e)+l|i<<l|r,A=f+t}else b=1<<f|i<<l|r,A=t}function I(t){t.return!==null&&(j(t,1),q(t,1,0))}function ot(t){for(;t===n;)n=wi[--sa],wi[sa]=null,a=wi[--sa],wi[sa]=null;for(;t===p;)p=s[--d],s[d]=null,A=s[--d],s[d]=null,b=s[--d],s[d]=null}var ut=null,ht=null,vt=!1,Ft=null,ne=!1,ve=Error(h(519));function Ke(t){var e=Error(h(418,""));throw ua(ke(e,t)),ve}function fl(t){var e=t.stateNode,i=t.type,r=t.memoizedProps;switch(e[_e]=t,e[Ce]=r,i){case"dialog":At("cancel",e),At("close",e);break;case"iframe":case"object":case"embed":At("load",e);break;case"video":case"audio":for(i=0;i<as.length;i++)At(as[i],e);break;case"source":At("error",e);break;case"img":case"image":case"link":At("error",e),At("load",e);break;case"details":At("toggle",e);break;case"input":At("invalid",e),Vs(e,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),Gi(e);break;case"select":At("invalid",e);break;case"textarea":At("invalid",e),Zn(e,r.value,r.defaultValue,r.children),Gi(e)}i=r.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||e.textContent===""+i||r.suppressHydrationWarning===!0||Tm(e.textContent,i)?(r.popover!=null&&(At("beforetoggle",e),At("toggle",e)),r.onScroll!=null&&At("scroll",e),r.onScrollEnd!=null&&At("scrollend",e),r.onClick!=null&&(e.onclick=Ql),e=!0):e=!1,e||Ke(t)}function hl(t){for(ut=t.return;ut;)switch(ut.tag){case 5:case 13:ne=!1;return;case 27:case 3:ne=!0;return;default:ut=ut.return}}function tr(t){if(t!==ut)return!1;if(!vt)return hl(t),vt=!0,!1;var e=t.tag,i;if((i=e!==3&&e!==27)&&((i=e===5)&&(i=t.type,i=!(i!=="form"&&i!=="button")||hf(t.type,t.memoizedProps)),i=!i),i&&ht&&Ke(t),hl(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(h(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(i=t.data,i==="/$"){if(e===0){ht=Dn(t.nextSibling);break t}e--}else i!=="$"&&i!=="$!"&&i!=="$?"||e++;t=t.nextSibling}ht=null}}else e===27?(e=ht,Ea(t.type)?(t=_f,_f=null,ht=t):ht=e):ht=ut?Dn(t.stateNode.nextSibling):null;return!0}function la(){ht=ut=null,vt=!1}function dl(){var t=Ft;return t!==null&&(We===null?We=t:We.push.apply(We,t),Ft=null),t}function ua(t){Ft===null?Ft=[t]:Ft.push(t)}var It=Q(null),bn=null,Cn=null;function Vn(t,e,i){tt(It,e._currentValue),e._currentValue=i}function Nn(t){t._currentValue=It.current,nt(It)}function er(t,e,i){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===i)break;t=t.return}}function Pr(t,e,i,r){var l=t.child;for(l!==null&&(l.return=t);l!==null;){var f=l.dependencies;if(f!==null){var g=l.child;f=f.firstContext;t:for(;f!==null;){var x=f;f=l;for(var R=0;R<e.length;R++)if(x.context===e[R]){f.lanes|=i,x=f.alternate,x!==null&&(x.lanes|=i),er(f.return,i,t),r||(g=null);break t}f=x.next}}else if(l.tag===18){if(g=l.return,g===null)throw Error(h(341));g.lanes|=i,f=g.alternate,f!==null&&(f.lanes|=i),er(g,i,t),g=null}else g=l.child;if(g!==null)g.return=l;else for(g=l;g!==null;){if(g===t){g=null;break}if(l=g.sibling,l!==null){l.return=g.return,g=l;break}g=g.return}l=g}}function nr(t,e,i,r){t=null;for(var l=e,f=!1;l!==null;){if(!f){if((l.flags&524288)!==0)f=!0;else if((l.flags&262144)!==0)break}if(l.tag===10){var g=l.alternate;if(g===null)throw Error(h(387));if(g=g.memoizedProps,g!==null){var x=l.type;Ze(l.pendingProps.value,g.value)||(t!==null?t.push(x):t=[x])}}else if(l===te.current){if(g=l.alternate,g===null)throw Error(h(387));g.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(t!==null?t.push(cs):t=[cs])}l=l.return}t!==null&&Pr(e,t,i,r),e.flags|=262144}function ml(t){for(t=t.firstContext;t!==null;){if(!Ze(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ir(t){bn=t,Cn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function De(t){return fh(bn,t)}function pl(t,e){return bn===null&&ir(t),fh(t,e)}function fh(t,e){var i=e._currentValue;if(e={context:e,memoizedValue:i,next:null},Cn===null){if(t===null)throw Error(h(308));Cn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Cn=Cn.next=e;return i}var C_=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(i,r){t.push(r)}};this.abort=function(){e.aborted=!0,t.forEach(function(i){return i()})}},N_=o.unstable_scheduleCallback,z_=o.unstable_NormalPriority,he={$$typeof:et,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function $u(){return{controller:new C_,data:new Map,refCount:0}}function jo(t){t.refCount--,t.refCount===0&&N_(z_,function(){t.controller.abort()})}var Uo=null,tc=0,Zr=0,Hr=null;function D_(t,e){if(Uo===null){var i=Uo=[];tc=0,Zr=nf(),Hr={status:"pending",value:void 0,then:function(r){i.push(r)}}}return tc++,e.then(hh,hh),e}function hh(){if(--tc===0&&Uo!==null){Hr!==null&&(Hr.status="fulfilled");var t=Uo;Uo=null,Zr=0,Hr=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function B_(t,e){var i=[],r={status:"pending",value:null,reason:null,then:function(l){i.push(l)}};return t.then(function(){r.status="fulfilled",r.value=e;for(var l=0;l<i.length;l++)(0,i[l])(e)},function(l){for(r.status="rejected",r.reason=l,l=0;l<i.length;l++)(0,i[l])(void 0)}),r}var dh=V.S;V.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&D_(t,e),dh!==null&&dh(t,e)};var ar=Q(null);function ec(){var t=ar.current;return t!==null?t:Wt.pooledCache}function _l(t,e){e===null?tt(ar,ar.current):tt(ar,e.pool)}function mh(){var t=ec();return t===null?null:{parent:he._currentValue,pool:t}}var Po=Error(h(460)),ph=Error(h(474)),gl=Error(h(542)),nc={then:function(){}};function _h(t){return t=t.status,t==="fulfilled"||t==="rejected"}function vl(){}function gh(t,e,i){switch(i=t[i],i===void 0?t.push(e):i!==e&&(e.then(vl,vl),e=i),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,yh(t),t;default:if(typeof e.status=="string")e.then(vl,vl);else{if(t=Wt,t!==null&&100<t.shellSuspendCounter)throw Error(h(482));t=e,t.status="pending",t.then(function(r){if(e.status==="pending"){var l=e;l.status="fulfilled",l.value=r}},function(r){if(e.status==="pending"){var l=e;l.status="rejected",l.reason=r}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,yh(t),t}throw Zo=e,Po}}var Zo=null;function vh(){if(Zo===null)throw Error(h(459));var t=Zo;return Zo=null,t}function yh(t){if(t===Po||t===gl)throw Error(h(483))}var ca=!1;function ic(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ac(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function fa(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function ha(t,e,i){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,(Ht&2)!==0){var l=r.pending;return l===null?e.next=e:(e.next=l.next,l.next=e),r.pending=e,e=Yn(t),Ia(t,null,i),e}return Wa(t,r,e,i),Yn(t)}function Ho(t,e,i){if(e=e.updateQueue,e!==null&&(e=e.shared,(i&4194048)!==0)){var r=e.lanes;r&=t.pendingLanes,i|=r,e.lanes=i,Zs(t,i)}}function rc(t,e){var i=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,i===r)){var l=null,f=null;if(i=i.firstBaseUpdate,i!==null){do{var g={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};f===null?l=f=g:f=f.next=g,i=i.next}while(i!==null);f===null?l=f=e:f=f.next=e}else l=f=e;i={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:f,shared:r.shared,callbacks:r.callbacks},t.updateQueue=i;return}t=i.lastBaseUpdate,t===null?i.firstBaseUpdate=e:t.next=e,i.lastBaseUpdate=e}var oc=!1;function ko(){if(oc){var t=Hr;if(t!==null)throw t}}function qo(t,e,i,r){oc=!1;var l=t.updateQueue;ca=!1;var f=l.firstBaseUpdate,g=l.lastBaseUpdate,x=l.shared.pending;if(x!==null){l.shared.pending=null;var R=x,Z=R.next;R.next=null,g===null?f=Z:g.next=Z,g=R;var K=t.alternate;K!==null&&(K=K.updateQueue,x=K.lastBaseUpdate,x!==g&&(x===null?K.firstBaseUpdate=Z:x.next=Z,K.lastBaseUpdate=R))}if(f!==null){var W=l.baseState;g=0,K=Z=R=null,x=f;do{var H=x.lane&-536870913,k=H!==x.lane;if(k?(Mt&H)===H:(r&H)===H){H!==0&&H===Zr&&(oc=!0),K!==null&&(K=K.next={lane:0,tag:x.tag,payload:x.payload,callback:null,next:null});t:{var gt=t,pt=x;H=e;var Vt=i;switch(pt.tag){case 1:if(gt=pt.payload,typeof gt=="function"){W=gt.call(Vt,W,H);break t}W=gt;break t;case 3:gt.flags=gt.flags&-65537|128;case 0:if(gt=pt.payload,H=typeof gt=="function"?gt.call(Vt,W,H):gt,H==null)break t;W=E({},W,H);break t;case 2:ca=!0}}H=x.callback,H!==null&&(t.flags|=64,k&&(t.flags|=8192),k=l.callbacks,k===null?l.callbacks=[H]:k.push(H))}else k={lane:H,tag:x.tag,payload:x.payload,callback:x.callback,next:null},K===null?(Z=K=k,R=W):K=K.next=k,g|=H;if(x=x.next,x===null){if(x=l.shared.pending,x===null)break;k=x,x=k.next,k.next=null,l.lastBaseUpdate=k,l.shared.pending=null}}while(!0);K===null&&(R=W),l.baseState=R,l.firstBaseUpdate=Z,l.lastBaseUpdate=K,f===null&&(l.shared.lanes=0),ba|=g,t.lanes=g,t.memoizedState=W}}function bh(t,e){if(typeof t!="function")throw Error(h(191,t));t.call(e)}function xh(t,e){var i=t.callbacks;if(i!==null)for(t.callbacks=null,t=0;t<i.length;t++)bh(i[t],e)}var kr=Q(null),yl=Q(0);function wh(t,e){t=Ri,tt(yl,t),tt(kr,e),Ri=t|e.baseLanes}function sc(){tt(yl,Ri),tt(kr,kr.current)}function lc(){Ri=yl.current,nt(kr),nt(yl)}var da=0,Et=null,Yt=null,ce=null,bl=!1,qr=!1,rr=!1,xl=0,Yo=0,Yr=null,j_=0;function se(){throw Error(h(321))}function uc(t,e){if(e===null)return!1;for(var i=0;i<e.length&&i<t.length;i++)if(!Ze(t[i],e[i]))return!1;return!0}function cc(t,e,i,r,l,f){return da=f,Et=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,V.H=t===null||t.memoizedState===null?rd:od,rr=!1,f=i(r,l),rr=!1,qr&&(f=Eh(e,i,r,l)),Sh(t),f}function Sh(t){V.H=Ol;var e=Yt!==null&&Yt.next!==null;if(da=0,ce=Yt=Et=null,bl=!1,Yo=0,Yr=null,e)throw Error(h(300));t===null||ye||(t=t.dependencies,t!==null&&ml(t)&&(ye=!0))}function Eh(t,e,i,r){Et=t;var l=0;do{if(qr&&(Yr=null),Yo=0,qr=!1,25<=l)throw Error(h(301));if(l+=1,ce=Yt=null,t.updateQueue!=null){var f=t.updateQueue;f.lastEffect=null,f.events=null,f.stores=null,f.memoCache!=null&&(f.memoCache.index=0)}V.H=Y_,f=e(i,r)}while(qr);return f}function U_(){var t=V.H,e=t.useState()[0];return e=typeof e.then=="function"?Go(e):e,t=t.useState()[0],(Yt!==null?Yt.memoizedState:null)!==t&&(Et.flags|=1024),e}function fc(){var t=xl!==0;return xl=0,t}function hc(t,e,i){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i}function dc(t){if(bl){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}bl=!1}da=0,ce=Yt=Et=null,qr=!1,Yo=xl=0,Yr=null}function Je(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ce===null?Et.memoizedState=ce=t:ce=ce.next=t,ce}function fe(){if(Yt===null){var t=Et.alternate;t=t!==null?t.memoizedState:null}else t=Yt.next;var e=ce===null?Et.memoizedState:ce.next;if(e!==null)ce=e,Yt=t;else{if(t===null)throw Et.alternate===null?Error(h(467)):Error(h(310));Yt=t,t={memoizedState:Yt.memoizedState,baseState:Yt.baseState,baseQueue:Yt.baseQueue,queue:Yt.queue,next:null},ce===null?Et.memoizedState=ce=t:ce=ce.next=t}return ce}function mc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Go(t){var e=Yo;return Yo+=1,Yr===null&&(Yr=[]),t=gh(Yr,t,e),e=Et,(ce===null?e.memoizedState:ce.next)===null&&(e=e.alternate,V.H=e===null||e.memoizedState===null?rd:od),t}function wl(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Go(t);if(t.$$typeof===et)return De(t)}throw Error(h(438,String(t)))}function pc(t){var e=null,i=Et.updateQueue;if(i!==null&&(e=i.memoCache),e==null){var r=Et.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(e={data:r.data.map(function(l){return l.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),i===null&&(i=mc(),Et.updateQueue=i),i.memoCache=e,i=e.data[e.index],i===void 0)for(i=e.data[e.index]=Array(t),r=0;r<t;r++)i[r]=me;return e.index++,i}function Si(t,e){return typeof e=="function"?e(t):e}function Sl(t){var e=fe();return _c(e,Yt,t)}function _c(t,e,i){var r=t.queue;if(r===null)throw Error(h(311));r.lastRenderedReducer=i;var l=t.baseQueue,f=r.pending;if(f!==null){if(l!==null){var g=l.next;l.next=f.next,f.next=g}e.baseQueue=l=f,r.pending=null}if(f=t.baseState,l===null)t.memoizedState=f;else{e=l.next;var x=g=null,R=null,Z=e,K=!1;do{var W=Z.lane&-536870913;if(W!==Z.lane?(Mt&W)===W:(da&W)===W){var H=Z.revertLane;if(H===0)R!==null&&(R=R.next={lane:0,revertLane:0,action:Z.action,hasEagerState:Z.hasEagerState,eagerState:Z.eagerState,next:null}),W===Zr&&(K=!0);else if((da&H)===H){Z=Z.next,H===Zr&&(K=!0);continue}else W={lane:0,revertLane:Z.revertLane,action:Z.action,hasEagerState:Z.hasEagerState,eagerState:Z.eagerState,next:null},R===null?(x=R=W,g=f):R=R.next=W,Et.lanes|=H,ba|=H;W=Z.action,rr&&i(f,W),f=Z.hasEagerState?Z.eagerState:i(f,W)}else H={lane:W,revertLane:Z.revertLane,action:Z.action,hasEagerState:Z.hasEagerState,eagerState:Z.eagerState,next:null},R===null?(x=R=H,g=f):R=R.next=H,Et.lanes|=W,ba|=W;Z=Z.next}while(Z!==null&&Z!==e);if(R===null?g=f:R.next=x,!Ze(f,t.memoizedState)&&(ye=!0,K&&(i=Hr,i!==null)))throw i;t.memoizedState=f,t.baseState=g,t.baseQueue=R,r.lastRenderedState=f}return l===null&&(r.lanes=0),[t.memoizedState,r.dispatch]}function gc(t){var e=fe(),i=e.queue;if(i===null)throw Error(h(311));i.lastRenderedReducer=t;var r=i.dispatch,l=i.pending,f=e.memoizedState;if(l!==null){i.pending=null;var g=l=l.next;do f=t(f,g.action),g=g.next;while(g!==l);Ze(f,e.memoizedState)||(ye=!0),e.memoizedState=f,e.baseQueue===null&&(e.baseState=f),i.lastRenderedState=f}return[f,r]}function Th(t,e,i){var r=Et,l=fe(),f=vt;if(f){if(i===void 0)throw Error(h(407));i=i()}else i=e();var g=!Ze((Yt||l).memoizedState,i);g&&(l.memoizedState=i,ye=!0),l=l.queue;var x=Ah.bind(null,r,l,t);if(Vo(2048,8,x,[t]),l.getSnapshot!==e||g||ce!==null&&ce.memoizedState.tag&1){if(r.flags|=2048,Gr(9,El(),Oh.bind(null,r,l,i,e),null),Wt===null)throw Error(h(349));f||(da&124)!==0||Lh(r,e,i)}return i}function Lh(t,e,i){t.flags|=16384,t={getSnapshot:e,value:i},e=Et.updateQueue,e===null?(e=mc(),Et.updateQueue=e,e.stores=[t]):(i=e.stores,i===null?e.stores=[t]:i.push(t))}function Oh(t,e,i,r){e.value=i,e.getSnapshot=r,Rh(e)&&Mh(t)}function Ah(t,e,i){return i(function(){Rh(e)&&Mh(t)})}function Rh(t){var e=t.getSnapshot;t=t.value;try{var i=e();return!Ze(t,i)}catch{return!0}}function Mh(t){var e=oa(t,2);e!==null&&hn(e,t,2)}function vc(t){var e=Je();if(typeof t=="function"){var i=t;if(t=i(),rr){Tn(!0);try{i()}finally{Tn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Si,lastRenderedState:t},e}function Ch(t,e,i,r){return t.baseState=i,_c(t,Yt,typeof r=="function"?r:Si)}function P_(t,e,i,r,l){if(Ll(t))throw Error(h(485));if(t=e.action,t!==null){var f={payload:l,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(g){f.listeners.push(g)}};V.T!==null?i(!0):f.isTransition=!1,r(f),i=e.pending,i===null?(f.next=e.pending=f,Nh(e,f)):(f.next=i.next,e.pending=i.next=f)}}function Nh(t,e){var i=e.action,r=e.payload,l=t.state;if(e.isTransition){var f=V.T,g={};V.T=g;try{var x=i(l,r),R=V.S;R!==null&&R(g,x),zh(t,e,x)}catch(Z){yc(t,e,Z)}finally{V.T=f}}else try{f=i(l,r),zh(t,e,f)}catch(Z){yc(t,e,Z)}}function zh(t,e,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(r){Dh(t,e,r)},function(r){return yc(t,e,r)}):Dh(t,e,i)}function Dh(t,e,i){e.status="fulfilled",e.value=i,Bh(e),t.state=i,e=t.pending,e!==null&&(i=e.next,i===e?t.pending=null:(i=i.next,e.next=i,Nh(t,i)))}function yc(t,e,i){var r=t.pending;if(t.pending=null,r!==null){r=r.next;do e.status="rejected",e.reason=i,Bh(e),e=e.next;while(e!==r)}t.action=null}function Bh(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function jh(t,e){return e}function Uh(t,e){if(vt){var i=Wt.formState;if(i!==null){t:{var r=Et;if(vt){if(ht){e:{for(var l=ht,f=ne;l.nodeType!==8;){if(!f){l=null;break e}if(l=Dn(l.nextSibling),l===null){l=null;break e}}f=l.data,l=f==="F!"||f==="F"?l:null}if(l){ht=Dn(l.nextSibling),r=l.data==="F!";break t}}Ke(r)}r=!1}r&&(e=i[0])}}return i=Je(),i.memoizedState=i.baseState=e,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:jh,lastRenderedState:e},i.queue=r,i=nd.bind(null,Et,r),r.dispatch=i,r=vc(!1),f=Ec.bind(null,Et,!1,r.queue),r=Je(),l={state:e,dispatch:null,action:t,pending:null},r.queue=l,i=P_.bind(null,Et,l,f,i),l.dispatch=i,r.memoizedState=t,[e,i,!1]}function Ph(t){var e=fe();return Zh(e,Yt,t)}function Zh(t,e,i){if(e=_c(t,e,jh)[0],t=Sl(Si)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var r=Go(e)}catch(g){throw g===Po?gl:g}else r=e;e=fe();var l=e.queue,f=l.dispatch;return i!==e.memoizedState&&(Et.flags|=2048,Gr(9,El(),Z_.bind(null,l,i),null)),[r,f,t]}function Z_(t,e){t.action=e}function Hh(t){var e=fe(),i=Yt;if(i!==null)return Zh(e,i,t);fe(),e=e.memoizedState,i=fe();var r=i.queue.dispatch;return i.memoizedState=t,[e,r,!1]}function Gr(t,e,i,r){return t={tag:t,create:i,deps:r,inst:e,next:null},e=Et.updateQueue,e===null&&(e=mc(),Et.updateQueue=e),i=e.lastEffect,i===null?e.lastEffect=t.next=t:(r=i.next,i.next=t,t.next=r,e.lastEffect=t),t}function El(){return{destroy:void 0,resource:void 0}}function kh(){return fe().memoizedState}function Tl(t,e,i,r){var l=Je();r=r===void 0?null:r,Et.flags|=t,l.memoizedState=Gr(1|e,El(),i,r)}function Vo(t,e,i,r){var l=fe();r=r===void 0?null:r;var f=l.memoizedState.inst;Yt!==null&&r!==null&&uc(r,Yt.memoizedState.deps)?l.memoizedState=Gr(e,f,i,r):(Et.flags|=t,l.memoizedState=Gr(1|e,f,i,r))}function qh(t,e){Tl(8390656,8,t,e)}function Yh(t,e){Vo(2048,8,t,e)}function Gh(t,e){return Vo(4,2,t,e)}function Vh(t,e){return Vo(4,4,t,e)}function Xh(t,e){if(typeof e=="function"){t=t();var i=e(t);return function(){typeof i=="function"?i():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Qh(t,e,i){i=i!=null?i.concat([t]):null,Vo(4,4,Xh.bind(null,e,t),i)}function bc(){}function Kh(t,e){var i=fe();e=e===void 0?null:e;var r=i.memoizedState;return e!==null&&uc(e,r[1])?r[0]:(i.memoizedState=[t,e],t)}function Jh(t,e){var i=fe();e=e===void 0?null:e;var r=i.memoizedState;if(e!==null&&uc(e,r[1]))return r[0];if(r=t(),rr){Tn(!0);try{t()}finally{Tn(!1)}}return i.memoizedState=[r,e],r}function xc(t,e,i){return i===void 0||(da&1073741824)!==0?t.memoizedState=e:(t.memoizedState=i,t=Id(),Et.lanes|=t,ba|=t,i)}function Fh(t,e,i,r){return Ze(i,e)?i:kr.current!==null?(t=xc(t,i,r),Ze(t,e)||(ye=!0),t):(da&42)===0?(ye=!0,t.memoizedState=i):(t=Id(),Et.lanes|=t,ba|=t,e)}function Wh(t,e,i,r,l){var f=rt.p;rt.p=f!==0&&8>f?f:8;var g=V.T,x={};V.T=x,Ec(t,!1,e,i);try{var R=l(),Z=V.S;if(Z!==null&&Z(x,R),R!==null&&typeof R=="object"&&typeof R.then=="function"){var K=B_(R,r);Xo(t,e,K,fn(t))}else Xo(t,e,r,fn(t))}catch(W){Xo(t,e,{then:function(){},status:"rejected",reason:W},fn())}finally{rt.p=f,V.T=g}}function H_(){}function wc(t,e,i,r){if(t.tag!==5)throw Error(h(476));var l=Ih(t).queue;Wh(t,l,e,$,i===null?H_:function(){return $h(t),i(r)})}function Ih(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:$,baseState:$,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Si,lastRenderedState:$},next:null};var i={};return e.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Si,lastRenderedState:i},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function $h(t){var e=Ih(t).next.queue;Xo(t,e,{},fn())}function Sc(){return De(cs)}function td(){return fe().memoizedState}function ed(){return fe().memoizedState}function k_(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var i=fn();t=fa(i);var r=ha(e,t,i);r!==null&&(hn(r,e,i),Ho(r,e,i)),e={cache:$u()},t.payload=e;return}e=e.return}}function q_(t,e,i){var r=fn();i={lane:r,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},Ll(t)?id(e,i):(i=bi(t,e,i,r),i!==null&&(hn(i,t,r),ad(i,e,r)))}function nd(t,e,i){var r=fn();Xo(t,e,i,r)}function Xo(t,e,i,r){var l={lane:r,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if(Ll(t))id(e,l);else{var f=t.alternate;if(t.lanes===0&&(f===null||f.lanes===0)&&(f=e.lastRenderedReducer,f!==null))try{var g=e.lastRenderedState,x=f(g,i);if(l.hasEagerState=!0,l.eagerState=x,Ze(x,g))return Wa(t,e,l,0),Wt===null&&Fa(),!1}catch{}finally{}if(i=bi(t,e,l,r),i!==null)return hn(i,t,r),ad(i,e,r),!0}return!1}function Ec(t,e,i,r){if(r={lane:2,revertLane:nf(),action:r,hasEagerState:!1,eagerState:null,next:null},Ll(t)){if(e)throw Error(h(479))}else e=bi(t,i,r,2),e!==null&&hn(e,t,2)}function Ll(t){var e=t.alternate;return t===Et||e!==null&&e===Et}function id(t,e){qr=bl=!0;var i=t.pending;i===null?e.next=e:(e.next=i.next,i.next=e),t.pending=e}function ad(t,e,i){if((i&4194048)!==0){var r=e.lanes;r&=t.pendingLanes,i|=r,e.lanes=i,Zs(t,i)}}var Ol={readContext:De,use:wl,useCallback:se,useContext:se,useEffect:se,useImperativeHandle:se,useLayoutEffect:se,useInsertionEffect:se,useMemo:se,useReducer:se,useRef:se,useState:se,useDebugValue:se,useDeferredValue:se,useTransition:se,useSyncExternalStore:se,useId:se,useHostTransitionStatus:se,useFormState:se,useActionState:se,useOptimistic:se,useMemoCache:se,useCacheRefresh:se},rd={readContext:De,use:wl,useCallback:function(t,e){return Je().memoizedState=[t,e===void 0?null:e],t},useContext:De,useEffect:qh,useImperativeHandle:function(t,e,i){i=i!=null?i.concat([t]):null,Tl(4194308,4,Xh.bind(null,e,t),i)},useLayoutEffect:function(t,e){return Tl(4194308,4,t,e)},useInsertionEffect:function(t,e){Tl(4,2,t,e)},useMemo:function(t,e){var i=Je();e=e===void 0?null:e;var r=t();if(rr){Tn(!0);try{t()}finally{Tn(!1)}}return i.memoizedState=[r,e],r},useReducer:function(t,e,i){var r=Je();if(i!==void 0){var l=i(e);if(rr){Tn(!0);try{i(e)}finally{Tn(!1)}}}else l=e;return r.memoizedState=r.baseState=l,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:l},r.queue=t,t=t.dispatch=q_.bind(null,Et,t),[r.memoizedState,t]},useRef:function(t){var e=Je();return t={current:t},e.memoizedState=t},useState:function(t){t=vc(t);var e=t.queue,i=nd.bind(null,Et,e);return e.dispatch=i,[t.memoizedState,i]},useDebugValue:bc,useDeferredValue:function(t,e){var i=Je();return xc(i,t,e)},useTransition:function(){var t=vc(!1);return t=Wh.bind(null,Et,t.queue,!0,!1),Je().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,i){var r=Et,l=Je();if(vt){if(i===void 0)throw Error(h(407));i=i()}else{if(i=e(),Wt===null)throw Error(h(349));(Mt&124)!==0||Lh(r,e,i)}l.memoizedState=i;var f={value:i,getSnapshot:e};return l.queue=f,qh(Ah.bind(null,r,f,t),[t]),r.flags|=2048,Gr(9,El(),Oh.bind(null,r,f,i,e),null),i},useId:function(){var t=Je(),e=Wt.identifierPrefix;if(vt){var i=A,r=b;i=(r&~(1<<32-Ue(r)-1)).toString(32)+i,e="«"+e+"R"+i,i=xl++,0<i&&(e+="H"+i.toString(32)),e+="»"}else i=j_++,e="«"+e+"r"+i.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Sc,useFormState:Uh,useActionState:Uh,useOptimistic:function(t){var e=Je();e.memoizedState=e.baseState=t;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=i,e=Ec.bind(null,Et,!0,i),i.dispatch=e,[t,e]},useMemoCache:pc,useCacheRefresh:function(){return Je().memoizedState=k_.bind(null,Et)}},od={readContext:De,use:wl,useCallback:Kh,useContext:De,useEffect:Yh,useImperativeHandle:Qh,useInsertionEffect:Gh,useLayoutEffect:Vh,useMemo:Jh,useReducer:Sl,useRef:kh,useState:function(){return Sl(Si)},useDebugValue:bc,useDeferredValue:function(t,e){var i=fe();return Fh(i,Yt.memoizedState,t,e)},useTransition:function(){var t=Sl(Si)[0],e=fe().memoizedState;return[typeof t=="boolean"?t:Go(t),e]},useSyncExternalStore:Th,useId:td,useHostTransitionStatus:Sc,useFormState:Ph,useActionState:Ph,useOptimistic:function(t,e){var i=fe();return Ch(i,Yt,t,e)},useMemoCache:pc,useCacheRefresh:ed},Y_={readContext:De,use:wl,useCallback:Kh,useContext:De,useEffect:Yh,useImperativeHandle:Qh,useInsertionEffect:Gh,useLayoutEffect:Vh,useMemo:Jh,useReducer:gc,useRef:kh,useState:function(){return gc(Si)},useDebugValue:bc,useDeferredValue:function(t,e){var i=fe();return Yt===null?xc(i,t,e):Fh(i,Yt.memoizedState,t,e)},useTransition:function(){var t=gc(Si)[0],e=fe().memoizedState;return[typeof t=="boolean"?t:Go(t),e]},useSyncExternalStore:Th,useId:td,useHostTransitionStatus:Sc,useFormState:Hh,useActionState:Hh,useOptimistic:function(t,e){var i=fe();return Yt!==null?Ch(i,Yt,t,e):(i.baseState=t,[t,i.queue.dispatch])},useMemoCache:pc,useCacheRefresh:ed},Vr=null,Qo=0;function Al(t){var e=Qo;return Qo+=1,Vr===null&&(Vr=[]),gh(Vr,t,e)}function Ko(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Rl(t,e){throw e.$$typeof===z?Error(h(525)):(t=Object.prototype.toString.call(e),Error(h(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function sd(t){var e=t._init;return e(t._payload)}function ld(t){function e(D,C){if(t){var P=D.deletions;P===null?(D.deletions=[C],D.flags|=16):P.push(C)}}function i(D,C){if(!t)return null;for(;C!==null;)e(D,C),C=C.sibling;return null}function r(D){for(var C=new Map;D!==null;)D.key!==null?C.set(D.key,D):C.set(D.index,D),D=D.sibling;return C}function l(D,C){return D=yn(D,C),D.index=0,D.sibling=null,D}function f(D,C,P){return D.index=P,t?(P=D.alternate,P!==null?(P=P.index,P<C?(D.flags|=67108866,C):P):(D.flags|=67108866,C)):(D.flags|=1048576,C)}function g(D){return t&&D.alternate===null&&(D.flags|=67108866),D}function x(D,C,P,F){return C===null||C.tag!==6?(C=Bo(P,D.mode,F),C.return=D,C):(C=l(C,P),C.return=D,C)}function R(D,C,P,F){var ct=P.type;return ct===N?K(D,C,P.props.children,F,P.key):C!==null&&(C.elementType===ct||typeof ct=="object"&&ct!==null&&ct.$$typeof===Pt&&sd(ct)===C.type)?(C=l(C,P.props),Ko(C,P),C.return=D,C):(C=$a(P.type,P.key,P.props,null,D.mode,F),Ko(C,P),C.return=D,C)}function Z(D,C,P,F){return C===null||C.tag!==4||C.stateNode.containerInfo!==P.containerInfo||C.stateNode.implementation!==P.implementation?(C=Ur(P,D.mode,F),C.return=D,C):(C=l(C,P.children||[]),C.return=D,C)}function K(D,C,P,F,ct){return C===null||C.tag!==7?(C=Gn(P,D.mode,F,ct),C.return=D,C):(C=l(C,P),C.return=D,C)}function W(D,C,P){if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return C=Bo(""+C,D.mode,P),C.return=D,C;if(typeof C=="object"&&C!==null){switch(C.$$typeof){case B:return P=$a(C.type,C.key,C.props,null,D.mode,P),Ko(P,C),P.return=D,P;case J:return C=Ur(C,D.mode,P),C.return=D,C;case Pt:var F=C._init;return C=F(C._payload),W(D,C,P)}if(pe(C)||Zt(C))return C=Gn(C,D.mode,P,null),C.return=D,C;if(typeof C.then=="function")return W(D,Al(C),P);if(C.$$typeof===et)return W(D,pl(D,C),P);Rl(D,C)}return null}function H(D,C,P,F){var ct=C!==null?C.key:null;if(typeof P=="string"&&P!==""||typeof P=="number"||typeof P=="bigint")return ct!==null?null:x(D,C,""+P,F);if(typeof P=="object"&&P!==null){switch(P.$$typeof){case B:return P.key===ct?R(D,C,P,F):null;case J:return P.key===ct?Z(D,C,P,F):null;case Pt:return ct=P._init,P=ct(P._payload),H(D,C,P,F)}if(pe(P)||Zt(P))return ct!==null?null:K(D,C,P,F,null);if(typeof P.then=="function")return H(D,C,Al(P),F);if(P.$$typeof===et)return H(D,C,pl(D,P),F);Rl(D,P)}return null}function k(D,C,P,F,ct){if(typeof F=="string"&&F!==""||typeof F=="number"||typeof F=="bigint")return D=D.get(P)||null,x(C,D,""+F,ct);if(typeof F=="object"&&F!==null){switch(F.$$typeof){case B:return D=D.get(F.key===null?P:F.key)||null,R(C,D,F,ct);case J:return D=D.get(F.key===null?P:F.key)||null,Z(C,D,F,ct);case Pt:var Lt=F._init;return F=Lt(F._payload),k(D,C,P,F,ct)}if(pe(F)||Zt(F))return D=D.get(P)||null,K(C,D,F,ct,null);if(typeof F.then=="function")return k(D,C,P,Al(F),ct);if(F.$$typeof===et)return k(D,C,P,pl(C,F),ct);Rl(C,F)}return null}function gt(D,C,P,F){for(var ct=null,Lt=null,mt=C,_t=C=0,xe=null;mt!==null&&_t<P.length;_t++){mt.index>_t?(xe=mt,mt=null):xe=mt.sibling;var Bt=H(D,mt,P[_t],F);if(Bt===null){mt===null&&(mt=xe);break}t&&mt&&Bt.alternate===null&&e(D,mt),C=f(Bt,C,_t),Lt===null?ct=Bt:Lt.sibling=Bt,Lt=Bt,mt=xe}if(_t===P.length)return i(D,mt),vt&&j(D,_t),ct;if(mt===null){for(;_t<P.length;_t++)mt=W(D,P[_t],F),mt!==null&&(C=f(mt,C,_t),Lt===null?ct=mt:Lt.sibling=mt,Lt=mt);return vt&&j(D,_t),ct}for(mt=r(mt);_t<P.length;_t++)xe=k(mt,D,_t,P[_t],F),xe!==null&&(t&&xe.alternate!==null&&mt.delete(xe.key===null?_t:xe.key),C=f(xe,C,_t),Lt===null?ct=xe:Lt.sibling=xe,Lt=xe);return t&&mt.forEach(function(Ra){return e(D,Ra)}),vt&&j(D,_t),ct}function pt(D,C,P,F){if(P==null)throw Error(h(151));for(var ct=null,Lt=null,mt=C,_t=C=0,xe=null,Bt=P.next();mt!==null&&!Bt.done;_t++,Bt=P.next()){mt.index>_t?(xe=mt,mt=null):xe=mt.sibling;var Ra=H(D,mt,Bt.value,F);if(Ra===null){mt===null&&(mt=xe);break}t&&mt&&Ra.alternate===null&&e(D,mt),C=f(Ra,C,_t),Lt===null?ct=Ra:Lt.sibling=Ra,Lt=Ra,mt=xe}if(Bt.done)return i(D,mt),vt&&j(D,_t),ct;if(mt===null){for(;!Bt.done;_t++,Bt=P.next())Bt=W(D,Bt.value,F),Bt!==null&&(C=f(Bt,C,_t),Lt===null?ct=Bt:Lt.sibling=Bt,Lt=Bt);return vt&&j(D,_t),ct}for(mt=r(mt);!Bt.done;_t++,Bt=P.next())Bt=k(mt,D,_t,Bt.value,F),Bt!==null&&(t&&Bt.alternate!==null&&mt.delete(Bt.key===null?_t:Bt.key),C=f(Bt,C,_t),Lt===null?ct=Bt:Lt.sibling=Bt,Lt=Bt);return t&&mt.forEach(function(Gg){return e(D,Gg)}),vt&&j(D,_t),ct}function Vt(D,C,P,F){if(typeof P=="object"&&P!==null&&P.type===N&&P.key===null&&(P=P.props.children),typeof P=="object"&&P!==null){switch(P.$$typeof){case B:t:{for(var ct=P.key;C!==null;){if(C.key===ct){if(ct=P.type,ct===N){if(C.tag===7){i(D,C.sibling),F=l(C,P.props.children),F.return=D,D=F;break t}}else if(C.elementType===ct||typeof ct=="object"&&ct!==null&&ct.$$typeof===Pt&&sd(ct)===C.type){i(D,C.sibling),F=l(C,P.props),Ko(F,P),F.return=D,D=F;break t}i(D,C);break}else e(D,C);C=C.sibling}P.type===N?(F=Gn(P.props.children,D.mode,F,P.key),F.return=D,D=F):(F=$a(P.type,P.key,P.props,null,D.mode,F),Ko(F,P),F.return=D,D=F)}return g(D);case J:t:{for(ct=P.key;C!==null;){if(C.key===ct)if(C.tag===4&&C.stateNode.containerInfo===P.containerInfo&&C.stateNode.implementation===P.implementation){i(D,C.sibling),F=l(C,P.children||[]),F.return=D,D=F;break t}else{i(D,C);break}else e(D,C);C=C.sibling}F=Ur(P,D.mode,F),F.return=D,D=F}return g(D);case Pt:return ct=P._init,P=ct(P._payload),Vt(D,C,P,F)}if(pe(P))return gt(D,C,P,F);if(Zt(P)){if(ct=Zt(P),typeof ct!="function")throw Error(h(150));return P=ct.call(P),pt(D,C,P,F)}if(typeof P.then=="function")return Vt(D,C,Al(P),F);if(P.$$typeof===et)return Vt(D,C,pl(D,P),F);Rl(D,P)}return typeof P=="string"&&P!==""||typeof P=="number"||typeof P=="bigint"?(P=""+P,C!==null&&C.tag===6?(i(D,C.sibling),F=l(C,P),F.return=D,D=F):(i(D,C),F=Bo(P,D.mode,F),F.return=D,D=F),g(D)):i(D,C)}return function(D,C,P,F){try{Qo=0;var ct=Vt(D,C,P,F);return Vr=null,ct}catch(mt){if(mt===Po||mt===gl)throw mt;var Lt=Ye(29,mt,null,D.mode);return Lt.lanes=F,Lt.return=D,Lt}finally{}}}var Xr=ld(!0),ud=ld(!1),xn=Q(null),Xn=null;function ma(t){var e=t.alternate;tt(de,de.current&1),tt(xn,t),Xn===null&&(e===null||kr.current!==null||e.memoizedState!==null)&&(Xn=t)}function cd(t){if(t.tag===22){if(tt(de,de.current),tt(xn,t),Xn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Xn=t)}}else pa()}function pa(){tt(de,de.current),tt(xn,xn.current)}function Ei(t){nt(xn),Xn===t&&(Xn=null),nt(de)}var de=Q(0);function Ml(t){for(var e=t;e!==null;){if(e.tag===13){var i=e.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||pf(i)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Tc(t,e,i,r){e=t.memoizedState,i=i(r,e),i=i==null?e:E({},e,i),t.memoizedState=i,t.lanes===0&&(t.updateQueue.baseState=i)}var Lc={enqueueSetState:function(t,e,i){t=t._reactInternals;var r=fn(),l=fa(r);l.payload=e,i!=null&&(l.callback=i),e=ha(t,l,r),e!==null&&(hn(e,t,r),Ho(e,t,r))},enqueueReplaceState:function(t,e,i){t=t._reactInternals;var r=fn(),l=fa(r);l.tag=1,l.payload=e,i!=null&&(l.callback=i),e=ha(t,l,r),e!==null&&(hn(e,t,r),Ho(e,t,r))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var i=fn(),r=fa(i);r.tag=2,e!=null&&(r.callback=e),e=ha(t,r,i),e!==null&&(hn(e,t,i),Ho(e,t,i))}};function fd(t,e,i,r,l,f,g){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,f,g):e.prototype&&e.prototype.isPureReactComponent?!He(i,r)||!He(l,f):!0}function hd(t,e,i,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(i,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(i,r),e.state!==t&&Lc.enqueueReplaceState(e,e.state,null)}function or(t,e){var i=e;if("ref"in e){i={};for(var r in e)r!=="ref"&&(i[r]=e[r])}if(t=t.defaultProps){i===e&&(i=E({},i));for(var l in t)i[l]===void 0&&(i[l]=t[l])}return i}var Cl=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function dd(t){Cl(t)}function md(t){console.error(t)}function pd(t){Cl(t)}function Nl(t,e){try{var i=t.onUncaughtError;i(e.value,{componentStack:e.stack})}catch(r){setTimeout(function(){throw r})}}function _d(t,e,i){try{var r=t.onCaughtError;r(i.value,{componentStack:i.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(l){setTimeout(function(){throw l})}}function Oc(t,e,i){return i=fa(i),i.tag=3,i.payload={element:null},i.callback=function(){Nl(t,e)},i}function gd(t){return t=fa(t),t.tag=3,t}function vd(t,e,i,r){var l=i.type.getDerivedStateFromError;if(typeof l=="function"){var f=r.value;t.payload=function(){return l(f)},t.callback=function(){_d(e,i,r)}}var g=i.stateNode;g!==null&&typeof g.componentDidCatch=="function"&&(t.callback=function(){_d(e,i,r),typeof l!="function"&&(xa===null?xa=new Set([this]):xa.add(this));var x=r.stack;this.componentDidCatch(r.value,{componentStack:x!==null?x:""})})}function G_(t,e,i,r,l){if(i.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(e=i.alternate,e!==null&&nr(e,i,l,!0),i=xn.current,i!==null){switch(i.tag){case 13:return Xn===null?Wc():i.alternate===null&&oe===0&&(oe=3),i.flags&=-257,i.flags|=65536,i.lanes=l,r===nc?i.flags|=16384:(e=i.updateQueue,e===null?i.updateQueue=new Set([r]):e.add(r),$c(t,r,l)),!1;case 22:return i.flags|=65536,r===nc?i.flags|=16384:(e=i.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([r])},i.updateQueue=e):(i=e.retryQueue,i===null?e.retryQueue=new Set([r]):i.add(r)),$c(t,r,l)),!1}throw Error(h(435,i.tag))}return $c(t,r,l),Wc(),!1}if(vt)return e=xn.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=l,r!==ve&&(t=Error(h(422),{cause:r}),ua(ke(t,i)))):(r!==ve&&(e=Error(h(423),{cause:r}),ua(ke(e,i))),t=t.current.alternate,t.flags|=65536,l&=-l,t.lanes|=l,r=ke(r,i),l=Oc(t.stateNode,r,l),rc(t,l),oe!==4&&(oe=2)),!1;var f=Error(h(520),{cause:r});if(f=ke(f,i),es===null?es=[f]:es.push(f),oe!==4&&(oe=2),e===null)return!0;r=ke(r,i),i=e;do{switch(i.tag){case 3:return i.flags|=65536,t=l&-l,i.lanes|=t,t=Oc(i.stateNode,r,t),rc(i,t),!1;case 1:if(e=i.type,f=i.stateNode,(i.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(xa===null||!xa.has(f))))return i.flags|=65536,l&=-l,i.lanes|=l,l=gd(l),vd(l,t,i,r),rc(i,l),!1}i=i.return}while(i!==null);return!1}var yd=Error(h(461)),ye=!1;function Oe(t,e,i,r){e.child=t===null?ud(e,null,i,r):Xr(e,t.child,i,r)}function bd(t,e,i,r,l){i=i.render;var f=e.ref;if("ref"in r){var g={};for(var x in r)x!=="ref"&&(g[x]=r[x])}else g=r;return ir(e),r=cc(t,e,i,g,f,l),x=fc(),t!==null&&!ye?(hc(t,e,l),Ti(t,e,l)):(vt&&x&&I(e),e.flags|=1,Oe(t,e,r,l),e.child)}function xd(t,e,i,r,l){if(t===null){var f=i.type;return typeof f=="function"&&!jr(f)&&f.defaultProps===void 0&&i.compare===null?(e.tag=15,e.type=f,wd(t,e,f,r,l)):(t=$a(i.type,null,r,e,e.mode,l),t.ref=e.ref,t.return=e,e.child=t)}if(f=t.child,!Bc(t,l)){var g=f.memoizedProps;if(i=i.compare,i=i!==null?i:He,i(g,r)&&t.ref===e.ref)return Ti(t,e,l)}return e.flags|=1,t=yn(f,r),t.ref=e.ref,t.return=e,e.child=t}function wd(t,e,i,r,l){if(t!==null){var f=t.memoizedProps;if(He(f,r)&&t.ref===e.ref)if(ye=!1,e.pendingProps=r=f,Bc(t,l))(t.flags&131072)!==0&&(ye=!0);else return e.lanes=t.lanes,Ti(t,e,l)}return Ac(t,e,i,r,l)}function Sd(t,e,i){var r=e.pendingProps,l=r.children,f=t!==null?t.memoizedState:null;if(r.mode==="hidden"){if((e.flags&128)!==0){if(r=f!==null?f.baseLanes|i:i,t!==null){for(l=e.child=t.child,f=0;l!==null;)f=f|l.lanes|l.childLanes,l=l.sibling;e.childLanes=f&~r}else e.childLanes=0,e.child=null;return Ed(t,e,r,i)}if((i&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&_l(e,f!==null?f.cachePool:null),f!==null?wh(e,f):sc(),cd(e);else return e.lanes=e.childLanes=536870912,Ed(t,e,f!==null?f.baseLanes|i:i,i)}else f!==null?(_l(e,f.cachePool),wh(e,f),pa(),e.memoizedState=null):(t!==null&&_l(e,null),sc(),pa());return Oe(t,e,l,i),e.child}function Ed(t,e,i,r){var l=ec();return l=l===null?null:{parent:he._currentValue,pool:l},e.memoizedState={baseLanes:i,cachePool:l},t!==null&&_l(e,null),sc(),cd(e),t!==null&&nr(t,e,r,!0),null}function zl(t,e){var i=e.ref;if(i===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof i!="function"&&typeof i!="object")throw Error(h(284));(t===null||t.ref!==i)&&(e.flags|=4194816)}}function Ac(t,e,i,r,l){return ir(e),i=cc(t,e,i,r,void 0,l),r=fc(),t!==null&&!ye?(hc(t,e,l),Ti(t,e,l)):(vt&&r&&I(e),e.flags|=1,Oe(t,e,i,l),e.child)}function Td(t,e,i,r,l,f){return ir(e),e.updateQueue=null,i=Eh(e,r,i,l),Sh(t),r=fc(),t!==null&&!ye?(hc(t,e,f),Ti(t,e,f)):(vt&&r&&I(e),e.flags|=1,Oe(t,e,i,f),e.child)}function Ld(t,e,i,r,l){if(ir(e),e.stateNode===null){var f=xi,g=i.contextType;typeof g=="object"&&g!==null&&(f=De(g)),f=new i(r,f),e.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,f.updater=Lc,e.stateNode=f,f._reactInternals=e,f=e.stateNode,f.props=r,f.state=e.memoizedState,f.refs={},ic(e),g=i.contextType,f.context=typeof g=="object"&&g!==null?De(g):xi,f.state=e.memoizedState,g=i.getDerivedStateFromProps,typeof g=="function"&&(Tc(e,i,g,r),f.state=e.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(g=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),g!==f.state&&Lc.enqueueReplaceState(f,f.state,null),qo(e,r,f,l),ko(),f.state=e.memoizedState),typeof f.componentDidMount=="function"&&(e.flags|=4194308),r=!0}else if(t===null){f=e.stateNode;var x=e.memoizedProps,R=or(i,x);f.props=R;var Z=f.context,K=i.contextType;g=xi,typeof K=="object"&&K!==null&&(g=De(K));var W=i.getDerivedStateFromProps;K=typeof W=="function"||typeof f.getSnapshotBeforeUpdate=="function",x=e.pendingProps!==x,K||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(x||Z!==g)&&hd(e,f,r,g),ca=!1;var H=e.memoizedState;f.state=H,qo(e,r,f,l),ko(),Z=e.memoizedState,x||H!==Z||ca?(typeof W=="function"&&(Tc(e,i,W,r),Z=e.memoizedState),(R=ca||fd(e,i,R,r,H,Z,g))?(K||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount()),typeof f.componentDidMount=="function"&&(e.flags|=4194308)):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=Z),f.props=r,f.state=Z,f.context=g,r=R):(typeof f.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{f=e.stateNode,ac(t,e),g=e.memoizedProps,K=or(i,g),f.props=K,W=e.pendingProps,H=f.context,Z=i.contextType,R=xi,typeof Z=="object"&&Z!==null&&(R=De(Z)),x=i.getDerivedStateFromProps,(Z=typeof x=="function"||typeof f.getSnapshotBeforeUpdate=="function")||typeof f.UNSAFE_componentWillReceiveProps!="function"&&typeof f.componentWillReceiveProps!="function"||(g!==W||H!==R)&&hd(e,f,r,R),ca=!1,H=e.memoizedState,f.state=H,qo(e,r,f,l),ko();var k=e.memoizedState;g!==W||H!==k||ca||t!==null&&t.dependencies!==null&&ml(t.dependencies)?(typeof x=="function"&&(Tc(e,i,x,r),k=e.memoizedState),(K=ca||fd(e,i,K,r,H,k,R)||t!==null&&t.dependencies!==null&&ml(t.dependencies))?(Z||typeof f.UNSAFE_componentWillUpdate!="function"&&typeof f.componentWillUpdate!="function"||(typeof f.componentWillUpdate=="function"&&f.componentWillUpdate(r,k,R),typeof f.UNSAFE_componentWillUpdate=="function"&&f.UNSAFE_componentWillUpdate(r,k,R)),typeof f.componentDidUpdate=="function"&&(e.flags|=4),typeof f.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof f.componentDidUpdate!="function"||g===t.memoizedProps&&H===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===t.memoizedProps&&H===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=k),f.props=r,f.state=k,f.context=R,r=K):(typeof f.componentDidUpdate!="function"||g===t.memoizedProps&&H===t.memoizedState||(e.flags|=4),typeof f.getSnapshotBeforeUpdate!="function"||g===t.memoizedProps&&H===t.memoizedState||(e.flags|=1024),r=!1)}return f=r,zl(t,e),r=(e.flags&128)!==0,f||r?(f=e.stateNode,i=r&&typeof i.getDerivedStateFromError!="function"?null:f.render(),e.flags|=1,t!==null&&r?(e.child=Xr(e,t.child,null,l),e.child=Xr(e,null,i,l)):Oe(t,e,i,l),e.memoizedState=f.state,t=e.child):t=Ti(t,e,l),t}function Od(t,e,i,r){return la(),e.flags|=256,Oe(t,e,i,r),e.child}var Rc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Mc(t){return{baseLanes:t,cachePool:mh()}}function Cc(t,e,i){return t=t!==null?t.childLanes&~i:0,e&&(t|=wn),t}function Ad(t,e,i){var r=e.pendingProps,l=!1,f=(e.flags&128)!==0,g;if((g=f)||(g=t!==null&&t.memoizedState===null?!1:(de.current&2)!==0),g&&(l=!0,e.flags&=-129),g=(e.flags&32)!==0,e.flags&=-33,t===null){if(vt){if(l?ma(e):pa(),vt){var x=ht,R;if(R=x){t:{for(R=x,x=ne;R.nodeType!==8;){if(!x){x=null;break t}if(R=Dn(R.nextSibling),R===null){x=null;break t}}x=R}x!==null?(e.memoizedState={dehydrated:x,treeContext:p!==null?{id:b,overflow:A}:null,retryLane:536870912,hydrationErrors:null},R=Ye(18,null,null,0),R.stateNode=x,R.return=e,e.child=R,ut=e,ht=null,R=!0):R=!1}R||Ke(e)}if(x=e.memoizedState,x!==null&&(x=x.dehydrated,x!==null))return pf(x)?e.lanes=32:e.lanes=536870912,null;Ei(e)}return x=r.children,r=r.fallback,l?(pa(),l=e.mode,x=Dl({mode:"hidden",children:x},l),r=Gn(r,l,i,null),x.return=e,r.return=e,x.sibling=r,e.child=x,l=e.child,l.memoizedState=Mc(i),l.childLanes=Cc(t,g,i),e.memoizedState=Rc,r):(ma(e),Nc(e,x))}if(R=t.memoizedState,R!==null&&(x=R.dehydrated,x!==null)){if(f)e.flags&256?(ma(e),e.flags&=-257,e=zc(t,e,i)):e.memoizedState!==null?(pa(),e.child=t.child,e.flags|=128,e=null):(pa(),l=r.fallback,x=e.mode,r=Dl({mode:"visible",children:r.children},x),l=Gn(l,x,i,null),l.flags|=2,r.return=e,l.return=e,r.sibling=l,e.child=r,Xr(e,t.child,null,i),r=e.child,r.memoizedState=Mc(i),r.childLanes=Cc(t,g,i),e.memoizedState=Rc,e=l);else if(ma(e),pf(x)){if(g=x.nextSibling&&x.nextSibling.dataset,g)var Z=g.dgst;g=Z,r=Error(h(419)),r.stack="",r.digest=g,ua({value:r,source:null,stack:null}),e=zc(t,e,i)}else if(ye||nr(t,e,i,!1),g=(i&t.childLanes)!==0,ye||g){if(g=Wt,g!==null&&(r=i&-i,r=(r&42)!==0?1:po(r),r=(r&(g.suspendedLanes|i))!==0?0:r,r!==0&&r!==R.retryLane))throw R.retryLane=r,oa(t,r),hn(g,t,r),yd;x.data==="$?"||Wc(),e=zc(t,e,i)}else x.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=R.treeContext,ht=Dn(x.nextSibling),ut=e,vt=!0,Ft=null,ne=!1,t!==null&&(s[d++]=b,s[d++]=A,s[d++]=p,b=t.id,A=t.overflow,p=e),e=Nc(e,r.children),e.flags|=4096);return e}return l?(pa(),l=r.fallback,x=e.mode,R=t.child,Z=R.sibling,r=yn(R,{mode:"hidden",children:r.children}),r.subtreeFlags=R.subtreeFlags&65011712,Z!==null?l=yn(Z,l):(l=Gn(l,x,i,null),l.flags|=2),l.return=e,r.return=e,r.sibling=l,e.child=r,r=l,l=e.child,x=t.child.memoizedState,x===null?x=Mc(i):(R=x.cachePool,R!==null?(Z=he._currentValue,R=R.parent!==Z?{parent:Z,pool:Z}:R):R=mh(),x={baseLanes:x.baseLanes|i,cachePool:R}),l.memoizedState=x,l.childLanes=Cc(t,g,i),e.memoizedState=Rc,r):(ma(e),i=t.child,t=i.sibling,i=yn(i,{mode:"visible",children:r.children}),i.return=e,i.sibling=null,t!==null&&(g=e.deletions,g===null?(e.deletions=[t],e.flags|=16):g.push(t)),e.child=i,e.memoizedState=null,i)}function Nc(t,e){return e=Dl({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Dl(t,e){return t=Ye(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function zc(t,e,i){return Xr(e,t.child,null,i),t=Nc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Rd(t,e,i){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),er(t.return,e,i)}function Dc(t,e,i,r,l){var f=t.memoizedState;f===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:i,tailMode:l}:(f.isBackwards=e,f.rendering=null,f.renderingStartTime=0,f.last=r,f.tail=i,f.tailMode=l)}function Md(t,e,i){var r=e.pendingProps,l=r.revealOrder,f=r.tail;if(Oe(t,e,r.children,i),r=de.current,(r&2)!==0)r=r&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Rd(t,i,e);else if(t.tag===19)Rd(t,i,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}switch(tt(de,r),l){case"forwards":for(i=e.child,l=null;i!==null;)t=i.alternate,t!==null&&Ml(t)===null&&(l=i),i=i.sibling;i=l,i===null?(l=e.child,e.child=null):(l=i.sibling,i.sibling=null),Dc(e,!1,l,i,f);break;case"backwards":for(i=null,l=e.child,e.child=null;l!==null;){if(t=l.alternate,t!==null&&Ml(t)===null){e.child=l;break}t=l.sibling,l.sibling=i,i=l,l=t}Dc(e,!0,i,null,f);break;case"together":Dc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Ti(t,e,i){if(t!==null&&(e.dependencies=t.dependencies),ba|=e.lanes,(i&e.childLanes)===0)if(t!==null){if(nr(t,e,i,!1),(i&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(h(153));if(e.child!==null){for(t=e.child,i=yn(t,t.pendingProps),e.child=i,i.return=e;t.sibling!==null;)t=t.sibling,i=i.sibling=yn(t,t.pendingProps),i.return=e;i.sibling=null}return e.child}function Bc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&ml(t)))}function V_(t,e,i){switch(e.tag){case 3:zt(e,e.stateNode.containerInfo),Vn(e,he,t.memoizedState.cache),la();break;case 27:case 5:za(e);break;case 4:zt(e,e.stateNode.containerInfo);break;case 10:Vn(e,e.type,e.memoizedProps.value);break;case 13:var r=e.memoizedState;if(r!==null)return r.dehydrated!==null?(ma(e),e.flags|=128,null):(i&e.child.childLanes)!==0?Ad(t,e,i):(ma(e),t=Ti(t,e,i),t!==null?t.sibling:null);ma(e);break;case 19:var l=(t.flags&128)!==0;if(r=(i&e.childLanes)!==0,r||(nr(t,e,i,!1),r=(i&e.childLanes)!==0),l){if(r)return Md(t,e,i);e.flags|=128}if(l=e.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),tt(de,de.current),r)break;return null;case 22:case 23:return e.lanes=0,Sd(t,e,i);case 24:Vn(e,he,t.memoizedState.cache)}return Ti(t,e,i)}function Cd(t,e,i){if(t!==null)if(t.memoizedProps!==e.pendingProps)ye=!0;else{if(!Bc(t,i)&&(e.flags&128)===0)return ye=!1,V_(t,e,i);ye=(t.flags&131072)!==0}else ye=!1,vt&&(e.flags&1048576)!==0&&q(e,a,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var r=e.elementType,l=r._init;if(r=l(r._payload),e.type=r,typeof r=="function")jr(r)?(t=or(r,t),e.tag=1,e=Ld(null,e,r,t,i)):(e.tag=0,e=Ac(null,e,r,t,i));else{if(r!=null){if(l=r.$$typeof,l===xt){e.tag=11,e=bd(null,e,r,t,i);break t}else if(l===Nt){e.tag=14,e=xd(null,e,r,t,i);break t}}throw e=Se(r)||r,Error(h(306,e,""))}}return e;case 0:return Ac(t,e,e.type,e.pendingProps,i);case 1:return r=e.type,l=or(r,e.pendingProps),Ld(t,e,r,l,i);case 3:t:{if(zt(e,e.stateNode.containerInfo),t===null)throw Error(h(387));r=e.pendingProps;var f=e.memoizedState;l=f.element,ac(t,e),qo(e,r,null,i);var g=e.memoizedState;if(r=g.cache,Vn(e,he,r),r!==f.cache&&Pr(e,[he],i,!0),ko(),r=g.element,f.isDehydrated)if(f={element:r,isDehydrated:!1,cache:g.cache},e.updateQueue.baseState=f,e.memoizedState=f,e.flags&256){e=Od(t,e,r,i);break t}else if(r!==l){l=ke(Error(h(424)),e),ua(l),e=Od(t,e,r,i);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(ht=Dn(t.firstChild),ut=e,vt=!0,Ft=null,ne=!0,i=ud(e,null,r,i),e.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling}else{if(la(),r===l){e=Ti(t,e,i);break t}Oe(t,e,r,i)}e=e.child}return e;case 26:return zl(t,e),t===null?(i=Bm(e.type,null,e.pendingProps,null))?e.memoizedState=i:vt||(i=e.type,t=e.pendingProps,r=Kl(at.current).createElement(i),r[_e]=e,r[Ce]=t,Re(r,i,t),ue(r),e.stateNode=r):e.memoizedState=Bm(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return za(e),t===null&&vt&&(r=e.stateNode=Nm(e.type,e.pendingProps,at.current),ut=e,ne=!0,l=ht,Ea(e.type)?(_f=l,ht=Dn(r.firstChild)):ht=l),Oe(t,e,e.pendingProps.children,i),zl(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&vt&&((l=r=ht)&&(r=yg(r,e.type,e.pendingProps,ne),r!==null?(e.stateNode=r,ut=e,ht=Dn(r.firstChild),ne=!1,l=!0):l=!1),l||Ke(e)),za(e),l=e.type,f=e.pendingProps,g=t!==null?t.memoizedProps:null,r=f.children,hf(l,f)?r=null:g!==null&&hf(l,g)&&(e.flags|=32),e.memoizedState!==null&&(l=cc(t,e,U_,null,null,i),cs._currentValue=l),zl(t,e),Oe(t,e,r,i),e.child;case 6:return t===null&&vt&&((t=i=ht)&&(i=bg(i,e.pendingProps,ne),i!==null?(e.stateNode=i,ut=e,ht=null,t=!0):t=!1),t||Ke(e)),null;case 13:return Ad(t,e,i);case 4:return zt(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=Xr(e,null,r,i):Oe(t,e,r,i),e.child;case 11:return bd(t,e,e.type,e.pendingProps,i);case 7:return Oe(t,e,e.pendingProps,i),e.child;case 8:return Oe(t,e,e.pendingProps.children,i),e.child;case 12:return Oe(t,e,e.pendingProps.children,i),e.child;case 10:return r=e.pendingProps,Vn(e,e.type,r.value),Oe(t,e,r.children,i),e.child;case 9:return l=e.type._context,r=e.pendingProps.children,ir(e),l=De(l),r=r(l),e.flags|=1,Oe(t,e,r,i),e.child;case 14:return xd(t,e,e.type,e.pendingProps,i);case 15:return wd(t,e,e.type,e.pendingProps,i);case 19:return Md(t,e,i);case 31:return r=e.pendingProps,i=e.mode,r={mode:r.mode,children:r.children},t===null?(i=Dl(r,i),i.ref=e.ref,e.child=i,i.return=e,e=i):(i=yn(t.child,r),i.ref=e.ref,e.child=i,i.return=e,e=i),e;case 22:return Sd(t,e,i);case 24:return ir(e),r=De(he),t===null?(l=ec(),l===null&&(l=Wt,f=$u(),l.pooledCache=f,f.refCount++,f!==null&&(l.pooledCacheLanes|=i),l=f),e.memoizedState={parent:r,cache:l},ic(e),Vn(e,he,l)):((t.lanes&i)!==0&&(ac(t,e),qo(e,null,null,i),ko()),l=t.memoizedState,f=e.memoizedState,l.parent!==r?(l={parent:r,cache:r},e.memoizedState=l,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=l),Vn(e,he,r)):(r=f.cache,Vn(e,he,r),r!==l.cache&&Pr(e,[he],i,!0))),Oe(t,e,e.pendingProps.children,i),e.child;case 29:throw e.pendingProps}throw Error(h(156,e.tag))}function Li(t){t.flags|=4}function Nd(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Hm(e)){if(e=xn.current,e!==null&&((Mt&4194048)===Mt?Xn!==null:(Mt&62914560)!==Mt&&(Mt&536870912)===0||e!==Xn))throw Zo=nc,ph;t.flags|=8192}}function Bl(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?mo():536870912,t.lanes|=e,Fr|=e)}function Jo(t,e){if(!vt)switch(t.tailMode){case"hidden":e=t.tail;for(var i=null;e!==null;)e.alternate!==null&&(i=e),e=e.sibling;i===null?t.tail=null:i.sibling=null;break;case"collapsed":i=t.tail;for(var r=null;i!==null;)i.alternate!==null&&(r=i),i=i.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function ie(t){var e=t.alternate!==null&&t.alternate.child===t.child,i=0,r=0;if(e)for(var l=t.child;l!==null;)i|=l.lanes|l.childLanes,r|=l.subtreeFlags&65011712,r|=l.flags&65011712,l.return=t,l=l.sibling;else for(l=t.child;l!==null;)i|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=t,l=l.sibling;return t.subtreeFlags|=r,t.childLanes=i,e}function X_(t,e,i){var r=e.pendingProps;switch(ot(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ie(e),null;case 1:return ie(e),null;case 3:return i=e.stateNode,r=null,t!==null&&(r=t.memoizedState.cache),e.memoizedState.cache!==r&&(e.flags|=2048),Nn(he),En(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(t===null||t.child===null)&&(tr(e)?Li(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,dl())),ie(e),null;case 26:return i=e.memoizedState,t===null?(Li(e),i!==null?(ie(e),Nd(e,i)):(ie(e),e.flags&=-16777217)):i?i!==t.memoizedState?(Li(e),ie(e),Nd(e,i)):(ie(e),e.flags&=-16777217):(t.memoizedProps!==r&&Li(e),ie(e),e.flags&=-16777217),null;case 27:ji(e),i=at.current;var l=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==r&&Li(e);else{if(!r){if(e.stateNode===null)throw Error(h(166));return ie(e),null}t=st.current,tr(e)?fl(e):(t=Nm(l,r,i),e.stateNode=t,Li(e))}return ie(e),null;case 5:if(ji(e),i=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==r&&Li(e);else{if(!r){if(e.stateNode===null)throw Error(h(166));return ie(e),null}if(t=st.current,tr(e))fl(e);else{switch(l=Kl(at.current),t){case 1:t=l.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:t=l.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":t=l.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":t=l.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":t=l.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof r.is=="string"?l.createElement("select",{is:r.is}):l.createElement("select"),r.multiple?t.multiple=!0:r.size&&(t.size=r.size);break;default:t=typeof r.is=="string"?l.createElement(i,{is:r.is}):l.createElement(i)}}t[_e]=e,t[Ce]=r;t:for(l=e.child;l!==null;){if(l.tag===5||l.tag===6)t.appendChild(l.stateNode);else if(l.tag!==4&&l.tag!==27&&l.child!==null){l.child.return=l,l=l.child;continue}if(l===e)break t;for(;l.sibling===null;){if(l.return===null||l.return===e)break t;l=l.return}l.sibling.return=l.return,l=l.sibling}e.stateNode=t;t:switch(Re(t,i,r),i){case"button":case"input":case"select":case"textarea":t=!!r.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Li(e)}}return ie(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==r&&Li(e);else{if(typeof r!="string"&&e.stateNode===null)throw Error(h(166));if(t=at.current,tr(e)){if(t=e.stateNode,i=e.memoizedProps,r=null,l=ut,l!==null)switch(l.tag){case 27:case 5:r=l.memoizedProps}t[_e]=e,t=!!(t.nodeValue===i||r!==null&&r.suppressHydrationWarning===!0||Tm(t.nodeValue,i)),t||Ke(e)}else t=Kl(t).createTextNode(r),t[_e]=e,e.stateNode=t}return ie(e),null;case 13:if(r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(l=tr(e),r!==null&&r.dehydrated!==null){if(t===null){if(!l)throw Error(h(318));if(l=e.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(h(317));l[_e]=e}else la(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;ie(e),l=!1}else l=dl(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=l),l=!0;if(!l)return e.flags&256?(Ei(e),e):(Ei(e),null)}if(Ei(e),(e.flags&128)!==0)return e.lanes=i,e;if(i=r!==null,t=t!==null&&t.memoizedState!==null,i){r=e.child,l=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(l=r.alternate.memoizedState.cachePool.pool);var f=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(f=r.memoizedState.cachePool.pool),f!==l&&(r.flags|=2048)}return i!==t&&i&&(e.child.flags|=8192),Bl(e,e.updateQueue),ie(e),null;case 4:return En(),t===null&&sf(e.stateNode.containerInfo),ie(e),null;case 10:return Nn(e.type),ie(e),null;case 19:if(nt(de),l=e.memoizedState,l===null)return ie(e),null;if(r=(e.flags&128)!==0,f=l.rendering,f===null)if(r)Jo(l,!1);else{if(oe!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(f=Ml(t),f!==null){for(e.flags|=128,Jo(l,!1),t=f.updateQueue,e.updateQueue=t,Bl(e,t),e.subtreeFlags=0,t=i,i=e.child;i!==null;)Do(i,t),i=i.sibling;return tt(de,de.current&1|2),e.child}t=t.sibling}l.tail!==null&&Ve()>Pl&&(e.flags|=128,r=!0,Jo(l,!1),e.lanes=4194304)}else{if(!r)if(t=Ml(f),t!==null){if(e.flags|=128,r=!0,t=t.updateQueue,e.updateQueue=t,Bl(e,t),Jo(l,!0),l.tail===null&&l.tailMode==="hidden"&&!f.alternate&&!vt)return ie(e),null}else 2*Ve()-l.renderingStartTime>Pl&&i!==536870912&&(e.flags|=128,r=!0,Jo(l,!1),e.lanes=4194304);l.isBackwards?(f.sibling=e.child,e.child=f):(t=l.last,t!==null?t.sibling=f:e.child=f,l.last=f)}return l.tail!==null?(e=l.tail,l.rendering=e,l.tail=e.sibling,l.renderingStartTime=Ve(),e.sibling=null,t=de.current,tt(de,r?t&1|2:t&1),e):(ie(e),null);case 22:case 23:return Ei(e),lc(),r=e.memoizedState!==null,t!==null?t.memoizedState!==null!==r&&(e.flags|=8192):r&&(e.flags|=8192),r?(i&536870912)!==0&&(e.flags&128)===0&&(ie(e),e.subtreeFlags&6&&(e.flags|=8192)):ie(e),i=e.updateQueue,i!==null&&Bl(e,i.retryQueue),i=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),r=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(r=e.memoizedState.cachePool.pool),r!==i&&(e.flags|=2048),t!==null&&nt(ar),null;case 24:return i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),Nn(he),ie(e),null;case 25:return null;case 30:return null}throw Error(h(156,e.tag))}function Q_(t,e){switch(ot(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Nn(he),En(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return ji(e),null;case 13:if(Ei(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(h(340));la()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return nt(de),null;case 4:return En(),null;case 10:return Nn(e.type),null;case 22:case 23:return Ei(e),lc(),t!==null&&nt(ar),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Nn(he),null;case 25:return null;default:return null}}function zd(t,e){switch(ot(e),e.tag){case 3:Nn(he),En();break;case 26:case 27:case 5:ji(e);break;case 4:En();break;case 13:Ei(e);break;case 19:nt(de);break;case 10:Nn(e.type);break;case 22:case 23:Ei(e),lc(),t!==null&&nt(ar);break;case 24:Nn(he)}}function Fo(t,e){try{var i=e.updateQueue,r=i!==null?i.lastEffect:null;if(r!==null){var l=r.next;i=l;do{if((i.tag&t)===t){r=void 0;var f=i.create,g=i.inst;r=f(),g.destroy=r}i=i.next}while(i!==l)}}catch(x){Qt(e,e.return,x)}}function _a(t,e,i){try{var r=e.updateQueue,l=r!==null?r.lastEffect:null;if(l!==null){var f=l.next;r=f;do{if((r.tag&t)===t){var g=r.inst,x=g.destroy;if(x!==void 0){g.destroy=void 0,l=e;var R=i,Z=x;try{Z()}catch(K){Qt(l,R,K)}}}r=r.next}while(r!==f)}}catch(K){Qt(e,e.return,K)}}function Dd(t){var e=t.updateQueue;if(e!==null){var i=t.stateNode;try{xh(e,i)}catch(r){Qt(t,t.return,r)}}}function Bd(t,e,i){i.props=or(t.type,t.memoizedProps),i.state=t.memoizedState;try{i.componentWillUnmount()}catch(r){Qt(t,e,r)}}function Wo(t,e){try{var i=t.ref;if(i!==null){switch(t.tag){case 26:case 27:case 5:var r=t.stateNode;break;case 30:r=t.stateNode;break;default:r=t.stateNode}typeof i=="function"?t.refCleanup=i(r):i.current=r}}catch(l){Qt(t,e,l)}}function Qn(t,e){var i=t.ref,r=t.refCleanup;if(i!==null)if(typeof r=="function")try{r()}catch(l){Qt(t,e,l)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(l){Qt(t,e,l)}else i.current=null}function jd(t){var e=t.type,i=t.memoizedProps,r=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":i.autoFocus&&r.focus();break t;case"img":i.src?r.src=i.src:i.srcSet&&(r.srcset=i.srcSet)}}catch(l){Qt(t,t.return,l)}}function jc(t,e,i){try{var r=t.stateNode;mg(r,t.type,i,e),r[Ce]=e}catch(l){Qt(t,t.return,l)}}function Ud(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Ea(t.type)||t.tag===4}function Uc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Ud(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Ea(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Pc(t,e,i){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?(i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i).insertBefore(t,e):(e=i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i,e.appendChild(t),i=i._reactRootContainer,i!=null||e.onclick!==null||(e.onclick=Ql));else if(r!==4&&(r===27&&Ea(t.type)&&(i=t.stateNode,e=null),t=t.child,t!==null))for(Pc(t,e,i),t=t.sibling;t!==null;)Pc(t,e,i),t=t.sibling}function jl(t,e,i){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?i.insertBefore(t,e):i.appendChild(t);else if(r!==4&&(r===27&&Ea(t.type)&&(i=t.stateNode),t=t.child,t!==null))for(jl(t,e,i),t=t.sibling;t!==null;)jl(t,e,i),t=t.sibling}function Pd(t){var e=t.stateNode,i=t.memoizedProps;try{for(var r=t.type,l=e.attributes;l.length;)e.removeAttributeNode(l[0]);Re(e,r,i),e[_e]=t,e[Ce]=i}catch(f){Qt(t,t.return,f)}}var Oi=!1,le=!1,Zc=!1,Zd=typeof WeakSet=="function"?WeakSet:Set,be=null;function K_(t,e){if(t=t.containerInfo,cf=tu,t=Qa(t),Ka(t)){if("selectionStart"in t)var i={start:t.selectionStart,end:t.selectionEnd};else t:{i=(i=t.ownerDocument)&&i.defaultView||window;var r=i.getSelection&&i.getSelection();if(r&&r.rangeCount!==0){i=r.anchorNode;var l=r.anchorOffset,f=r.focusNode;r=r.focusOffset;try{i.nodeType,f.nodeType}catch{i=null;break t}var g=0,x=-1,R=-1,Z=0,K=0,W=t,H=null;e:for(;;){for(var k;W!==i||l!==0&&W.nodeType!==3||(x=g+l),W!==f||r!==0&&W.nodeType!==3||(R=g+r),W.nodeType===3&&(g+=W.nodeValue.length),(k=W.firstChild)!==null;)H=W,W=k;for(;;){if(W===t)break e;if(H===i&&++Z===l&&(x=g),H===f&&++K===r&&(R=g),(k=W.nextSibling)!==null)break;W=H,H=W.parentNode}W=k}i=x===-1||R===-1?null:{start:x,end:R}}else i=null}i=i||{start:0,end:0}}else i=null;for(ff={focusedElem:t,selectionRange:i},tu=!1,be=e;be!==null;)if(e=be,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,be=t;else for(;be!==null;){switch(e=be,f=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&f!==null){t=void 0,i=e,l=f.memoizedProps,f=f.memoizedState,r=i.stateNode;try{var gt=or(i.type,l,i.elementType===i.type);t=r.getSnapshotBeforeUpdate(gt,f),r.__reactInternalSnapshotBeforeUpdate=t}catch(pt){Qt(i,i.return,pt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,i=t.nodeType,i===9)mf(t);else if(i===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":mf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(h(163))}if(t=e.sibling,t!==null){t.return=e.return,be=t;break}be=e.return}}function Hd(t,e,i){var r=i.flags;switch(i.tag){case 0:case 11:case 15:ga(t,i),r&4&&Fo(5,i);break;case 1:if(ga(t,i),r&4)if(t=i.stateNode,e===null)try{t.componentDidMount()}catch(g){Qt(i,i.return,g)}else{var l=or(i.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(l,e,t.__reactInternalSnapshotBeforeUpdate)}catch(g){Qt(i,i.return,g)}}r&64&&Dd(i),r&512&&Wo(i,i.return);break;case 3:if(ga(t,i),r&64&&(t=i.updateQueue,t!==null)){if(e=null,i.child!==null)switch(i.child.tag){case 27:case 5:e=i.child.stateNode;break;case 1:e=i.child.stateNode}try{xh(t,e)}catch(g){Qt(i,i.return,g)}}break;case 27:e===null&&r&4&&Pd(i);case 26:case 5:ga(t,i),e===null&&r&4&&jd(i),r&512&&Wo(i,i.return);break;case 12:ga(t,i);break;case 13:ga(t,i),r&4&&Yd(t,i),r&64&&(t=i.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(i=ig.bind(null,i),xg(t,i))));break;case 22:if(r=i.memoizedState!==null||Oi,!r){e=e!==null&&e.memoizedState!==null||le,l=Oi;var f=le;Oi=r,(le=e)&&!f?va(t,i,(i.subtreeFlags&8772)!==0):ga(t,i),Oi=l,le=f}break;case 30:break;default:ga(t,i)}}function kd(t){var e=t.alternate;e!==null&&(t.alternate=null,kd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&yr(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var $t=null,Fe=!1;function Ai(t,e,i){for(i=i.child;i!==null;)qd(t,e,i),i=i.sibling}function qd(t,e,i){if(Me&&typeof Me.onCommitFiberUnmount=="function")try{Me.onCommitFiberUnmount(ni,i)}catch{}switch(i.tag){case 26:le||Qn(i,e),Ai(t,e,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:le||Qn(i,e);var r=$t,l=Fe;Ea(i.type)&&($t=i.stateNode,Fe=!1),Ai(t,e,i),os(i.stateNode),$t=r,Fe=l;break;case 5:le||Qn(i,e);case 6:if(r=$t,l=Fe,$t=null,Ai(t,e,i),$t=r,Fe=l,$t!==null)if(Fe)try{($t.nodeType===9?$t.body:$t.nodeName==="HTML"?$t.ownerDocument.body:$t).removeChild(i.stateNode)}catch(f){Qt(i,e,f)}else try{$t.removeChild(i.stateNode)}catch(f){Qt(i,e,f)}break;case 18:$t!==null&&(Fe?(t=$t,Mm(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,i.stateNode),ms(t)):Mm($t,i.stateNode));break;case 4:r=$t,l=Fe,$t=i.stateNode.containerInfo,Fe=!0,Ai(t,e,i),$t=r,Fe=l;break;case 0:case 11:case 14:case 15:le||_a(2,i,e),le||_a(4,i,e),Ai(t,e,i);break;case 1:le||(Qn(i,e),r=i.stateNode,typeof r.componentWillUnmount=="function"&&Bd(i,e,r)),Ai(t,e,i);break;case 21:Ai(t,e,i);break;case 22:le=(r=le)||i.memoizedState!==null,Ai(t,e,i),le=r;break;default:Ai(t,e,i)}}function Yd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{ms(t)}catch(i){Qt(e,e.return,i)}}function J_(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Zd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Zd),e;default:throw Error(h(435,t.tag))}}function Hc(t,e){var i=J_(t);e.forEach(function(r){var l=ag.bind(null,t,r);i.has(r)||(i.add(r),r.then(l,l))})}function ln(t,e){var i=e.deletions;if(i!==null)for(var r=0;r<i.length;r++){var l=i[r],f=t,g=e,x=g;t:for(;x!==null;){switch(x.tag){case 27:if(Ea(x.type)){$t=x.stateNode,Fe=!1;break t}break;case 5:$t=x.stateNode,Fe=!1;break t;case 3:case 4:$t=x.stateNode.containerInfo,Fe=!0;break t}x=x.return}if($t===null)throw Error(h(160));qd(f,g,l),$t=null,Fe=!1,f=l.alternate,f!==null&&(f.return=null),l.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Gd(e,t),e=e.sibling}var zn=null;function Gd(t,e){var i=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ln(e,t),un(t),r&4&&(_a(3,t,t.return),Fo(3,t),_a(5,t,t.return));break;case 1:ln(e,t),un(t),r&512&&(le||i===null||Qn(i,i.return)),r&64&&Oi&&(t=t.updateQueue,t!==null&&(r=t.callbacks,r!==null&&(i=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=i===null?r:i.concat(r))));break;case 26:var l=zn;if(ln(e,t),un(t),r&512&&(le||i===null||Qn(i,i.return)),r&4){var f=i!==null?i.memoizedState:null;if(r=t.memoizedState,i===null)if(r===null)if(t.stateNode===null){t:{r=t.type,i=t.memoizedProps,l=l.ownerDocument||l;e:switch(r){case"title":f=l.getElementsByTagName("title")[0],(!f||f[ki]||f[_e]||f.namespaceURI==="http://www.w3.org/2000/svg"||f.hasAttribute("itemprop"))&&(f=l.createElement(r),l.head.insertBefore(f,l.querySelector("head > title"))),Re(f,r,i),f[_e]=t,ue(f),r=f;break t;case"link":var g=Pm("link","href",l).get(r+(i.href||""));if(g){for(var x=0;x<g.length;x++)if(f=g[x],f.getAttribute("href")===(i.href==null||i.href===""?null:i.href)&&f.getAttribute("rel")===(i.rel==null?null:i.rel)&&f.getAttribute("title")===(i.title==null?null:i.title)&&f.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){g.splice(x,1);break e}}f=l.createElement(r),Re(f,r,i),l.head.appendChild(f);break;case"meta":if(g=Pm("meta","content",l).get(r+(i.content||""))){for(x=0;x<g.length;x++)if(f=g[x],f.getAttribute("content")===(i.content==null?null:""+i.content)&&f.getAttribute("name")===(i.name==null?null:i.name)&&f.getAttribute("property")===(i.property==null?null:i.property)&&f.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&f.getAttribute("charset")===(i.charSet==null?null:i.charSet)){g.splice(x,1);break e}}f=l.createElement(r),Re(f,r,i),l.head.appendChild(f);break;default:throw Error(h(468,r))}f[_e]=t,ue(f),r=f}t.stateNode=r}else Zm(l,t.type,t.stateNode);else t.stateNode=Um(l,r,t.memoizedProps);else f!==r?(f===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):f.count--,r===null?Zm(l,t.type,t.stateNode):Um(l,r,t.memoizedProps)):r===null&&t.stateNode!==null&&jc(t,t.memoizedProps,i.memoizedProps)}break;case 27:ln(e,t),un(t),r&512&&(le||i===null||Qn(i,i.return)),i!==null&&r&4&&jc(t,t.memoizedProps,i.memoizedProps);break;case 5:if(ln(e,t),un(t),r&512&&(le||i===null||Qn(i,i.return)),t.flags&32){l=t.stateNode;try{mn(l,"")}catch(k){Qt(t,t.return,k)}}r&4&&t.stateNode!=null&&(l=t.memoizedProps,jc(t,l,i!==null?i.memoizedProps:l)),r&1024&&(Zc=!0);break;case 6:if(ln(e,t),un(t),r&4){if(t.stateNode===null)throw Error(h(162));r=t.memoizedProps,i=t.stateNode;try{i.nodeValue=r}catch(k){Qt(t,t.return,k)}}break;case 3:if(Wl=null,l=zn,zn=Jl(e.containerInfo),ln(e,t),zn=l,un(t),r&4&&i!==null&&i.memoizedState.isDehydrated)try{ms(e.containerInfo)}catch(k){Qt(t,t.return,k)}Zc&&(Zc=!1,Vd(t));break;case 4:r=zn,zn=Jl(t.stateNode.containerInfo),ln(e,t),un(t),zn=r;break;case 12:ln(e,t),un(t);break;case 13:ln(e,t),un(t),t.child.flags&8192&&t.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(Xc=Ve()),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,Hc(t,r)));break;case 22:l=t.memoizedState!==null;var R=i!==null&&i.memoizedState!==null,Z=Oi,K=le;if(Oi=Z||l,le=K||R,ln(e,t),le=K,Oi=Z,un(t),r&8192)t:for(e=t.stateNode,e._visibility=l?e._visibility&-2:e._visibility|1,l&&(i===null||R||Oi||le||sr(t)),i=null,e=t;;){if(e.tag===5||e.tag===26){if(i===null){R=i=e;try{if(f=R.stateNode,l)g=f.style,typeof g.setProperty=="function"?g.setProperty("display","none","important"):g.display="none";else{x=R.stateNode;var W=R.memoizedProps.style,H=W!=null&&W.hasOwnProperty("display")?W.display:null;x.style.display=H==null||typeof H=="boolean"?"":(""+H).trim()}}catch(k){Qt(R,R.return,k)}}}else if(e.tag===6){if(i===null){R=e;try{R.stateNode.nodeValue=l?"":R.memoizedProps}catch(k){Qt(R,R.return,k)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;i===e&&(i=null),e=e.return}i===e&&(i=null),e.sibling.return=e.return,e=e.sibling}r&4&&(r=t.updateQueue,r!==null&&(i=r.retryQueue,i!==null&&(r.retryQueue=null,Hc(t,i))));break;case 19:ln(e,t),un(t),r&4&&(r=t.updateQueue,r!==null&&(t.updateQueue=null,Hc(t,r)));break;case 30:break;case 21:break;default:ln(e,t),un(t)}}function un(t){var e=t.flags;if(e&2){try{for(var i,r=t.return;r!==null;){if(Ud(r)){i=r;break}r=r.return}if(i==null)throw Error(h(160));switch(i.tag){case 27:var l=i.stateNode,f=Uc(t);jl(t,f,l);break;case 5:var g=i.stateNode;i.flags&32&&(mn(g,""),i.flags&=-33);var x=Uc(t);jl(t,x,g);break;case 3:case 4:var R=i.stateNode.containerInfo,Z=Uc(t);Pc(t,Z,R);break;default:throw Error(h(161))}}catch(K){Qt(t,t.return,K)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Vd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Vd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ga(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Hd(t,e.alternate,e),e=e.sibling}function sr(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:_a(4,e,e.return),sr(e);break;case 1:Qn(e,e.return);var i=e.stateNode;typeof i.componentWillUnmount=="function"&&Bd(e,e.return,i),sr(e);break;case 27:os(e.stateNode);case 26:case 5:Qn(e,e.return),sr(e);break;case 22:e.memoizedState===null&&sr(e);break;case 30:sr(e);break;default:sr(e)}t=t.sibling}}function va(t,e,i){for(i=i&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var r=e.alternate,l=t,f=e,g=f.flags;switch(f.tag){case 0:case 11:case 15:va(l,f,i),Fo(4,f);break;case 1:if(va(l,f,i),r=f,l=r.stateNode,typeof l.componentDidMount=="function")try{l.componentDidMount()}catch(Z){Qt(r,r.return,Z)}if(r=f,l=r.updateQueue,l!==null){var x=r.stateNode;try{var R=l.shared.hiddenCallbacks;if(R!==null)for(l.shared.hiddenCallbacks=null,l=0;l<R.length;l++)bh(R[l],x)}catch(Z){Qt(r,r.return,Z)}}i&&g&64&&Dd(f),Wo(f,f.return);break;case 27:Pd(f);case 26:case 5:va(l,f,i),i&&r===null&&g&4&&jd(f),Wo(f,f.return);break;case 12:va(l,f,i);break;case 13:va(l,f,i),i&&g&4&&Yd(l,f);break;case 22:f.memoizedState===null&&va(l,f,i),Wo(f,f.return);break;case 30:break;default:va(l,f,i)}e=e.sibling}}function kc(t,e){var i=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==i&&(t!=null&&t.refCount++,i!=null&&jo(i))}function qc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&jo(t))}function Kn(t,e,i,r){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Xd(t,e,i,r),e=e.sibling}function Xd(t,e,i,r){var l=e.flags;switch(e.tag){case 0:case 11:case 15:Kn(t,e,i,r),l&2048&&Fo(9,e);break;case 1:Kn(t,e,i,r);break;case 3:Kn(t,e,i,r),l&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&jo(t)));break;case 12:if(l&2048){Kn(t,e,i,r),t=e.stateNode;try{var f=e.memoizedProps,g=f.id,x=f.onPostCommit;typeof x=="function"&&x(g,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(R){Qt(e,e.return,R)}}else Kn(t,e,i,r);break;case 13:Kn(t,e,i,r);break;case 23:break;case 22:f=e.stateNode,g=e.alternate,e.memoizedState!==null?f._visibility&2?Kn(t,e,i,r):Io(t,e):f._visibility&2?Kn(t,e,i,r):(f._visibility|=2,Qr(t,e,i,r,(e.subtreeFlags&10256)!==0)),l&2048&&kc(g,e);break;case 24:Kn(t,e,i,r),l&2048&&qc(e.alternate,e);break;default:Kn(t,e,i,r)}}function Qr(t,e,i,r,l){for(l=l&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var f=t,g=e,x=i,R=r,Z=g.flags;switch(g.tag){case 0:case 11:case 15:Qr(f,g,x,R,l),Fo(8,g);break;case 23:break;case 22:var K=g.stateNode;g.memoizedState!==null?K._visibility&2?Qr(f,g,x,R,l):Io(f,g):(K._visibility|=2,Qr(f,g,x,R,l)),l&&Z&2048&&kc(g.alternate,g);break;case 24:Qr(f,g,x,R,l),l&&Z&2048&&qc(g.alternate,g);break;default:Qr(f,g,x,R,l)}e=e.sibling}}function Io(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var i=t,r=e,l=r.flags;switch(r.tag){case 22:Io(i,r),l&2048&&kc(r.alternate,r);break;case 24:Io(i,r),l&2048&&qc(r.alternate,r);break;default:Io(i,r)}e=e.sibling}}var $o=8192;function Kr(t){if(t.subtreeFlags&$o)for(t=t.child;t!==null;)Qd(t),t=t.sibling}function Qd(t){switch(t.tag){case 26:Kr(t),t.flags&$o&&t.memoizedState!==null&&Dg(zn,t.memoizedState,t.memoizedProps);break;case 5:Kr(t);break;case 3:case 4:var e=zn;zn=Jl(t.stateNode.containerInfo),Kr(t),zn=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=$o,$o=16777216,Kr(t),$o=e):Kr(t));break;default:Kr(t)}}function Kd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function ts(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var r=e[i];be=r,Fd(r,t)}Kd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Jd(t),t=t.sibling}function Jd(t){switch(t.tag){case 0:case 11:case 15:ts(t),t.flags&2048&&_a(9,t,t.return);break;case 3:ts(t);break;case 12:ts(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Ul(t)):ts(t);break;default:ts(t)}}function Ul(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var i=0;i<e.length;i++){var r=e[i];be=r,Fd(r,t)}Kd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:_a(8,e,e.return),Ul(e);break;case 22:i=e.stateNode,i._visibility&2&&(i._visibility&=-3,Ul(e));break;default:Ul(e)}t=t.sibling}}function Fd(t,e){for(;be!==null;){var i=be;switch(i.tag){case 0:case 11:case 15:_a(8,i,e);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var r=i.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:jo(i.memoizedState.cache)}if(r=i.child,r!==null)r.return=i,be=r;else t:for(i=t;be!==null;){r=be;var l=r.sibling,f=r.return;if(kd(r),r===i){be=null;break t}if(l!==null){l.return=f,be=l;break t}be=f}}}var F_={getCacheForType:function(t){var e=De(he),i=e.data.get(t);return i===void 0&&(i=t(),e.data.set(t,i)),i}},W_=typeof WeakMap=="function"?WeakMap:Map,Ht=0,Wt=null,Ot=null,Mt=0,kt=0,cn=null,ya=!1,Jr=!1,Yc=!1,Ri=0,oe=0,ba=0,lr=0,Gc=0,wn=0,Fr=0,es=null,We=null,Vc=!1,Xc=0,Pl=1/0,Zl=null,xa=null,Ae=0,wa=null,Wr=null,Ir=0,Qc=0,Kc=null,Wd=null,ns=0,Jc=null;function fn(){if((Ht&2)!==0&&Mt!==0)return Mt&-Mt;if(V.T!==null){var t=Zr;return t!==0?t:nf()}return Hs()}function Id(){wn===0&&(wn=(Mt&536870912)===0||vt?Us():536870912);var t=xn.current;return t!==null&&(t.flags|=32),wn}function hn(t,e,i){(t===Wt&&(kt===2||kt===9)||t.cancelPendingCommit!==null)&&($r(t,0),Sa(t,Mt,wn,!1)),Zi(t,i),((Ht&2)===0||t!==Wt)&&(t===Wt&&((Ht&2)===0&&(lr|=i),oe===4&&Sa(t,Mt,wn,!1)),Jn(t))}function $d(t,e,i){if((Ht&6)!==0)throw Error(h(327));var r=!i&&(e&124)===0&&(e&t.expiredLanes)===0||Ln(t,e),l=r?tg(t,e):Ic(t,e,!0),f=r;do{if(l===0){Jr&&!r&&Sa(t,e,0,!1);break}else{if(i=t.current.alternate,f&&!I_(i)){l=Ic(t,e,!1),f=!1;continue}if(l===2){if(f=e,t.errorRecoveryDisabledLanes&f)var g=0;else g=t.pendingLanes&-536870913,g=g!==0?g:g&536870912?536870912:0;if(g!==0){e=g;t:{var x=t;l=es;var R=x.current.memoizedState.isDehydrated;if(R&&($r(x,g).flags|=256),g=Ic(x,g,!1),g!==2){if(Yc&&!R){x.errorRecoveryDisabledLanes|=f,lr|=f,l=4;break t}f=We,We=l,f!==null&&(We===null?We=f:We.push.apply(We,f))}l=g}if(f=!1,l!==2)continue}}if(l===1){$r(t,0),Sa(t,e,0,!0);break}t:{switch(r=t,f=l,f){case 0:case 1:throw Error(h(345));case 4:if((e&4194048)!==e)break;case 6:Sa(r,e,wn,!ya);break t;case 2:We=null;break;case 3:case 5:break;default:throw Error(h(329))}if((e&62914560)===e&&(l=Xc+300-Ve(),10<l)){if(Sa(r,e,wn,!ya),gr(r,0,!0)!==0)break t;r.timeoutHandle=Am(tm.bind(null,r,i,We,Zl,Vc,e,wn,lr,Fr,ya,f,2,-0,0),l);break t}tm(r,i,We,Zl,Vc,e,wn,lr,Fr,ya,f,0,-0,0)}}break}while(!0);Jn(t)}function tm(t,e,i,r,l,f,g,x,R,Z,K,W,H,k){if(t.timeoutHandle=-1,W=e.subtreeFlags,(W&8192||(W&16785408)===16785408)&&(us={stylesheets:null,count:0,unsuspend:zg},Qd(e),W=Bg(),W!==null)){t.cancelPendingCommit=W(sm.bind(null,t,e,f,i,r,l,g,x,R,K,1,H,k)),Sa(t,f,g,!Z);return}sm(t,e,f,i,r,l,g,x,R)}function I_(t){for(var e=t;;){var i=e.tag;if((i===0||i===11||i===15)&&e.flags&16384&&(i=e.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var r=0;r<i.length;r++){var l=i[r],f=l.getSnapshot;l=l.value;try{if(!Ze(f(),l))return!1}catch{return!1}}if(i=e.child,e.subtreeFlags&16384&&i!==null)i.return=e,e=i;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Sa(t,e,i,r){e&=~Gc,e&=~lr,t.suspendedLanes|=e,t.pingedLanes&=~e,r&&(t.warmLanes|=e),r=t.expirationTimes;for(var l=e;0<l;){var f=31-Ue(l),g=1<<f;r[f]=-1,l&=~g}i!==0&&Ps(t,i,e)}function Hl(){return(Ht&6)===0?(is(0),!1):!0}function Fc(){if(Ot!==null){if(kt===0)var t=Ot.return;else t=Ot,Cn=bn=null,dc(t),Vr=null,Qo=0,t=Ot;for(;t!==null;)zd(t.alternate,t),t=t.return;Ot=null}}function $r(t,e){var i=t.timeoutHandle;i!==-1&&(t.timeoutHandle=-1,_g(i)),i=t.cancelPendingCommit,i!==null&&(t.cancelPendingCommit=null,i()),Fc(),Wt=t,Ot=i=yn(t.current,null),Mt=e,kt=0,cn=null,ya=!1,Jr=Ln(t,e),Yc=!1,Fr=wn=Gc=lr=ba=oe=0,We=es=null,Vc=!1,(e&8)!==0&&(e|=e&32);var r=t.entangledLanes;if(r!==0)for(t=t.entanglements,r&=e;0<r;){var l=31-Ue(r),f=1<<l;e|=t[l],r&=~f}return Ri=e,Fa(),i}function em(t,e){Et=null,V.H=Ol,e===Po||e===gl?(e=vh(),kt=3):e===ph?(e=vh(),kt=4):kt=e===yd?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,cn=e,Ot===null&&(oe=1,Nl(t,ke(e,t.current)))}function nm(){var t=V.H;return V.H=Ol,t===null?Ol:t}function im(){var t=V.A;return V.A=F_,t}function Wc(){oe=4,ya||(Mt&4194048)!==Mt&&xn.current!==null||(Jr=!0),(ba&134217727)===0&&(lr&134217727)===0||Wt===null||Sa(Wt,Mt,wn,!1)}function Ic(t,e,i){var r=Ht;Ht|=2;var l=nm(),f=im();(Wt!==t||Mt!==e)&&(Zl=null,$r(t,e)),e=!1;var g=oe;t:do try{if(kt!==0&&Ot!==null){var x=Ot,R=cn;switch(kt){case 8:Fc(),g=6;break t;case 3:case 2:case 9:case 6:xn.current===null&&(e=!0);var Z=kt;if(kt=0,cn=null,to(t,x,R,Z),i&&Jr){g=0;break t}break;default:Z=kt,kt=0,cn=null,to(t,x,R,Z)}}$_(),g=oe;break}catch(K){em(t,K)}while(!0);return e&&t.shellSuspendCounter++,Cn=bn=null,Ht=r,V.H=l,V.A=f,Ot===null&&(Wt=null,Mt=0,Fa()),g}function $_(){for(;Ot!==null;)am(Ot)}function tg(t,e){var i=Ht;Ht|=2;var r=nm(),l=im();Wt!==t||Mt!==e?(Zl=null,Pl=Ve()+500,$r(t,e)):Jr=Ln(t,e);t:do try{if(kt!==0&&Ot!==null){e=Ot;var f=cn;e:switch(kt){case 1:kt=0,cn=null,to(t,e,f,1);break;case 2:case 9:if(_h(f)){kt=0,cn=null,rm(e);break}e=function(){kt!==2&&kt!==9||Wt!==t||(kt=7),Jn(t)},f.then(e,e);break t;case 3:kt=7;break t;case 4:kt=5;break t;case 7:_h(f)?(kt=0,cn=null,rm(e)):(kt=0,cn=null,to(t,e,f,7));break;case 5:var g=null;switch(Ot.tag){case 26:g=Ot.memoizedState;case 5:case 27:var x=Ot;if(!g||Hm(g)){kt=0,cn=null;var R=x.sibling;if(R!==null)Ot=R;else{var Z=x.return;Z!==null?(Ot=Z,kl(Z)):Ot=null}break e}}kt=0,cn=null,to(t,e,f,5);break;case 6:kt=0,cn=null,to(t,e,f,6);break;case 8:Fc(),oe=6;break t;default:throw Error(h(462))}}eg();break}catch(K){em(t,K)}while(!0);return Cn=bn=null,V.H=r,V.A=l,Ht=i,Ot!==null?0:(Wt=null,Mt=0,Fa(),oe)}function eg(){for(;Ot!==null&&!Cs();)am(Ot)}function am(t){var e=Cd(t.alternate,t,Ri);t.memoizedProps=t.pendingProps,e===null?kl(t):Ot=e}function rm(t){var e=t,i=e.alternate;switch(e.tag){case 15:case 0:e=Td(i,e,e.pendingProps,e.type,void 0,Mt);break;case 11:e=Td(i,e,e.pendingProps,e.type.render,e.ref,Mt);break;case 5:dc(e);default:zd(i,e),e=Ot=Do(e,Ri),e=Cd(i,e,Ri)}t.memoizedProps=t.pendingProps,e===null?kl(t):Ot=e}function to(t,e,i,r){Cn=bn=null,dc(e),Vr=null,Qo=0;var l=e.return;try{if(G_(t,l,e,i,Mt)){oe=1,Nl(t,ke(i,t.current)),Ot=null;return}}catch(f){if(l!==null)throw Ot=l,f;oe=1,Nl(t,ke(i,t.current)),Ot=null;return}e.flags&32768?(vt||r===1?t=!0:Jr||(Mt&536870912)!==0?t=!1:(ya=t=!0,(r===2||r===9||r===3||r===6)&&(r=xn.current,r!==null&&r.tag===13&&(r.flags|=16384))),om(e,t)):kl(e)}function kl(t){var e=t;do{if((e.flags&32768)!==0){om(e,ya);return}t=e.return;var i=X_(e.alternate,e,Ri);if(i!==null){Ot=i;return}if(e=e.sibling,e!==null){Ot=e;return}Ot=e=t}while(e!==null);oe===0&&(oe=5)}function om(t,e){do{var i=Q_(t.alternate,t);if(i!==null){i.flags&=32767,Ot=i;return}if(i=t.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!e&&(t=t.sibling,t!==null)){Ot=t;return}Ot=t=i}while(t!==null);oe=6,Ot=null}function sm(t,e,i,r,l,f,g,x,R){t.cancelPendingCommit=null;do ql();while(Ae!==0);if((Ht&6)!==0)throw Error(h(327));if(e!==null){if(e===t.current)throw Error(h(177));if(f=e.lanes|e.childLanes,f|=sn,Du(t,i,f,g,x,R),t===Wt&&(Ot=Wt=null,Mt=0),Wr=e,wa=t,Ir=i,Qc=f,Kc=l,Wd=r,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,rg(Ui,function(){return hm(),null})):(t.callbackNode=null,t.callbackPriority=0),r=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||r){r=V.T,V.T=null,l=rt.p,rt.p=2,g=Ht,Ht|=4;try{K_(t,e,i)}finally{Ht=g,rt.p=l,V.T=r}}Ae=1,lm(),um(),cm()}}function lm(){if(Ae===1){Ae=0;var t=wa,e=Wr,i=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||i){i=V.T,V.T=null;var r=rt.p;rt.p=2;var l=Ht;Ht|=4;try{Gd(e,t);var f=ff,g=Qa(t.containerInfo),x=f.focusedElem,R=f.selectionRange;if(g!==x&&x&&x.ownerDocument&&Cr(x.ownerDocument.documentElement,x)){if(R!==null&&Ka(x)){var Z=R.start,K=R.end;if(K===void 0&&(K=Z),"selectionStart"in x)x.selectionStart=Z,x.selectionEnd=Math.min(K,x.value.length);else{var W=x.ownerDocument||document,H=W&&W.defaultView||window;if(H.getSelection){var k=H.getSelection(),gt=x.textContent.length,pt=Math.min(R.start,gt),Vt=R.end===void 0?pt:Math.min(R.end,gt);!k.extend&&pt>Vt&&(g=Vt,Vt=pt,pt=g);var D=No(x,pt),C=No(x,Vt);if(D&&C&&(k.rangeCount!==1||k.anchorNode!==D.node||k.anchorOffset!==D.offset||k.focusNode!==C.node||k.focusOffset!==C.offset)){var P=W.createRange();P.setStart(D.node,D.offset),k.removeAllRanges(),pt>Vt?(k.addRange(P),k.extend(C.node,C.offset)):(P.setEnd(C.node,C.offset),k.addRange(P))}}}}for(W=[],k=x;k=k.parentNode;)k.nodeType===1&&W.push({element:k,left:k.scrollLeft,top:k.scrollTop});for(typeof x.focus=="function"&&x.focus(),x=0;x<W.length;x++){var F=W[x];F.element.scrollLeft=F.left,F.element.scrollTop=F.top}}tu=!!cf,ff=cf=null}finally{Ht=l,rt.p=r,V.T=i}}t.current=e,Ae=2}}function um(){if(Ae===2){Ae=0;var t=wa,e=Wr,i=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||i){i=V.T,V.T=null;var r=rt.p;rt.p=2;var l=Ht;Ht|=4;try{Hd(t,e.alternate,e)}finally{Ht=l,rt.p=r,V.T=i}}Ae=3}}function cm(){if(Ae===4||Ae===3){Ae=0,Ns();var t=wa,e=Wr,i=Ir,r=Wd;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Ae=5:(Ae=0,Wr=wa=null,fm(t,t.pendingLanes));var l=t.pendingLanes;if(l===0&&(xa=null),_o(i),e=e.stateNode,Me&&typeof Me.onCommitFiberRoot=="function")try{Me.onCommitFiberRoot(ni,e,void 0,(e.current.flags&128)===128)}catch{}if(r!==null){e=V.T,l=rt.p,rt.p=2,V.T=null;try{for(var f=t.onRecoverableError,g=0;g<r.length;g++){var x=r[g];f(x.value,{componentStack:x.stack})}}finally{V.T=e,rt.p=l}}(Ir&3)!==0&&ql(),Jn(t),l=t.pendingLanes,(i&4194090)!==0&&(l&42)!==0?t===Jc?ns++:(ns=0,Jc=t):ns=0,is(0)}}function fm(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,jo(e)))}function ql(t){return lm(),um(),cm(),hm()}function hm(){if(Ae!==5)return!1;var t=wa,e=Qc;Qc=0;var i=_o(Ir),r=V.T,l=rt.p;try{rt.p=32>i?32:i,V.T=null,i=Kc,Kc=null;var f=wa,g=Ir;if(Ae=0,Wr=wa=null,Ir=0,(Ht&6)!==0)throw Error(h(331));var x=Ht;if(Ht|=4,Jd(f.current),Xd(f,f.current,g,i),Ht=x,is(0,!1),Me&&typeof Me.onPostCommitFiberRoot=="function")try{Me.onPostCommitFiberRoot(ni,f)}catch{}return!0}finally{rt.p=l,V.T=r,fm(t,e)}}function dm(t,e,i){e=ke(i,e),e=Oc(t.stateNode,e,2),t=ha(t,e,2),t!==null&&(Zi(t,2),Jn(t))}function Qt(t,e,i){if(t.tag===3)dm(t,t,i);else for(;e!==null;){if(e.tag===3){dm(e,t,i);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(xa===null||!xa.has(r))){t=ke(i,t),i=gd(2),r=ha(e,i,2),r!==null&&(vd(i,r,e,t),Zi(r,2),Jn(r));break}}e=e.return}}function $c(t,e,i){var r=t.pingCache;if(r===null){r=t.pingCache=new W_;var l=new Set;r.set(e,l)}else l=r.get(e),l===void 0&&(l=new Set,r.set(e,l));l.has(i)||(Yc=!0,l.add(i),t=ng.bind(null,t,e,i),e.then(t,t))}function ng(t,e,i){var r=t.pingCache;r!==null&&r.delete(e),t.pingedLanes|=t.suspendedLanes&i,t.warmLanes&=~i,Wt===t&&(Mt&i)===i&&(oe===4||oe===3&&(Mt&62914560)===Mt&&300>Ve()-Xc?(Ht&2)===0&&$r(t,0):Gc|=i,Fr===Mt&&(Fr=0)),Jn(t)}function mm(t,e){e===0&&(e=mo()),t=oa(t,e),t!==null&&(Zi(t,e),Jn(t))}function ig(t){var e=t.memoizedState,i=0;e!==null&&(i=e.retryLane),mm(t,i)}function ag(t,e){var i=0;switch(t.tag){case 13:var r=t.stateNode,l=t.memoizedState;l!==null&&(i=l.retryLane);break;case 19:r=t.stateNode;break;case 22:r=t.stateNode._retryCache;break;default:throw Error(h(314))}r!==null&&r.delete(e),mm(t,i)}function rg(t,e){return Da(t,e)}var Yl=null,eo=null,tf=!1,Gl=!1,ef=!1,ur=0;function Jn(t){t!==eo&&t.next===null&&(eo===null?Yl=eo=t:eo=eo.next=t),Gl=!0,tf||(tf=!0,sg())}function is(t,e){if(!ef&&Gl){ef=!0;do for(var i=!1,r=Yl;r!==null;){if(t!==0){var l=r.pendingLanes;if(l===0)var f=0;else{var g=r.suspendedLanes,x=r.pingedLanes;f=(1<<31-Ue(42|t)+1)-1,f&=l&~(g&~x),f=f&201326741?f&201326741|1:f?f|2:0}f!==0&&(i=!0,vm(r,f))}else f=Mt,f=gr(r,r===Wt?f:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(f&3)===0||Ln(r,f)||(i=!0,vm(r,f));r=r.next}while(i);ef=!1}}function og(){pm()}function pm(){Gl=tf=!1;var t=0;ur!==0&&(pg()&&(t=ur),ur=0);for(var e=Ve(),i=null,r=Yl;r!==null;){var l=r.next,f=_m(r,e);f===0?(r.next=null,i===null?Yl=l:i.next=l,l===null&&(eo=i)):(i=r,(t!==0||(f&3)!==0)&&(Gl=!0)),r=l}is(t)}function _m(t,e){for(var i=t.suspendedLanes,r=t.pingedLanes,l=t.expirationTimes,f=t.pendingLanes&-62914561;0<f;){var g=31-Ue(f),x=1<<g,R=l[g];R===-1?((x&i)===0||(x&r)!==0)&&(l[g]=zu(x,e)):R<=e&&(t.expiredLanes|=x),f&=~x}if(e=Wt,i=Mt,i=gr(t,t===e?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r=t.callbackNode,i===0||t===e&&(kt===2||kt===9)||t.cancelPendingCommit!==null)return r!==null&&r!==null&&co(r),t.callbackNode=null,t.callbackPriority=0;if((i&3)===0||Ln(t,i)){if(e=i&-i,e===t.callbackPriority)return e;switch(r!==null&&co(r),_o(i)){case 2:case 8:i=fo;break;case 32:i=Ui;break;case 268435456:i=ho;break;default:i=Ui}return r=gm.bind(null,t),i=Da(i,r),t.callbackPriority=e,t.callbackNode=i,e}return r!==null&&r!==null&&co(r),t.callbackPriority=2,t.callbackNode=null,2}function gm(t,e){if(Ae!==0&&Ae!==5)return t.callbackNode=null,t.callbackPriority=0,null;var i=t.callbackNode;if(ql()&&t.callbackNode!==i)return null;var r=Mt;return r=gr(t,t===Wt?r:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),r===0?null:($d(t,r,e),_m(t,Ve()),t.callbackNode!=null&&t.callbackNode===i?gm.bind(null,t):null)}function vm(t,e){if(ql())return null;$d(t,e,!0)}function sg(){gg(function(){(Ht&6)!==0?Da(zs,og):pm()})}function nf(){return ur===0&&(ur=Us()),ur}function ym(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Qi(""+t)}function bm(t,e){var i=e.ownerDocument.createElement("input");return i.name=e.name,i.value=e.value,t.id&&i.setAttribute("form",t.id),e.parentNode.insertBefore(i,e),t=new FormData(t),i.parentNode.removeChild(i),t}function lg(t,e,i,r,l){if(e==="submit"&&i&&i.stateNode===l){var f=ym((l[Ce]||null).action),g=r.submitter;g&&(e=(e=g[Ce]||null)?ym(e.formAction):g.getAttribute("formAction"),e!==null&&(f=e,g=null));var x=new qa("action","action",null,r,l);t.push({event:x,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(ur!==0){var R=g?bm(l,g):new FormData(l);wc(i,{pending:!0,data:R,method:l.method,action:f},null,R)}}else typeof f=="function"&&(x.preventDefault(),R=g?bm(l,g):new FormData(l),wc(i,{pending:!0,data:R,method:l.method,action:f},f,R))},currentTarget:l}]})}}for(var af=0;af<vi.length;af++){var rf=vi[af],ug=rf.toLowerCase(),cg=rf[0].toUpperCase()+rf.slice(1);on(ug,"on"+cg)}on(ll,"onAnimationEnd"),on(rn,"onAnimationIteration"),on(Ja,"onAnimationStart"),on("dblclick","onDoubleClick"),on("focusin","onFocus"),on("focusout","onBlur"),on(Wu,"onTransitionRun"),on(Br,"onTransitionStart"),on(Iu,"onTransitionCancel"),on(zo,"onTransitionEnd"),oi("onMouseEnter",["mouseout","mouseover"]),oi("onMouseLeave",["mouseout","mouseover"]),oi("onPointerEnter",["pointerout","pointerover"]),oi("onPointerLeave",["pointerout","pointerover"]),ri("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),ri("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),ri("onBeforeInput",["compositionend","keypress","textInput","paste"]),ri("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),ri("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),ri("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var as="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),fg=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(as));function xm(t,e){e=(e&4)!==0;for(var i=0;i<t.length;i++){var r=t[i],l=r.event;r=r.listeners;t:{var f=void 0;if(e)for(var g=r.length-1;0<=g;g--){var x=r[g],R=x.instance,Z=x.currentTarget;if(x=x.listener,R!==f&&l.isPropagationStopped())break t;f=x,l.currentTarget=Z;try{f(l)}catch(K){Cl(K)}l.currentTarget=null,f=R}else for(g=0;g<r.length;g++){if(x=r[g],R=x.instance,Z=x.currentTarget,x=x.listener,R!==f&&l.isPropagationStopped())break t;f=x,l.currentTarget=Z;try{f(l)}catch(K){Cl(K)}l.currentTarget=null,f=R}}}}function At(t,e){var i=e[Xe];i===void 0&&(i=e[Xe]=new Set);var r=t+"__bubble";i.has(r)||(wm(e,t,2,!1),i.add(r))}function of(t,e,i){var r=0;e&&(r|=4),wm(i,t,r,e)}var Vl="_reactListening"+Math.random().toString(36).slice(2);function sf(t){if(!t[Vl]){t[Vl]=!0,qs.forEach(function(i){i!=="selectionchange"&&(fg.has(i)||of(i,!1,t),of(i,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Vl]||(e[Vl]=!0,of("selectionchange",!1,e))}}function wm(t,e,i,r){switch(Xm(e)){case 2:var l=Pg;break;case 8:l=Zg;break;default:l=xf}i=l.bind(null,e,i,t),l=void 0,!ka||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(l=!0),r?l!==void 0?t.addEventListener(e,i,{capture:!0,passive:l}):t.addEventListener(e,i,!0):l!==void 0?t.addEventListener(e,i,{passive:l}):t.addEventListener(e,i,!1)}function lf(t,e,i,r,l){var f=r;if((e&1)===0&&(e&2)===0&&r!==null)t:for(;;){if(r===null)return;var g=r.tag;if(g===3||g===4){var x=r.stateNode.containerInfo;if(x===l)break;if(g===4)for(g=r.return;g!==null;){var R=g.tag;if((R===3||R===4)&&g.stateNode.containerInfo===l)return;g=g.return}for(;x!==null;){if(g=Pn(x),g===null)return;if(R=g.tag,R===5||R===6||R===26||R===27){r=f=g;continue t}x=x.parentNode}}r=r.return}tn(function(){var Z=f,K=Ki(i),W=[];t:{var H=ul.get(t);if(H!==void 0){var k=qa,gt=t;switch(t){case"keypress":if(re(i)===0)break t;case"keydown":case"keyup":k=Yu;break;case"focusin":gt="focus",k=So;break;case"focusout":gt="blur",k=So;break;case"beforeblur":case"afterblur":k=So;break;case"click":if(i.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":k=Wi;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":k=Pu;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":k=Vu;break;case ll:case rn:case Ja:k=Zu;break;case zo:k=Xu;break;case"scroll":case"scrollend":k=Uu;break;case"wheel":k=$s;break;case"copy":case"cut":case"paste":k=Eo;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":k=Lo;break;case"toggle":case"beforetoggle":k=mi}var pt=(e&4)!==0,Vt=!pt&&(t==="scroll"||t==="scrollend"),D=pt?H!==null?H+"Capture":null:H;pt=[];for(var C=Z,P;C!==null;){var F=C;if(P=F.stateNode,F=F.tag,F!==5&&F!==26&&F!==27||P===null||D===null||(F=Dt(C,D),F!=null&&pt.push(rs(C,F,P))),Vt)break;C=C.return}0<pt.length&&(H=new k(H,gt,null,i,K),W.push({event:H,listeners:pt}))}}if((e&7)===0){t:{if(H=t==="mouseover"||t==="pointerover",k=t==="mouseout"||t==="pointerout",H&&i!==Ha&&(gt=i.relatedTarget||i.fromElement)&&(Pn(gt)||gt[Hi]))break t;if((k||H)&&(H=K.window===K?K:(H=K.ownerDocument)?H.defaultView||H.parentWindow:window,k?(gt=i.relatedTarget||i.toElement,k=Z,gt=gt?Pn(gt):null,gt!==null&&(Vt=_(gt),pt=gt.tag,gt!==Vt||pt!==5&&pt!==27&&pt!==6)&&(gt=null)):(k=null,gt=Z),k!==gt)){if(pt=Wi,F="onMouseLeave",D="onMouseEnter",C="mouse",(t==="pointerout"||t==="pointerover")&&(pt=Lo,F="onPointerLeave",D="onPointerEnter",C="pointer"),Vt=k==null?H:dn(k),P=gt==null?H:dn(gt),H=new pt(F,C+"leave",k,i,K),H.target=Vt,H.relatedTarget=P,F=null,Pn(K)===Z&&(pt=new pt(D,C+"enter",gt,i,K),pt.target=P,pt.relatedTarget=Vt,F=pt),Vt=F,k&&gt)e:{for(pt=k,D=gt,C=0,P=pt;P;P=no(P))C++;for(P=0,F=D;F;F=no(F))P++;for(;0<C-P;)pt=no(pt),C--;for(;0<P-C;)D=no(D),P--;for(;C--;){if(pt===D||D!==null&&pt===D.alternate)break e;pt=no(pt),D=no(D)}pt=null}else pt=null;k!==null&&Sm(W,H,k,pt,!1),gt!==null&&Vt!==null&&Sm(W,Vt,gt,pt,!0)}}t:{if(H=Z?dn(Z):window,k=H.nodeName&&H.nodeName.toLowerCase(),k==="select"||k==="input"&&H.type==="file")var ct=ta;else if(_i(H))if(Ro)ct=Fu;else{ct=Ju;var Lt=Co}else k=H.nodeName,!k||k.toLowerCase()!=="input"||H.type!=="checkbox"&&H.type!=="radio"?Z&&Za(Z.elementType)&&(ct=ta):ct=Mn;if(ct&&(ct=ct(t,Z))){al(W,ct,i,K);break t}Lt&&Lt(t,H,Z),t==="focusout"&&Z&&H.type==="number"&&Z.memoizedProps.value!=null&&Vi(H,"number",H.value)}switch(Lt=Z?dn(Z):window,t){case"focusin":(_i(Lt)||Lt.contentEditable==="true")&&(an=Lt,ia=Z,gi=null);break;case"focusout":gi=ia=an=null;break;case"mousedown":zr=!0;break;case"contextmenu":case"mouseup":case"dragend":zr=!1,ol(W,i,K);break;case"selectionchange":if(Nr)break;case"keydown":case"keyup":ol(W,i,K)}var mt;if(pi)t:{switch(t){case"compositionstart":var _t="onCompositionStart";break t;case"compositionend":_t="onCompositionEnd";break t;case"compositionupdate":_t="onCompositionUpdate";break t}_t=void 0}else Ii?Rr(t,i)&&(_t="onCompositionEnd"):t==="keydown"&&i.keyCode===229&&(_t="onCompositionStart");_t&&(Ao&&i.locale!=="ko"&&(Ii||_t!=="onCompositionStart"?_t==="onCompositionEnd"&&Ii&&(mt=Fi()):(_n=K,An="value"in _n?_n.value:_n.textContent,Ii=!0)),Lt=Xl(Z,_t),0<Lt.length&&(_t=new en(_t,t,null,i,K),W.push({event:_t,listeners:Lt}),mt?_t.data=mt:(mt=nl(i),mt!==null&&(_t.data=mt)))),(mt=tl?il(t,i):Ku(t,i))&&(_t=Xl(Z,"onBeforeInput"),0<_t.length&&(Lt=new en("onBeforeInput","beforeinput",null,i,K),W.push({event:Lt,listeners:_t}),Lt.data=mt)),lg(W,t,Z,i,K)}xm(W,e)})}function rs(t,e,i){return{instance:t,listener:e,currentTarget:i}}function Xl(t,e){for(var i=e+"Capture",r=[];t!==null;){var l=t,f=l.stateNode;if(l=l.tag,l!==5&&l!==26&&l!==27||f===null||(l=Dt(t,i),l!=null&&r.unshift(rs(t,l,f)),l=Dt(t,e),l!=null&&r.push(rs(t,l,f))),t.tag===3)return r;t=t.return}return[]}function no(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Sm(t,e,i,r,l){for(var f=e._reactName,g=[];i!==null&&i!==r;){var x=i,R=x.alternate,Z=x.stateNode;if(x=x.tag,R!==null&&R===r)break;x!==5&&x!==26&&x!==27||Z===null||(R=Z,l?(Z=Dt(i,f),Z!=null&&g.unshift(rs(i,Z,R))):l||(Z=Dt(i,f),Z!=null&&g.push(rs(i,Z,R)))),i=i.return}g.length!==0&&t.push({event:e,listeners:g})}var hg=/\r\n?/g,dg=/\u0000|\uFFFD/g;function Em(t){return(typeof t=="string"?t:""+t).replace(hg,`
`).replace(dg,"")}function Tm(t,e){return e=Em(e),Em(t)===e}function Ql(){}function Gt(t,e,i,r,l,f){switch(i){case"children":typeof r=="string"?e==="body"||e==="textarea"&&r===""||mn(t,r):(typeof r=="number"||typeof r=="bigint")&&e!=="body"&&mn(t,""+r);break;case"className":xr(t,"class",r);break;case"tabIndex":xr(t,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":xr(t,i,r);break;case"style":Xi(t,r,f);break;case"data":if(e!=="object"){xr(t,"data",r);break}case"src":case"href":if(r===""&&(e!=="a"||i!=="href")){t.removeAttribute(i);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(i);break}r=Qi(""+r),t.setAttribute(i,r);break;case"action":case"formAction":if(typeof r=="function"){t.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof f=="function"&&(i==="formAction"?(e!=="input"&&Gt(t,e,"name",l.name,l,null),Gt(t,e,"formEncType",l.formEncType,l,null),Gt(t,e,"formMethod",l.formMethod,l,null),Gt(t,e,"formTarget",l.formTarget,l,null)):(Gt(t,e,"encType",l.encType,l,null),Gt(t,e,"method",l.method,l,null),Gt(t,e,"target",l.target,l,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){t.removeAttribute(i);break}r=Qi(""+r),t.setAttribute(i,r);break;case"onClick":r!=null&&(t.onclick=Ql);break;case"onScroll":r!=null&&At("scroll",t);break;case"onScrollEnd":r!=null&&At("scrollend",t);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(h(61));if(i=r.__html,i!=null){if(l.children!=null)throw Error(h(60));t.innerHTML=i}}break;case"multiple":t.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":t.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){t.removeAttribute("xlink:href");break}i=Qi(""+r),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,""+r):t.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,""):t.removeAttribute(i);break;case"capture":case"download":r===!0?t.setAttribute(i,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?t.setAttribute(i,r):t.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?t.setAttribute(i,r):t.removeAttribute(i);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?t.removeAttribute(i):t.setAttribute(i,r);break;case"popover":At("beforetoggle",t),At("toggle",t),br(t,"popover",r);break;case"xlinkActuate":On(t,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":On(t,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":On(t,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":On(t,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":On(t,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":On(t,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":On(t,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":On(t,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":On(t,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":br(t,"is",r);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=bo.get(i)||i,br(t,i,r))}}function uf(t,e,i,r,l,f){switch(i){case"style":Xi(t,r,f);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(h(61));if(i=r.__html,i!=null){if(l.children!=null)throw Error(h(60));t.innerHTML=i}}break;case"children":typeof r=="string"?mn(t,r):(typeof r=="number"||typeof r=="bigint")&&mn(t,""+r);break;case"onScroll":r!=null&&At("scroll",t);break;case"onScrollEnd":r!=null&&At("scrollend",t);break;case"onClick":r!=null&&(t.onclick=Ql);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ys.hasOwnProperty(i))t:{if(i[0]==="o"&&i[1]==="n"&&(l=i.endsWith("Capture"),e=i.slice(2,l?i.length-7:void 0),f=t[Ce]||null,f=f!=null?f[i]:null,typeof f=="function"&&t.removeEventListener(e,f,l),typeof r=="function")){typeof f!="function"&&f!==null&&(i in t?t[i]=null:t.hasAttribute(i)&&t.removeAttribute(i)),t.addEventListener(e,r,l);break t}i in t?t[i]=r:r===!0?t.setAttribute(i,""):br(t,i,r)}}}function Re(t,e,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":At("error",t),At("load",t);var r=!1,l=!1,f;for(f in i)if(i.hasOwnProperty(f)){var g=i[f];if(g!=null)switch(f){case"src":r=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(h(137,e));default:Gt(t,e,f,g,i,null)}}l&&Gt(t,e,"srcSet",i.srcSet,i,null),r&&Gt(t,e,"src",i.src,i,null);return;case"input":At("invalid",t);var x=f=g=l=null,R=null,Z=null;for(r in i)if(i.hasOwnProperty(r)){var K=i[r];if(K!=null)switch(r){case"name":l=K;break;case"type":g=K;break;case"checked":R=K;break;case"defaultChecked":Z=K;break;case"value":f=K;break;case"defaultValue":x=K;break;case"children":case"dangerouslySetInnerHTML":if(K!=null)throw Error(h(137,e));break;default:Gt(t,e,r,K,i,null)}}Vs(t,f,x,R,Z,g,l,!1),Gi(t);return;case"select":At("invalid",t),r=g=f=null;for(l in i)if(i.hasOwnProperty(l)&&(x=i[l],x!=null))switch(l){case"value":f=x;break;case"defaultValue":g=x;break;case"multiple":r=x;default:Gt(t,e,l,x,i,null)}e=f,i=g,t.multiple=!!r,e!=null?Qe(t,!!r,e,!1):i!=null&&Qe(t,!!r,i,!0);return;case"textarea":At("invalid",t),f=l=r=null;for(g in i)if(i.hasOwnProperty(g)&&(x=i[g],x!=null))switch(g){case"value":r=x;break;case"defaultValue":l=x;break;case"children":f=x;break;case"dangerouslySetInnerHTML":if(x!=null)throw Error(h(91));break;default:Gt(t,e,g,x,i,null)}Zn(t,r,l,f),Gi(t);return;case"option":for(R in i)if(i.hasOwnProperty(R)&&(r=i[R],r!=null))switch(R){case"selected":t.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:Gt(t,e,R,r,i,null)}return;case"dialog":At("beforetoggle",t),At("toggle",t),At("cancel",t),At("close",t);break;case"iframe":case"object":At("load",t);break;case"video":case"audio":for(r=0;r<as.length;r++)At(as[r],t);break;case"image":At("error",t),At("load",t);break;case"details":At("toggle",t);break;case"embed":case"source":case"link":At("error",t),At("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(Z in i)if(i.hasOwnProperty(Z)&&(r=i[Z],r!=null))switch(Z){case"children":case"dangerouslySetInnerHTML":throw Error(h(137,e));default:Gt(t,e,Z,r,i,null)}return;default:if(Za(e)){for(K in i)i.hasOwnProperty(K)&&(r=i[K],r!==void 0&&uf(t,e,K,r,i,void 0));return}}for(x in i)i.hasOwnProperty(x)&&(r=i[x],r!=null&&Gt(t,e,x,r,i,null))}function mg(t,e,i,r){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var l=null,f=null,g=null,x=null,R=null,Z=null,K=null;for(k in i){var W=i[k];if(i.hasOwnProperty(k)&&W!=null)switch(k){case"checked":break;case"value":break;case"defaultValue":R=W;default:r.hasOwnProperty(k)||Gt(t,e,k,null,r,W)}}for(var H in r){var k=r[H];if(W=i[H],r.hasOwnProperty(H)&&(k!=null||W!=null))switch(H){case"type":f=k;break;case"name":l=k;break;case"checked":Z=k;break;case"defaultChecked":K=k;break;case"value":g=k;break;case"defaultValue":x=k;break;case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(h(137,e));break;default:k!==W&&Gt(t,e,H,k,r,W)}}Ne(t,g,x,R,Z,K,f,l);return;case"select":k=g=x=H=null;for(f in i)if(R=i[f],i.hasOwnProperty(f)&&R!=null)switch(f){case"value":break;case"multiple":k=R;default:r.hasOwnProperty(f)||Gt(t,e,f,null,r,R)}for(l in r)if(f=r[l],R=i[l],r.hasOwnProperty(l)&&(f!=null||R!=null))switch(l){case"value":H=f;break;case"defaultValue":x=f;break;case"multiple":g=f;default:f!==R&&Gt(t,e,l,f,r,R)}e=x,i=g,r=k,H!=null?Qe(t,!!i,H,!1):!!r!=!!i&&(e!=null?Qe(t,!!i,e,!0):Qe(t,!!i,i?[]:"",!1));return;case"textarea":k=H=null;for(x in i)if(l=i[x],i.hasOwnProperty(x)&&l!=null&&!r.hasOwnProperty(x))switch(x){case"value":break;case"children":break;default:Gt(t,e,x,null,r,l)}for(g in r)if(l=r[g],f=i[g],r.hasOwnProperty(g)&&(l!=null||f!=null))switch(g){case"value":H=l;break;case"defaultValue":k=l;break;case"children":break;case"dangerouslySetInnerHTML":if(l!=null)throw Error(h(91));break;default:l!==f&&Gt(t,e,g,l,r,f)}ee(t,H,k);return;case"option":for(var gt in i)if(H=i[gt],i.hasOwnProperty(gt)&&H!=null&&!r.hasOwnProperty(gt))switch(gt){case"selected":t.selected=!1;break;default:Gt(t,e,gt,null,r,H)}for(R in r)if(H=r[R],k=i[R],r.hasOwnProperty(R)&&H!==k&&(H!=null||k!=null))switch(R){case"selected":t.selected=H&&typeof H!="function"&&typeof H!="symbol";break;default:Gt(t,e,R,H,r,k)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var pt in i)H=i[pt],i.hasOwnProperty(pt)&&H!=null&&!r.hasOwnProperty(pt)&&Gt(t,e,pt,null,r,H);for(Z in r)if(H=r[Z],k=i[Z],r.hasOwnProperty(Z)&&H!==k&&(H!=null||k!=null))switch(Z){case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(h(137,e));break;default:Gt(t,e,Z,H,r,k)}return;default:if(Za(e)){for(var Vt in i)H=i[Vt],i.hasOwnProperty(Vt)&&H!==void 0&&!r.hasOwnProperty(Vt)&&uf(t,e,Vt,void 0,r,H);for(K in r)H=r[K],k=i[K],!r.hasOwnProperty(K)||H===k||H===void 0&&k===void 0||uf(t,e,K,H,r,k);return}}for(var D in i)H=i[D],i.hasOwnProperty(D)&&H!=null&&!r.hasOwnProperty(D)&&Gt(t,e,D,null,r,H);for(W in r)H=r[W],k=i[W],!r.hasOwnProperty(W)||H===k||H==null&&k==null||Gt(t,e,W,H,r,k)}var cf=null,ff=null;function Kl(t){return t.nodeType===9?t:t.ownerDocument}function Lm(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Om(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function hf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var df=null;function pg(){var t=window.event;return t&&t.type==="popstate"?t===df?!1:(df=t,!0):(df=null,!1)}var Am=typeof setTimeout=="function"?setTimeout:void 0,_g=typeof clearTimeout=="function"?clearTimeout:void 0,Rm=typeof Promise=="function"?Promise:void 0,gg=typeof queueMicrotask=="function"?queueMicrotask:typeof Rm<"u"?function(t){return Rm.resolve(null).then(t).catch(vg)}:Am;function vg(t){setTimeout(function(){throw t})}function Ea(t){return t==="head"}function Mm(t,e){var i=e,r=0,l=0;do{var f=i.nextSibling;if(t.removeChild(i),f&&f.nodeType===8)if(i=f.data,i==="/$"){if(0<r&&8>r){i=r;var g=t.ownerDocument;if(i&1&&os(g.documentElement),i&2&&os(g.body),i&4)for(i=g.head,os(i),g=i.firstChild;g;){var x=g.nextSibling,R=g.nodeName;g[ki]||R==="SCRIPT"||R==="STYLE"||R==="LINK"&&g.rel.toLowerCase()==="stylesheet"||i.removeChild(g),g=x}}if(l===0){t.removeChild(f),ms(e);return}l--}else i==="$"||i==="$?"||i==="$!"?l++:r=i.charCodeAt(0)-48;else r=0;i=f}while(i);ms(e)}function mf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var i=e;switch(e=e.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":mf(i),yr(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}t.removeChild(i)}}function yg(t,e,i,r){for(;t.nodeType===1;){var l=i;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!r&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(r){if(!t[ki])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(f=t.getAttribute("rel"),f==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(f!==l.rel||t.getAttribute("href")!==(l.href==null||l.href===""?null:l.href)||t.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin)||t.getAttribute("title")!==(l.title==null?null:l.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(f=t.getAttribute("src"),(f!==(l.src==null?null:l.src)||t.getAttribute("type")!==(l.type==null?null:l.type)||t.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin))&&f&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var f=l.name==null?null:""+l.name;if(l.type==="hidden"&&t.getAttribute("name")===f)return t}else return t;if(t=Dn(t.nextSibling),t===null)break}return null}function bg(t,e,i){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!i||(t=Dn(t.nextSibling),t===null))return null;return t}function pf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function xg(t,e){var i=t.ownerDocument;if(t.data!=="$?"||i.readyState==="complete")e();else{var r=function(){e(),i.removeEventListener("DOMContentLoaded",r)};i.addEventListener("DOMContentLoaded",r),t._reactRetry=r}}function Dn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var _f=null;function Cm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var i=t.data;if(i==="$"||i==="$!"||i==="$?"){if(e===0)return t;e--}else i==="/$"&&e++}t=t.previousSibling}return null}function Nm(t,e,i){switch(e=Kl(i),t){case"html":if(t=e.documentElement,!t)throw Error(h(452));return t;case"head":if(t=e.head,!t)throw Error(h(453));return t;case"body":if(t=e.body,!t)throw Error(h(454));return t;default:throw Error(h(451))}}function os(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);yr(t)}var Sn=new Map,zm=new Set;function Jl(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Mi=rt.d;rt.d={f:wg,r:Sg,D:Eg,C:Tg,L:Lg,m:Og,X:Rg,S:Ag,M:Mg};function wg(){var t=Mi.f(),e=Hl();return t||e}function Sg(t){var e=ii(t);e!==null&&e.tag===5&&e.type==="form"?$h(e):Mi.r(t)}var io=typeof document>"u"?null:document;function Dm(t,e,i){var r=io;if(r&&typeof e=="string"&&e){var l=Te(e);l='link[rel="'+t+'"][href="'+l+'"]',typeof i=="string"&&(l+='[crossorigin="'+i+'"]'),zm.has(l)||(zm.add(l),t={rel:t,crossOrigin:i,href:e},r.querySelector(l)===null&&(e=r.createElement("link"),Re(e,"link",t),ue(e),r.head.appendChild(e)))}}function Eg(t){Mi.D(t),Dm("dns-prefetch",t,null)}function Tg(t,e){Mi.C(t,e),Dm("preconnect",t,e)}function Lg(t,e,i){Mi.L(t,e,i);var r=io;if(r&&t&&e){var l='link[rel="preload"][as="'+Te(e)+'"]';e==="image"&&i&&i.imageSrcSet?(l+='[imagesrcset="'+Te(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(l+='[imagesizes="'+Te(i.imageSizes)+'"]')):l+='[href="'+Te(t)+'"]';var f=l;switch(e){case"style":f=ao(t);break;case"script":f=ro(t)}Sn.has(f)||(t=E({rel:"preload",href:e==="image"&&i&&i.imageSrcSet?void 0:t,as:e},i),Sn.set(f,t),r.querySelector(l)!==null||e==="style"&&r.querySelector(ss(f))||e==="script"&&r.querySelector(ls(f))||(e=r.createElement("link"),Re(e,"link",t),ue(e),r.head.appendChild(e)))}}function Og(t,e){Mi.m(t,e);var i=io;if(i&&t){var r=e&&typeof e.as=="string"?e.as:"script",l='link[rel="modulepreload"][as="'+Te(r)+'"][href="'+Te(t)+'"]',f=l;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":f=ro(t)}if(!Sn.has(f)&&(t=E({rel:"modulepreload",href:t},e),Sn.set(f,t),i.querySelector(l)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(ls(f)))return}r=i.createElement("link"),Re(r,"link",t),ue(r),i.head.appendChild(r)}}}function Ag(t,e,i){Mi.S(t,e,i);var r=io;if(r&&t){var l=ai(r).hoistableStyles,f=ao(t);e=e||"default";var g=l.get(f);if(!g){var x={loading:0,preload:null};if(g=r.querySelector(ss(f)))x.loading=5;else{t=E({rel:"stylesheet",href:t,"data-precedence":e},i),(i=Sn.get(f))&&gf(t,i);var R=g=r.createElement("link");ue(R),Re(R,"link",t),R._p=new Promise(function(Z,K){R.onload=Z,R.onerror=K}),R.addEventListener("load",function(){x.loading|=1}),R.addEventListener("error",function(){x.loading|=2}),x.loading|=4,Fl(g,e,r)}g={type:"stylesheet",instance:g,count:1,state:x},l.set(f,g)}}}function Rg(t,e){Mi.X(t,e);var i=io;if(i&&t){var r=ai(i).hoistableScripts,l=ro(t),f=r.get(l);f||(f=i.querySelector(ls(l)),f||(t=E({src:t,async:!0},e),(e=Sn.get(l))&&vf(t,e),f=i.createElement("script"),ue(f),Re(f,"link",t),i.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},r.set(l,f))}}function Mg(t,e){Mi.M(t,e);var i=io;if(i&&t){var r=ai(i).hoistableScripts,l=ro(t),f=r.get(l);f||(f=i.querySelector(ls(l)),f||(t=E({src:t,async:!0,type:"module"},e),(e=Sn.get(l))&&vf(t,e),f=i.createElement("script"),ue(f),Re(f,"link",t),i.head.appendChild(f)),f={type:"script",instance:f,count:1,state:null},r.set(l,f))}}function Bm(t,e,i,r){var l=(l=at.current)?Jl(l):null;if(!l)throw Error(h(446));switch(t){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(e=ao(i.href),i=ai(l).hoistableStyles,r=i.get(e),r||(r={type:"style",instance:null,count:0,state:null},i.set(e,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){t=ao(i.href);var f=ai(l).hoistableStyles,g=f.get(t);if(g||(l=l.ownerDocument||l,g={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},f.set(t,g),(f=l.querySelector(ss(t)))&&!f._p&&(g.instance=f,g.state.loading=5),Sn.has(t)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},Sn.set(t,i),f||Cg(l,t,i,g.state))),e&&r===null)throw Error(h(528,""));return g}if(e&&r!==null)throw Error(h(529,""));return null;case"script":return e=i.async,i=i.src,typeof i=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=ro(i),i=ai(l).hoistableScripts,r=i.get(e),r||(r={type:"script",instance:null,count:0,state:null},i.set(e,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(h(444,t))}}function ao(t){return'href="'+Te(t)+'"'}function ss(t){return'link[rel="stylesheet"]['+t+"]"}function jm(t){return E({},t,{"data-precedence":t.precedence,precedence:null})}function Cg(t,e,i,r){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?r.loading=1:(e=t.createElement("link"),r.preload=e,e.addEventListener("load",function(){return r.loading|=1}),e.addEventListener("error",function(){return r.loading|=2}),Re(e,"link",i),ue(e),t.head.appendChild(e))}function ro(t){return'[src="'+Te(t)+'"]'}function ls(t){return"script[async]"+t}function Um(t,e,i){if(e.count++,e.instance===null)switch(e.type){case"style":var r=t.querySelector('style[data-href~="'+Te(i.href)+'"]');if(r)return e.instance=r,ue(r),r;var l=E({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return r=(t.ownerDocument||t).createElement("style"),ue(r),Re(r,"style",l),Fl(r,i.precedence,t),e.instance=r;case"stylesheet":l=ao(i.href);var f=t.querySelector(ss(l));if(f)return e.state.loading|=4,e.instance=f,ue(f),f;r=jm(i),(l=Sn.get(l))&&gf(r,l),f=(t.ownerDocument||t).createElement("link"),ue(f);var g=f;return g._p=new Promise(function(x,R){g.onload=x,g.onerror=R}),Re(f,"link",r),e.state.loading|=4,Fl(f,i.precedence,t),e.instance=f;case"script":return f=ro(i.src),(l=t.querySelector(ls(f)))?(e.instance=l,ue(l),l):(r=i,(l=Sn.get(f))&&(r=E({},i),vf(r,l)),t=t.ownerDocument||t,l=t.createElement("script"),ue(l),Re(l,"link",r),t.head.appendChild(l),e.instance=l);case"void":return null;default:throw Error(h(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(r=e.instance,e.state.loading|=4,Fl(r,i.precedence,t));return e.instance}function Fl(t,e,i){for(var r=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),l=r.length?r[r.length-1]:null,f=l,g=0;g<r.length;g++){var x=r[g];if(x.dataset.precedence===e)f=x;else if(f!==l)break}f?f.parentNode.insertBefore(t,f.nextSibling):(e=i.nodeType===9?i.head:i,e.insertBefore(t,e.firstChild))}function gf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function vf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Wl=null;function Pm(t,e,i){if(Wl===null){var r=new Map,l=Wl=new Map;l.set(i,r)}else l=Wl,r=l.get(i),r||(r=new Map,l.set(i,r));if(r.has(t))return r;for(r.set(t,null),i=i.getElementsByTagName(t),l=0;l<i.length;l++){var f=i[l];if(!(f[ki]||f[_e]||t==="link"&&f.getAttribute("rel")==="stylesheet")&&f.namespaceURI!=="http://www.w3.org/2000/svg"){var g=f.getAttribute(e)||"";g=t+g;var x=r.get(g);x?x.push(f):r.set(g,[f])}}return r}function Zm(t,e,i){t=t.ownerDocument||t,t.head.insertBefore(i,e==="title"?t.querySelector("head > title"):null)}function Ng(t,e,i){if(i===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Hm(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var us=null;function zg(){}function Dg(t,e,i){if(us===null)throw Error(h(475));var r=us;if(e.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var l=ao(i.href),f=t.querySelector(ss(l));if(f){t=f._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(r.count++,r=Il.bind(r),t.then(r,r)),e.state.loading|=4,e.instance=f,ue(f);return}f=t.ownerDocument||t,i=jm(i),(l=Sn.get(l))&&gf(i,l),f=f.createElement("link"),ue(f);var g=f;g._p=new Promise(function(x,R){g.onload=x,g.onerror=R}),Re(f,"link",i),e.instance=f}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(r.count++,e=Il.bind(r),t.addEventListener("load",e),t.addEventListener("error",e))}}function Bg(){if(us===null)throw Error(h(475));var t=us;return t.stylesheets&&t.count===0&&yf(t,t.stylesheets),0<t.count?function(e){var i=setTimeout(function(){if(t.stylesheets&&yf(t,t.stylesheets),t.unsuspend){var r=t.unsuspend;t.unsuspend=null,r()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(i)}}:null}function Il(){if(this.count--,this.count===0){if(this.stylesheets)yf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var $l=null;function yf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,$l=new Map,e.forEach(jg,t),$l=null,Il.call(t))}function jg(t,e){if(!(e.state.loading&4)){var i=$l.get(t);if(i)var r=i.get(null);else{i=new Map,$l.set(t,i);for(var l=t.querySelectorAll("link[data-precedence],style[data-precedence]"),f=0;f<l.length;f++){var g=l[f];(g.nodeName==="LINK"||g.getAttribute("media")!=="not all")&&(i.set(g.dataset.precedence,g),r=g)}r&&i.set(null,r)}l=e.instance,g=l.getAttribute("data-precedence"),f=i.get(g)||r,f===r&&i.set(null,l),i.set(g,l),this.count++,r=Il.bind(this),l.addEventListener("load",r),l.addEventListener("error",r),f?f.parentNode.insertBefore(l,f.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(l,t.firstChild)),e.state.loading|=4}}var cs={$$typeof:et,Provider:null,Consumer:null,_currentValue:$,_currentValue2:$,_threadCount:0};function Ug(t,e,i,r,l,f,g,x){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=vr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vr(0),this.hiddenUpdates=vr(null),this.identifierPrefix=r,this.onUncaughtError=l,this.onCaughtError=f,this.onRecoverableError=g,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=x,this.incompleteTransitions=new Map}function km(t,e,i,r,l,f,g,x,R,Z,K,W){return t=new Ug(t,e,i,g,x,R,Z,W),e=1,f===!0&&(e|=24),f=Ye(3,null,null,e),t.current=f,f.stateNode=t,e=$u(),e.refCount++,t.pooledCache=e,e.refCount++,f.memoizedState={element:r,isDehydrated:i,cache:e},ic(f),t}function qm(t){return t?(t=xi,t):xi}function Ym(t,e,i,r,l,f){l=qm(l),r.context===null?r.context=l:r.pendingContext=l,r=fa(e),r.payload={element:i},f=f===void 0?null:f,f!==null&&(r.callback=f),i=ha(t,r,e),i!==null&&(hn(i,t,e),Ho(i,t,e))}function Gm(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var i=t.retryLane;t.retryLane=i!==0&&i<e?i:e}}function bf(t,e){Gm(t,e),(t=t.alternate)&&Gm(t,e)}function Vm(t){if(t.tag===13){var e=oa(t,67108864);e!==null&&hn(e,t,67108864),bf(t,67108864)}}var tu=!0;function Pg(t,e,i,r){var l=V.T;V.T=null;var f=rt.p;try{rt.p=2,xf(t,e,i,r)}finally{rt.p=f,V.T=l}}function Zg(t,e,i,r){var l=V.T;V.T=null;var f=rt.p;try{rt.p=8,xf(t,e,i,r)}finally{rt.p=f,V.T=l}}function xf(t,e,i,r){if(tu){var l=wf(r);if(l===null)lf(t,e,r,eu,i),Qm(t,r);else if(kg(l,t,e,i,r))r.stopPropagation();else if(Qm(t,r),e&4&&-1<Hg.indexOf(t)){for(;l!==null;){var f=ii(l);if(f!==null)switch(f.tag){case 3:if(f=f.stateNode,f.current.memoizedState.isDehydrated){var g=jn(f.pendingLanes);if(g!==0){var x=f;for(x.pendingLanes|=2,x.entangledLanes|=2;g;){var R=1<<31-Ue(g);x.entanglements[1]|=R,g&=~R}Jn(f),(Ht&6)===0&&(Pl=Ve()+500,is(0))}}break;case 13:x=oa(f,2),x!==null&&hn(x,f,2),Hl(),bf(f,2)}if(f=wf(r),f===null&&lf(t,e,r,eu,i),f===l)break;l=f}l!==null&&r.stopPropagation()}else lf(t,e,r,null,i)}}function wf(t){return t=Ki(t),Sf(t)}var eu=null;function Sf(t){if(eu=null,t=Pn(t),t!==null){var e=_(t);if(e===null)t=null;else{var i=e.tag;if(i===13){if(t=v(e),t!==null)return t;t=null}else if(i===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return eu=t,null}function Xm(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(_r()){case zs:return 2;case fo:return 8;case Ui:case Ds:return 32;case ho:return 268435456;default:return 32}default:return 32}}var Ef=!1,Ta=null,La=null,Oa=null,fs=new Map,hs=new Map,Aa=[],Hg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Qm(t,e){switch(t){case"focusin":case"focusout":Ta=null;break;case"dragenter":case"dragleave":La=null;break;case"mouseover":case"mouseout":Oa=null;break;case"pointerover":case"pointerout":fs.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":hs.delete(e.pointerId)}}function ds(t,e,i,r,l,f){return t===null||t.nativeEvent!==f?(t={blockedOn:e,domEventName:i,eventSystemFlags:r,nativeEvent:f,targetContainers:[l]},e!==null&&(e=ii(e),e!==null&&Vm(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,l!==null&&e.indexOf(l)===-1&&e.push(l),t)}function kg(t,e,i,r,l){switch(e){case"focusin":return Ta=ds(Ta,t,e,i,r,l),!0;case"dragenter":return La=ds(La,t,e,i,r,l),!0;case"mouseover":return Oa=ds(Oa,t,e,i,r,l),!0;case"pointerover":var f=l.pointerId;return fs.set(f,ds(fs.get(f)||null,t,e,i,r,l)),!0;case"gotpointercapture":return f=l.pointerId,hs.set(f,ds(hs.get(f)||null,t,e,i,r,l)),!0}return!1}function Km(t){var e=Pn(t.target);if(e!==null){var i=_(e);if(i!==null){if(e=i.tag,e===13){if(e=v(i),e!==null){t.blockedOn=e,go(t.priority,function(){if(i.tag===13){var r=fn();r=po(r);var l=oa(i,r);l!==null&&hn(l,i,r),bf(i,r)}});return}}else if(e===3&&i.stateNode.current.memoizedState.isDehydrated){t.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}t.blockedOn=null}function nu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var i=wf(t.nativeEvent);if(i===null){i=t.nativeEvent;var r=new i.constructor(i.type,i);Ha=r,i.target.dispatchEvent(r),Ha=null}else return e=ii(i),e!==null&&Vm(e),t.blockedOn=i,!1;e.shift()}return!0}function Jm(t,e,i){nu(t)&&i.delete(e)}function qg(){Ef=!1,Ta!==null&&nu(Ta)&&(Ta=null),La!==null&&nu(La)&&(La=null),Oa!==null&&nu(Oa)&&(Oa=null),fs.forEach(Jm),hs.forEach(Jm)}function iu(t,e){t.blockedOn===e&&(t.blockedOn=null,Ef||(Ef=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,qg)))}var au=null;function Fm(t){au!==t&&(au=t,o.unstable_scheduleCallback(o.unstable_NormalPriority,function(){au===t&&(au=null);for(var e=0;e<t.length;e+=3){var i=t[e],r=t[e+1],l=t[e+2];if(typeof r!="function"){if(Sf(r||i)===null)continue;break}var f=ii(i);f!==null&&(t.splice(e,3),e-=3,wc(f,{pending:!0,data:l,method:i.method,action:r},r,l))}}))}function ms(t){function e(R){return iu(R,t)}Ta!==null&&iu(Ta,t),La!==null&&iu(La,t),Oa!==null&&iu(Oa,t),fs.forEach(e),hs.forEach(e);for(var i=0;i<Aa.length;i++){var r=Aa[i];r.blockedOn===t&&(r.blockedOn=null)}for(;0<Aa.length&&(i=Aa[0],i.blockedOn===null);)Km(i),i.blockedOn===null&&Aa.shift();if(i=(t.ownerDocument||t).$$reactFormReplay,i!=null)for(r=0;r<i.length;r+=3){var l=i[r],f=i[r+1],g=l[Ce]||null;if(typeof f=="function")g||Fm(i);else if(g){var x=null;if(f&&f.hasAttribute("formAction")){if(l=f,g=f[Ce]||null)x=g.formAction;else if(Sf(l)!==null)continue}else x=g.action;typeof x=="function"?i[r+1]=x:(i.splice(r,3),r-=3),Fm(i)}}}function Tf(t){this._internalRoot=t}ru.prototype.render=Tf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(h(409));var i=e.current,r=fn();Ym(i,r,t,e,null,null)},ru.prototype.unmount=Tf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Ym(t.current,2,null,t,null,null),Hl(),e[Hi]=null}};function ru(t){this._internalRoot=t}ru.prototype.unstable_scheduleHydration=function(t){if(t){var e=Hs();t={blockedOn:null,target:t,priority:e};for(var i=0;i<Aa.length&&e!==0&&e<Aa[i].priority;i++);Aa.splice(i,0,t),i===0&&Km(t)}};var Wm=u.version;if(Wm!=="19.1.0")throw Error(h(527,Wm,"19.1.0"));rt.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(h(188)):(t=Object.keys(t).join(","),Error(h(268,t)));return t=w(e),t=t!==null?y(t):null,t=t===null?null:t.stateNode,t};var Yg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:V,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ou=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ou.isDisabled&&ou.supportsFiber)try{ni=ou.inject(Yg),Me=ou}catch{}}return _s.createRoot=function(t,e){if(!m(t))throw Error(h(299));var i=!1,r="",l=dd,f=md,g=pd,x=null;return e!=null&&(e.unstable_strictMode===!0&&(i=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onUncaughtError!==void 0&&(l=e.onUncaughtError),e.onCaughtError!==void 0&&(f=e.onCaughtError),e.onRecoverableError!==void 0&&(g=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(x=e.unstable_transitionCallbacks)),e=km(t,1,!1,null,null,i,r,l,f,g,x,null),t[Hi]=e.current,sf(t),new Tf(e)},_s.hydrateRoot=function(t,e,i){if(!m(t))throw Error(h(299));var r=!1,l="",f=dd,g=md,x=pd,R=null,Z=null;return i!=null&&(i.unstable_strictMode===!0&&(r=!0),i.identifierPrefix!==void 0&&(l=i.identifierPrefix),i.onUncaughtError!==void 0&&(f=i.onUncaughtError),i.onCaughtError!==void 0&&(g=i.onCaughtError),i.onRecoverableError!==void 0&&(x=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(R=i.unstable_transitionCallbacks),i.formState!==void 0&&(Z=i.formState)),e=km(t,1,!0,e,i??null,r,l,f,g,x,R,Z),e.context=qm(null),i=e.current,r=fn(),r=po(r),l=fa(r),l.callback=null,ha(i,l,r),i=r,e.current.lanes=i,Zi(e,i),Jn(e),t[Hi]=e.current,sf(t),new ru(e)},_s.version="19.1.0",_s}var sp;function Ig(){if(sp)return Af.exports;sp=1;function o(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(o)}catch(u){console.error(u)}}return o(),Af.exports=Wg(),Af.exports}var $g=Ig(),gs={},lp;function tv(){if(lp)return gs;lp=1,Object.defineProperty(gs,"__esModule",{value:!0}),gs.parse=v,gs.serialize=y;const o=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,u=/^[\u0021-\u003A\u003C-\u007E]*$/,c=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,h=/^[\u0020-\u003A\u003D-\u007E]*$/,m=Object.prototype.toString,_=(()=>{const B=function(){};return B.prototype=Object.create(null),B})();function v(B,J){const N=new _,U=B.length;if(U<2)return N;const Y=J?.decode||E;let X=0;do{const it=B.indexOf("=",X);if(it===-1)break;const et=B.indexOf(";",X),xt=et===-1?U:et;if(it>xt){X=B.lastIndexOf(";",it-1)+1;continue}const lt=S(B,X,it),Ct=w(B,it,lt),Nt=B.slice(lt,Ct);if(N[Nt]===void 0){let Pt=S(B,it+1,xt),jt=w(B,xt,Pt);const me=Y(B.slice(Pt,jt));N[Nt]=me}X=xt+1}while(X<U);return N}function S(B,J,N){do{const U=B.charCodeAt(J);if(U!==32&&U!==9)return J}while(++J<N);return N}function w(B,J,N){for(;J>N;){const U=B.charCodeAt(--J);if(U!==32&&U!==9)return J+1}return N}function y(B,J,N){const U=N?.encode||encodeURIComponent;if(!o.test(B))throw new TypeError(`argument name is invalid: ${B}`);const Y=U(J);if(!u.test(Y))throw new TypeError(`argument val is invalid: ${J}`);let X=B+"="+Y;if(!N)return X;if(N.maxAge!==void 0){if(!Number.isInteger(N.maxAge))throw new TypeError(`option maxAge is invalid: ${N.maxAge}`);X+="; Max-Age="+N.maxAge}if(N.domain){if(!c.test(N.domain))throw new TypeError(`option domain is invalid: ${N.domain}`);X+="; Domain="+N.domain}if(N.path){if(!h.test(N.path))throw new TypeError(`option path is invalid: ${N.path}`);X+="; Path="+N.path}if(N.expires){if(!z(N.expires)||!Number.isFinite(N.expires.valueOf()))throw new TypeError(`option expires is invalid: ${N.expires}`);X+="; Expires="+N.expires.toUTCString()}if(N.httpOnly&&(X+="; HttpOnly"),N.secure&&(X+="; Secure"),N.partitioned&&(X+="; Partitioned"),N.priority)switch(typeof N.priority=="string"?N.priority.toLowerCase():void 0){case"low":X+="; Priority=Low";break;case"medium":X+="; Priority=Medium";break;case"high":X+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${N.priority}`)}if(N.sameSite)switch(typeof N.sameSite=="string"?N.sameSite.toLowerCase():N.sameSite){case!0:case"strict":X+="; SameSite=Strict";break;case"lax":X+="; SameSite=Lax";break;case"none":X+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${N.sameSite}`)}return X}function E(B){if(B.indexOf("%")===-1)return B;try{return decodeURIComponent(B)}catch{return B}}function z(B){return m.call(B)==="[object Date]"}return gs}tv();var up="popstate";function ev(o={}){function u(h,m){let{pathname:_,search:v,hash:S}=h.location;return Pf("",{pathname:_,search:v,hash:S},m.state&&m.state.usr||null,m.state&&m.state.key||"default")}function c(h,m){return typeof m=="string"?m:ws(m)}return iv(u,c,null,o)}function ae(o,u){if(o===!1||o===null||typeof o>"u")throw new Error(u)}function Wn(o,u){if(!o){typeof console<"u"&&console.warn(u);try{throw new Error(u)}catch{}}}function nv(){return Math.random().toString(36).substring(2,10)}function cp(o,u){return{usr:o.state,key:o.key,idx:u}}function Pf(o,u,c=null,h){return{pathname:typeof o=="string"?o:o.pathname,search:"",hash:"",...typeof u=="string"?oo(u):u,state:c,key:u&&u.key||h||nv()}}function ws({pathname:o="/",search:u="",hash:c=""}){return u&&u!=="?"&&(o+=u.charAt(0)==="?"?u:"?"+u),c&&c!=="#"&&(o+=c.charAt(0)==="#"?c:"#"+c),o}function oo(o){let u={};if(o){let c=o.indexOf("#");c>=0&&(u.hash=o.substring(c),o=o.substring(0,c));let h=o.indexOf("?");h>=0&&(u.search=o.substring(h),o=o.substring(0,h)),o&&(u.pathname=o)}return u}function iv(o,u,c,h={}){let{window:m=document.defaultView,v5Compat:_=!1}=h,v=m.history,S="POP",w=null,y=E();y==null&&(y=0,v.replaceState({...v.state,idx:y},""));function E(){return(v.state||{idx:null}).idx}function z(){S="POP";let Y=E(),X=Y==null?null:Y-y;y=Y,w&&w({action:S,location:U.location,delta:X})}function B(Y,X){S="PUSH";let it=Pf(U.location,Y,X);y=E()+1;let et=cp(it,y),xt=U.createHref(it);try{v.pushState(et,"",xt)}catch(lt){if(lt instanceof DOMException&&lt.name==="DataCloneError")throw lt;m.location.assign(xt)}_&&w&&w({action:S,location:U.location,delta:1})}function J(Y,X){S="REPLACE";let it=Pf(U.location,Y,X);y=E();let et=cp(it,y),xt=U.createHref(it);v.replaceState(et,"",xt),_&&w&&w({action:S,location:U.location,delta:0})}function N(Y){return av(Y)}let U={get action(){return S},get location(){return o(m,v)},listen(Y){if(w)throw new Error("A history only accepts one active listener");return m.addEventListener(up,z),w=Y,()=>{m.removeEventListener(up,z),w=null}},createHref(Y){return u(m,Y)},createURL:N,encodeLocation(Y){let X=N(Y);return{pathname:X.pathname,search:X.search,hash:X.hash}},push:B,replace:J,go(Y){return v.go(Y)}};return U}function av(o,u=!1){let c="http://localhost";typeof window<"u"&&(c=window.location.origin!=="null"?window.location.origin:window.location.href),ae(c,"No window.location.(origin|href) available to create URL");let h=typeof o=="string"?o:ws(o);return h=h.replace(/ $/,"%20"),!u&&h.startsWith("//")&&(h=c+h),new URL(h,c)}function zp(o,u,c="/"){return rv(o,u,c,!1)}function rv(o,u,c,h){let m=typeof u=="string"?oo(u):u,_=zi(m.pathname||"/",c);if(_==null)return null;let v=Dp(o);ov(v);let S=null;for(let w=0;S==null&&w<v.length;++w){let y=gv(_);S=pv(v[w],y,h)}return S}function Dp(o,u=[],c=[],h=""){let m=(_,v,S)=>{let w={relativePath:S===void 0?_.path||"":S,caseSensitive:_.caseSensitive===!0,childrenIndex:v,route:_};w.relativePath.startsWith("/")&&(ae(w.relativePath.startsWith(h),`Absolute route path "${w.relativePath}" nested under path "${h}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),w.relativePath=w.relativePath.slice(h.length));let y=Ni([h,w.relativePath]),E=c.concat(w);_.children&&_.children.length>0&&(ae(_.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${y}".`),Dp(_.children,u,E,y)),!(_.path==null&&!_.index)&&u.push({path:y,score:dv(y,_.index),routesMeta:E})};return o.forEach((_,v)=>{if(_.path===""||!_.path?.includes("?"))m(_,v);else for(let S of Bp(_.path))m(_,v,S)}),u}function Bp(o){let u=o.split("/");if(u.length===0)return[];let[c,...h]=u,m=c.endsWith("?"),_=c.replace(/\?$/,"");if(h.length===0)return m?[_,""]:[_];let v=Bp(h.join("/")),S=[];return S.push(...v.map(w=>w===""?_:[_,w].join("/"))),m&&S.push(...v),S.map(w=>o.startsWith("/")&&w===""?"/":w)}function ov(o){o.sort((u,c)=>u.score!==c.score?c.score-u.score:mv(u.routesMeta.map(h=>h.childrenIndex),c.routesMeta.map(h=>h.childrenIndex)))}var sv=/^:[\w-]+$/,lv=3,uv=2,cv=1,fv=10,hv=-2,fp=o=>o==="*";function dv(o,u){let c=o.split("/"),h=c.length;return c.some(fp)&&(h+=hv),u&&(h+=uv),c.filter(m=>!fp(m)).reduce((m,_)=>m+(sv.test(_)?lv:_===""?cv:fv),h)}function mv(o,u){return o.length===u.length&&o.slice(0,-1).every((h,m)=>h===u[m])?o[o.length-1]-u[u.length-1]:0}function pv(o,u,c=!1){let{routesMeta:h}=o,m={},_="/",v=[];for(let S=0;S<h.length;++S){let w=h[S],y=S===h.length-1,E=_==="/"?u:u.slice(_.length)||"/",z=pu({path:w.relativePath,caseSensitive:w.caseSensitive,end:y},E),B=w.route;if(!z&&y&&c&&!h[h.length-1].route.index&&(z=pu({path:w.relativePath,caseSensitive:w.caseSensitive,end:!1},E)),!z)return null;Object.assign(m,z.params),v.push({params:m,pathname:Ni([_,z.pathname]),pathnameBase:xv(Ni([_,z.pathnameBase])),route:B}),z.pathnameBase!=="/"&&(_=Ni([_,z.pathnameBase]))}return v}function pu(o,u){typeof o=="string"&&(o={path:o,caseSensitive:!1,end:!0});let[c,h]=_v(o.path,o.caseSensitive,o.end),m=u.match(c);if(!m)return null;let _=m[0],v=_.replace(/(.)\/+$/,"$1"),S=m.slice(1);return{params:h.reduce((y,{paramName:E,isOptional:z},B)=>{if(E==="*"){let N=S[B]||"";v=_.slice(0,_.length-N.length).replace(/(.)\/+$/,"$1")}const J=S[B];return z&&!J?y[E]=void 0:y[E]=(J||"").replace(/%2F/g,"/"),y},{}),pathname:_,pathnameBase:v,pattern:o}}function _v(o,u=!1,c=!0){Wn(o==="*"||!o.endsWith("*")||o.endsWith("/*"),`Route path "${o}" will be treated as if it were "${o.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${o.replace(/\*$/,"/*")}".`);let h=[],m="^"+o.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(v,S,w)=>(h.push({paramName:S,isOptional:w!=null}),w?"/?([^\\/]+)?":"/([^\\/]+)"));return o.endsWith("*")?(h.push({paramName:"*"}),m+=o==="*"||o==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):c?m+="\\/*$":o!==""&&o!=="/"&&(m+="(?:(?=\\/|$))"),[new RegExp(m,u?void 0:"i"),h]}function gv(o){try{return o.split("/").map(u=>decodeURIComponent(u).replace(/\//g,"%2F")).join("/")}catch(u){return Wn(!1,`The URL path "${o}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${u}).`),o}}function zi(o,u){if(u==="/")return o;if(!o.toLowerCase().startsWith(u.toLowerCase()))return null;let c=u.endsWith("/")?u.length-1:u.length,h=o.charAt(c);return h&&h!=="/"?null:o.slice(c)||"/"}function vv(o,u="/"){let{pathname:c,search:h="",hash:m=""}=typeof o=="string"?oo(o):o;return{pathname:c?c.startsWith("/")?c:yv(c,u):u,search:wv(h),hash:Sv(m)}}function yv(o,u){let c=u.replace(/\/+$/,"").split("/");return o.split("/").forEach(m=>{m===".."?c.length>1&&c.pop():m!=="."&&c.push(m)}),c.length>1?c.join("/"):"/"}function Nf(o,u,c,h){return`Cannot include a '${o}' character in a manually specified \`to.${u}\` field [${JSON.stringify(h)}].  Please separate it out to the \`to.${c}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function bv(o){return o.filter((u,c)=>c===0||u.route.path&&u.route.path.length>0)}function jp(o){let u=bv(o);return u.map((c,h)=>h===u.length-1?c.pathname:c.pathnameBase)}function Up(o,u,c,h=!1){let m;typeof o=="string"?m=oo(o):(m={...o},ae(!m.pathname||!m.pathname.includes("?"),Nf("?","pathname","search",m)),ae(!m.pathname||!m.pathname.includes("#"),Nf("#","pathname","hash",m)),ae(!m.search||!m.search.includes("#"),Nf("#","search","hash",m)));let _=o===""||m.pathname==="",v=_?"/":m.pathname,S;if(v==null)S=c;else{let z=u.length-1;if(!h&&v.startsWith("..")){let B=v.split("/");for(;B[0]==="..";)B.shift(),z-=1;m.pathname=B.join("/")}S=z>=0?u[z]:"/"}let w=vv(m,S),y=v&&v!=="/"&&v.endsWith("/"),E=(_||v===".")&&c.endsWith("/");return!w.pathname.endsWith("/")&&(y||E)&&(w.pathname+="/"),w}var Ni=o=>o.join("/").replace(/\/\/+/g,"/"),xv=o=>o.replace(/\/+$/,"").replace(/^\/*/,"/"),wv=o=>!o||o==="?"?"":o.startsWith("?")?o:"?"+o,Sv=o=>!o||o==="#"?"":o.startsWith("#")?o:"#"+o;function Ev(o){return o!=null&&typeof o.status=="number"&&typeof o.statusText=="string"&&typeof o.internal=="boolean"&&"data"in o}var Pp=["POST","PUT","PATCH","DELETE"];new Set(Pp);var Tv=["GET",...Pp];new Set(Tv);var so=M.createContext(null);so.displayName="DataRouter";var yu=M.createContext(null);yu.displayName="DataRouterState";var Zp=M.createContext({isTransitioning:!1});Zp.displayName="ViewTransition";var Lv=M.createContext(new Map);Lv.displayName="Fetchers";var Ov=M.createContext(null);Ov.displayName="Await";var In=M.createContext(null);In.displayName="Navigation";var Es=M.createContext(null);Es.displayName="Location";var $n=M.createContext({outlet:null,matches:[],isDataRoute:!1});$n.displayName="Route";var If=M.createContext(null);If.displayName="RouteError";function Av(o,{relative:u}={}){ae(Ts(),"useHref() may be used only in the context of a <Router> component.");let{basename:c,navigator:h}=M.useContext(In),{hash:m,pathname:_,search:v}=Ls(o,{relative:u}),S=_;return c!=="/"&&(S=_==="/"?c:Ni([c,_])),h.createHref({pathname:S,search:v,hash:m})}function Ts(){return M.useContext(Es)!=null}function Bi(){return ae(Ts(),"useLocation() may be used only in the context of a <Router> component."),M.useContext(Es).location}var Hp="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function kp(o){M.useContext(In).static||M.useLayoutEffect(o)}function bu(){let{isDataRoute:o}=M.useContext($n);return o?qv():Rv()}function Rv(){ae(Ts(),"useNavigate() may be used only in the context of a <Router> component.");let o=M.useContext(so),{basename:u,navigator:c}=M.useContext(In),{matches:h}=M.useContext($n),{pathname:m}=Bi(),_=JSON.stringify(jp(h)),v=M.useRef(!1);return kp(()=>{v.current=!0}),M.useCallback((w,y={})=>{if(Wn(v.current,Hp),!v.current)return;if(typeof w=="number"){c.go(w);return}let E=Up(w,JSON.parse(_),m,y.relative==="path");o==null&&u!=="/"&&(E.pathname=E.pathname==="/"?u:Ni([u,E.pathname])),(y.replace?c.replace:c.push)(E,y.state,y)},[u,c,_,m,o])}M.createContext(null);function Mv(){let{matches:o}=M.useContext($n),u=o[o.length-1];return u?u.params:{}}function Ls(o,{relative:u}={}){let{matches:c}=M.useContext($n),{pathname:h}=Bi(),m=JSON.stringify(jp(c));return M.useMemo(()=>Up(o,JSON.parse(m),h,u==="path"),[o,m,h,u])}function Cv(o,u){return qp(o,u)}function qp(o,u,c,h){ae(Ts(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:m}=M.useContext(In),{matches:_}=M.useContext($n),v=_[_.length-1],S=v?v.params:{},w=v?v.pathname:"/",y=v?v.pathnameBase:"/",E=v&&v.route;{let X=E&&E.path||"";Yp(w,!E||X.endsWith("*")||X.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${w}" (under <Route path="${X}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${X}"> to <Route path="${X==="/"?"*":`${X}/*`}">.`)}let z=Bi(),B;if(u){let X=typeof u=="string"?oo(u):u;ae(y==="/"||X.pathname?.startsWith(y),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${y}" but pathname "${X.pathname}" was given in the \`location\` prop.`),B=X}else B=z;let J=B.pathname||"/",N=J;if(y!=="/"){let X=y.replace(/^\//,"").split("/");N="/"+J.replace(/^\//,"").split("/").slice(X.length).join("/")}let U=zp(o,{pathname:N});Wn(E||U!=null,`No routes matched location "${B.pathname}${B.search}${B.hash}" `),Wn(U==null||U[U.length-1].route.element!==void 0||U[U.length-1].route.Component!==void 0||U[U.length-1].route.lazy!==void 0,`Matched leaf route at location "${B.pathname}${B.search}${B.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let Y=jv(U&&U.map(X=>Object.assign({},X,{params:Object.assign({},S,X.params),pathname:Ni([y,m.encodeLocation?m.encodeLocation(X.pathname).pathname:X.pathname]),pathnameBase:X.pathnameBase==="/"?y:Ni([y,m.encodeLocation?m.encodeLocation(X.pathnameBase).pathname:X.pathnameBase])})),_,c,h);return u&&Y?M.createElement(Es.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...B},navigationType:"POP"}},Y):Y}function Nv(){let o=kv(),u=Ev(o)?`${o.status} ${o.statusText}`:o instanceof Error?o.message:JSON.stringify(o),c=o instanceof Error?o.stack:null,h="rgba(200,200,200, 0.5)",m={padding:"0.5rem",backgroundColor:h},_={padding:"2px 4px",backgroundColor:h},v=null;return console.error("Error handled by React Router default ErrorBoundary:",o),v=M.createElement(M.Fragment,null,M.createElement("p",null,"💿 Hey developer 👋"),M.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",M.createElement("code",{style:_},"ErrorBoundary")," or"," ",M.createElement("code",{style:_},"errorElement")," prop on your route.")),M.createElement(M.Fragment,null,M.createElement("h2",null,"Unexpected Application Error!"),M.createElement("h3",{style:{fontStyle:"italic"}},u),c?M.createElement("pre",{style:m},c):null,v)}var zv=M.createElement(Nv,null),Dv=class extends M.Component{constructor(o){super(o),this.state={location:o.location,revalidation:o.revalidation,error:o.error}}static getDerivedStateFromError(o){return{error:o}}static getDerivedStateFromProps(o,u){return u.location!==o.location||u.revalidation!=="idle"&&o.revalidation==="idle"?{error:o.error,location:o.location,revalidation:o.revalidation}:{error:o.error!==void 0?o.error:u.error,location:u.location,revalidation:o.revalidation||u.revalidation}}componentDidCatch(o,u){console.error("React Router caught the following error during render",o,u)}render(){return this.state.error!==void 0?M.createElement($n.Provider,{value:this.props.routeContext},M.createElement(If.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Bv({routeContext:o,match:u,children:c}){let h=M.useContext(so);return h&&h.static&&h.staticContext&&(u.route.errorElement||u.route.ErrorBoundary)&&(h.staticContext._deepestRenderedBoundaryId=u.route.id),M.createElement($n.Provider,{value:o},c)}function jv(o,u=[],c=null,h=null){if(o==null){if(!c)return null;if(c.errors)o=c.matches;else if(u.length===0&&!c.initialized&&c.matches.length>0)o=c.matches;else return null}let m=o,_=c?.errors;if(_!=null){let w=m.findIndex(y=>y.route.id&&_?.[y.route.id]!==void 0);ae(w>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(_).join(",")}`),m=m.slice(0,Math.min(m.length,w+1))}let v=!1,S=-1;if(c)for(let w=0;w<m.length;w++){let y=m[w];if((y.route.HydrateFallback||y.route.hydrateFallbackElement)&&(S=w),y.route.id){let{loaderData:E,errors:z}=c,B=y.route.loader&&!E.hasOwnProperty(y.route.id)&&(!z||z[y.route.id]===void 0);if(y.route.lazy||B){v=!0,S>=0?m=m.slice(0,S+1):m=[m[0]];break}}}return m.reduceRight((w,y,E)=>{let z,B=!1,J=null,N=null;c&&(z=_&&y.route.id?_[y.route.id]:void 0,J=y.route.errorElement||zv,v&&(S<0&&E===0?(Yp("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),B=!0,N=null):S===E&&(B=!0,N=y.route.hydrateFallbackElement||null)));let U=u.concat(m.slice(0,E+1)),Y=()=>{let X;return z?X=J:B?X=N:y.route.Component?X=M.createElement(y.route.Component,null):y.route.element?X=y.route.element:X=w,M.createElement(Bv,{match:y,routeContext:{outlet:w,matches:U,isDataRoute:c!=null},children:X})};return c&&(y.route.ErrorBoundary||y.route.errorElement||E===0)?M.createElement(Dv,{location:c.location,revalidation:c.revalidation,component:J,error:z,children:Y(),routeContext:{outlet:null,matches:U,isDataRoute:!0}}):Y()},null)}function $f(o){return`${o} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Uv(o){let u=M.useContext(so);return ae(u,$f(o)),u}function Pv(o){let u=M.useContext(yu);return ae(u,$f(o)),u}function Zv(o){let u=M.useContext($n);return ae(u,$f(o)),u}function th(o){let u=Zv(o),c=u.matches[u.matches.length-1];return ae(c.route.id,`${o} can only be used on routes that contain a unique "id"`),c.route.id}function Hv(){return th("useRouteId")}function kv(){let o=M.useContext(If),u=Pv("useRouteError"),c=th("useRouteError");return o!==void 0?o:u.errors?.[c]}function qv(){let{router:o}=Uv("useNavigate"),u=th("useNavigate"),c=M.useRef(!1);return kp(()=>{c.current=!0}),M.useCallback(async(m,_={})=>{Wn(c.current,Hp),c.current&&(typeof m=="number"?o.navigate(m):await o.navigate(m,{fromRouteId:u,..._}))},[o,u])}var hp={};function Yp(o,u,c){!u&&!hp[o]&&(hp[o]=!0,Wn(!1,c))}M.memo(Yv);function Yv({routes:o,future:u,state:c}){return qp(o,void 0,c,u)}function Ma(o){ae(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Gv({basename:o="/",children:u=null,location:c,navigationType:h="POP",navigator:m,static:_=!1}){ae(!Ts(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let v=o.replace(/^\/*/,"/"),S=M.useMemo(()=>({basename:v,navigator:m,static:_,future:{}}),[v,m,_]);typeof c=="string"&&(c=oo(c));let{pathname:w="/",search:y="",hash:E="",state:z=null,key:B="default"}=c,J=M.useMemo(()=>{let N=zi(w,v);return N==null?null:{location:{pathname:N,search:y,hash:E,state:z,key:B},navigationType:h}},[v,w,y,E,z,B,h]);return Wn(J!=null,`<Router basename="${v}"> is not able to match the URL "${w}${y}${E}" because it does not start with the basename, so the <Router> won't render anything.`),J==null?null:M.createElement(In.Provider,{value:S},M.createElement(Es.Provider,{children:u,value:J}))}function Vv({children:o,location:u}){return Cv(Zf(o),u)}function Zf(o,u=[]){let c=[];return M.Children.forEach(o,(h,m)=>{if(!M.isValidElement(h))return;let _=[...u,m];if(h.type===M.Fragment){c.push.apply(c,Zf(h.props.children,_));return}ae(h.type===Ma,`[${typeof h.type=="string"?h.type:h.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),ae(!h.props.index||!h.props.children,"An index route cannot have child routes.");let v={id:h.props.id||_.join("-"),caseSensitive:h.props.caseSensitive,element:h.props.element,Component:h.props.Component,index:h.props.index,path:h.props.path,loader:h.props.loader,action:h.props.action,hydrateFallbackElement:h.props.hydrateFallbackElement,HydrateFallback:h.props.HydrateFallback,errorElement:h.props.errorElement,ErrorBoundary:h.props.ErrorBoundary,hasErrorBoundary:h.props.hasErrorBoundary===!0||h.props.ErrorBoundary!=null||h.props.errorElement!=null,shouldRevalidate:h.props.shouldRevalidate,handle:h.props.handle,lazy:h.props.lazy};h.props.children&&(v.children=Zf(h.props.children,_)),c.push(v)}),c}var uu="get",cu="application/x-www-form-urlencoded";function xu(o){return o!=null&&typeof o.tagName=="string"}function Xv(o){return xu(o)&&o.tagName.toLowerCase()==="button"}function Qv(o){return xu(o)&&o.tagName.toLowerCase()==="form"}function Kv(o){return xu(o)&&o.tagName.toLowerCase()==="input"}function Jv(o){return!!(o.metaKey||o.altKey||o.ctrlKey||o.shiftKey)}function Fv(o,u){return o.button===0&&(!u||u==="_self")&&!Jv(o)}var su=null;function Wv(){if(su===null)try{new FormData(document.createElement("form"),0),su=!1}catch{su=!0}return su}var Iv=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function zf(o){return o!=null&&!Iv.has(o)?(Wn(!1,`"${o}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${cu}"`),null):o}function $v(o,u){let c,h,m,_,v;if(Qv(o)){let S=o.getAttribute("action");h=S?zi(S,u):null,c=o.getAttribute("method")||uu,m=zf(o.getAttribute("enctype"))||cu,_=new FormData(o)}else if(Xv(o)||Kv(o)&&(o.type==="submit"||o.type==="image")){let S=o.form;if(S==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let w=o.getAttribute("formaction")||S.getAttribute("action");if(h=w?zi(w,u):null,c=o.getAttribute("formmethod")||S.getAttribute("method")||uu,m=zf(o.getAttribute("formenctype"))||zf(S.getAttribute("enctype"))||cu,_=new FormData(S,o),!Wv()){let{name:y,type:E,value:z}=o;if(E==="image"){let B=y?`${y}.`:"";_.append(`${B}x`,"0"),_.append(`${B}y`,"0")}else y&&_.append(y,z)}}else{if(xu(o))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');c=uu,h=null,m=cu,v=o}return _&&m==="text/plain"&&(v=_,_=void 0),{action:h,method:c.toLowerCase(),encType:m,formData:_,body:v}}function eh(o,u){if(o===!1||o===null||typeof o>"u")throw new Error(u)}async function ty(o,u){if(o.id in u)return u[o.id];try{let c=await import(o.module);return u[o.id]=c,c}catch(c){return console.error(`Error loading route module \`${o.module}\`, reloading page...`),console.error(c),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function ey(o){return o==null?!1:o.href==null?o.rel==="preload"&&typeof o.imageSrcSet=="string"&&typeof o.imageSizes=="string":typeof o.rel=="string"&&typeof o.href=="string"}async function ny(o,u,c){let h=await Promise.all(o.map(async m=>{let _=u.routes[m.route.id];if(_){let v=await ty(_,c);return v.links?v.links():[]}return[]}));return oy(h.flat(1).filter(ey).filter(m=>m.rel==="stylesheet"||m.rel==="preload").map(m=>m.rel==="stylesheet"?{...m,rel:"prefetch",as:"style"}:{...m,rel:"prefetch"}))}function dp(o,u,c,h,m,_){let v=(w,y)=>c[y]?w.route.id!==c[y].route.id:!0,S=(w,y)=>c[y].pathname!==w.pathname||c[y].route.path?.endsWith("*")&&c[y].params["*"]!==w.params["*"];return _==="assets"?u.filter((w,y)=>v(w,y)||S(w,y)):_==="data"?u.filter((w,y)=>{let E=h.routes[w.route.id];if(!E||!E.hasLoader)return!1;if(v(w,y)||S(w,y))return!0;if(w.route.shouldRevalidate){let z=w.route.shouldRevalidate({currentUrl:new URL(m.pathname+m.search+m.hash,window.origin),currentParams:c[0]?.params||{},nextUrl:new URL(o,window.origin),nextParams:w.params,defaultShouldRevalidate:!0});if(typeof z=="boolean")return z}return!0}):[]}function iy(o,u,{includeHydrateFallback:c}={}){return ay(o.map(h=>{let m=u.routes[h.route.id];if(!m)return[];let _=[m.module];return m.clientActionModule&&(_=_.concat(m.clientActionModule)),m.clientLoaderModule&&(_=_.concat(m.clientLoaderModule)),c&&m.hydrateFallbackModule&&(_=_.concat(m.hydrateFallbackModule)),m.imports&&(_=_.concat(m.imports)),_}).flat(1))}function ay(o){return[...new Set(o)]}function ry(o){let u={},c=Object.keys(o).sort();for(let h of c)u[h]=o[h];return u}function oy(o,u){let c=new Set;return new Set(u),o.reduce((h,m)=>{let _=JSON.stringify(ry(m));return c.has(_)||(c.add(_),h.push({key:_,link:m})),h},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var sy=new Set([100,101,204,205]);function ly(o,u){let c=typeof o=="string"?new URL(o,typeof window>"u"?"server://singlefetch/":window.location.origin):o;return c.pathname==="/"?c.pathname="_root.data":u&&zi(c.pathname,u)==="/"?c.pathname=`${u.replace(/\/$/,"")}/_root.data`:c.pathname=`${c.pathname.replace(/\/$/,"")}.data`,c}function Gp(){let o=M.useContext(so);return eh(o,"You must render this element inside a <DataRouterContext.Provider> element"),o}function uy(){let o=M.useContext(yu);return eh(o,"You must render this element inside a <DataRouterStateContext.Provider> element"),o}var nh=M.createContext(void 0);nh.displayName="FrameworkContext";function Vp(){let o=M.useContext(nh);return eh(o,"You must render this element inside a <HydratedRouter> element"),o}function cy(o,u){let c=M.useContext(nh),[h,m]=M.useState(!1),[_,v]=M.useState(!1),{onFocus:S,onBlur:w,onMouseEnter:y,onMouseLeave:E,onTouchStart:z}=u,B=M.useRef(null);M.useEffect(()=>{if(o==="render"&&v(!0),o==="viewport"){let U=X=>{X.forEach(it=>{v(it.isIntersecting)})},Y=new IntersectionObserver(U,{threshold:.5});return B.current&&Y.observe(B.current),()=>{Y.disconnect()}}},[o]),M.useEffect(()=>{if(h){let U=setTimeout(()=>{v(!0)},100);return()=>{clearTimeout(U)}}},[h]);let J=()=>{m(!0)},N=()=>{m(!1),v(!1)};return c?o!=="intent"?[_,B,{}]:[_,B,{onFocus:vs(S,J),onBlur:vs(w,N),onMouseEnter:vs(y,J),onMouseLeave:vs(E,N),onTouchStart:vs(z,J)}]:[!1,B,{}]}function vs(o,u){return c=>{o&&o(c),c.defaultPrevented||u(c)}}function fy({page:o,...u}){let{router:c}=Gp(),h=M.useMemo(()=>zp(c.routes,o,c.basename),[c.routes,o,c.basename]);return h?M.createElement(dy,{page:o,matches:h,...u}):null}function hy(o){let{manifest:u,routeModules:c}=Vp(),[h,m]=M.useState([]);return M.useEffect(()=>{let _=!1;return ny(o,u,c).then(v=>{_||m(v)}),()=>{_=!0}},[o,u,c]),h}function dy({page:o,matches:u,...c}){let h=Bi(),{manifest:m,routeModules:_}=Vp(),{basename:v}=Gp(),{loaderData:S,matches:w}=uy(),y=M.useMemo(()=>dp(o,u,w,m,h,"data"),[o,u,w,m,h]),E=M.useMemo(()=>dp(o,u,w,m,h,"assets"),[o,u,w,m,h]),z=M.useMemo(()=>{if(o===h.pathname+h.search+h.hash)return[];let N=new Set,U=!1;if(u.forEach(X=>{let it=m.routes[X.route.id];!it||!it.hasLoader||(!y.some(et=>et.route.id===X.route.id)&&X.route.id in S&&_[X.route.id]?.shouldRevalidate||it.hasClientLoader?U=!0:N.add(X.route.id))}),N.size===0)return[];let Y=ly(o,v);return U&&N.size>0&&Y.searchParams.set("_routes",u.filter(X=>N.has(X.route.id)).map(X=>X.route.id).join(",")),[Y.pathname+Y.search]},[v,S,h,m,y,u,o,_]),B=M.useMemo(()=>iy(E,m),[E,m]),J=hy(E);return M.createElement(M.Fragment,null,z.map(N=>M.createElement("link",{key:N,rel:"prefetch",as:"fetch",href:N,...c})),B.map(N=>M.createElement("link",{key:N,rel:"modulepreload",href:N,...c})),J.map(({key:N,link:U})=>M.createElement("link",{key:N,...U})))}function my(...o){return u=>{o.forEach(c=>{typeof c=="function"?c(u):c!=null&&(c.current=u)})}}var Xp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Xp&&(window.__reactRouterVersion="7.6.3")}catch{}function py({basename:o,children:u,window:c}){let h=M.useRef();h.current==null&&(h.current=ev({window:c,v5Compat:!0}));let m=h.current,[_,v]=M.useState({action:m.action,location:m.location}),S=M.useCallback(w=>{M.startTransition(()=>v(w))},[v]);return M.useLayoutEffect(()=>m.listen(S),[m,S]),M.createElement(Gv,{basename:o,children:u,location:_.location,navigationType:_.action,navigator:m})}var Qp=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,we=M.forwardRef(function({onClick:u,discover:c="render",prefetch:h="none",relative:m,reloadDocument:_,replace:v,state:S,target:w,to:y,preventScrollReset:E,viewTransition:z,...B},J){let{basename:N}=M.useContext(In),U=typeof y=="string"&&Qp.test(y),Y,X=!1;if(typeof y=="string"&&U&&(Y=y,Xp))try{let jt=new URL(window.location.href),me=y.startsWith("//")?new URL(jt.protocol+y):new URL(y),Xt=zi(me.pathname,N);me.origin===jt.origin&&Xt!=null?y=Xt+me.search+me.hash:X=!0}catch{Wn(!1,`<Link to="${y}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let it=Av(y,{relative:m}),[et,xt,lt]=cy(h,B),Ct=yy(y,{replace:v,state:S,target:w,preventScrollReset:E,relative:m,viewTransition:z});function Nt(jt){u&&u(jt),jt.defaultPrevented||Ct(jt)}let Pt=M.createElement("a",{...B,...lt,href:Y||it,onClick:X||_?u:Nt,ref:my(J,xt),target:w,"data-discover":!U&&c==="render"?"true":void 0});return et&&!U?M.createElement(M.Fragment,null,Pt,M.createElement(fy,{page:it})):Pt});we.displayName="Link";var _y=M.forwardRef(function({"aria-current":u="page",caseSensitive:c=!1,className:h="",end:m=!1,style:_,to:v,viewTransition:S,children:w,...y},E){let z=Ls(v,{relative:y.relative}),B=Bi(),J=M.useContext(yu),{navigator:N,basename:U}=M.useContext(In),Y=J!=null&&Ey(z)&&S===!0,X=N.encodeLocation?N.encodeLocation(z).pathname:z.pathname,it=B.pathname,et=J&&J.navigation&&J.navigation.location?J.navigation.location.pathname:null;c||(it=it.toLowerCase(),et=et?et.toLowerCase():null,X=X.toLowerCase()),et&&U&&(et=zi(et,U)||et);const xt=X!=="/"&&X.endsWith("/")?X.length-1:X.length;let lt=it===X||!m&&it.startsWith(X)&&it.charAt(xt)==="/",Ct=et!=null&&(et===X||!m&&et.startsWith(X)&&et.charAt(X.length)==="/"),Nt={isActive:lt,isPending:Ct,isTransitioning:Y},Pt=lt?u:void 0,jt;typeof h=="function"?jt=h(Nt):jt=[h,lt?"active":null,Ct?"pending":null,Y?"transitioning":null].filter(Boolean).join(" ");let me=typeof _=="function"?_(Nt):_;return M.createElement(we,{...y,"aria-current":Pt,className:jt,ref:E,style:me,to:v,viewTransition:S},typeof w=="function"?w(Nt):w)});_y.displayName="NavLink";var gy=M.forwardRef(({discover:o="render",fetcherKey:u,navigate:c,reloadDocument:h,replace:m,state:_,method:v=uu,action:S,onSubmit:w,relative:y,preventScrollReset:E,viewTransition:z,...B},J)=>{let N=wy(),U=Sy(S,{relative:y}),Y=v.toLowerCase()==="get"?"get":"post",X=typeof S=="string"&&Qp.test(S),it=et=>{if(w&&w(et),et.defaultPrevented)return;et.preventDefault();let xt=et.nativeEvent.submitter,lt=xt?.getAttribute("formmethod")||v;N(xt||et.currentTarget,{fetcherKey:u,method:lt,navigate:c,replace:m,state:_,relative:y,preventScrollReset:E,viewTransition:z})};return M.createElement("form",{ref:J,method:Y,action:U,onSubmit:h?w:it,...B,"data-discover":!X&&o==="render"?"true":void 0})});gy.displayName="Form";function vy(o){return`${o} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Kp(o){let u=M.useContext(so);return ae(u,vy(o)),u}function yy(o,{target:u,replace:c,state:h,preventScrollReset:m,relative:_,viewTransition:v}={}){let S=bu(),w=Bi(),y=Ls(o,{relative:_});return M.useCallback(E=>{if(Fv(E,u)){E.preventDefault();let z=c!==void 0?c:ws(w)===ws(y);S(o,{replace:z,state:h,preventScrollReset:m,relative:_,viewTransition:v})}},[w,S,y,c,h,u,o,m,_,v])}var by=0,xy=()=>`__${String(++by)}__`;function wy(){let{router:o}=Kp("useSubmit"),{basename:u}=M.useContext(In),c=Hv();return M.useCallback(async(h,m={})=>{let{action:_,method:v,encType:S,formData:w,body:y}=$v(h,u);if(m.navigate===!1){let E=m.fetcherKey||xy();await o.fetch(E,c,m.action||_,{preventScrollReset:m.preventScrollReset,formData:w,body:y,formMethod:m.method||v,formEncType:m.encType||S,flushSync:m.flushSync})}else await o.navigate(m.action||_,{preventScrollReset:m.preventScrollReset,formData:w,body:y,formMethod:m.method||v,formEncType:m.encType||S,replace:m.replace,state:m.state,fromRouteId:c,flushSync:m.flushSync,viewTransition:m.viewTransition})},[o,u,c])}function Sy(o,{relative:u}={}){let{basename:c}=M.useContext(In),h=M.useContext($n);ae(h,"useFormAction must be used inside a RouteContext");let[m]=h.matches.slice(-1),_={...Ls(o||".",{relative:u})},v=Bi();if(o==null){_.search=v.search;let S=new URLSearchParams(_.search),w=S.getAll("index");if(w.some(E=>E==="")){S.delete("index"),w.filter(z=>z).forEach(z=>S.append("index",z));let E=S.toString();_.search=E?`?${E}`:""}}return(!o||o===".")&&m.route.index&&(_.search=_.search?_.search.replace(/^\?/,"?index&"):"?index"),c!=="/"&&(_.pathname=_.pathname==="/"?c:Ni([c,_.pathname])),ws(_)}function Ey(o,u={}){let c=M.useContext(Zp);ae(c!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:h}=Kp("useViewTransitionState"),m=Ls(o,{relative:u.relative});if(!c.isTransitioning)return!1;let _=zi(c.currentLocation.pathname,h)||c.currentLocation.pathname,v=zi(c.nextLocation.pathname,h)||c.nextLocation.pathname;return pu(m.pathname,v)!=null||pu(m.pathname,_)!=null}[...sy];var Ty=Np();let Ly={data:""},Oy=o=>typeof window=="object"?((o?o.querySelector("#_goober"):window._goober)||Object.assign((o||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:o||Ly,Ay=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Ry=/\/\*[^]*?\*\/|  +/g,mp=/\n+/g,Ca=(o,u)=>{let c="",h="",m="";for(let _ in o){let v=o[_];_[0]=="@"?_[1]=="i"?c=_+" "+v+";":h+=_[1]=="f"?Ca(v,_):_+"{"+Ca(v,_[1]=="k"?"":u)+"}":typeof v=="object"?h+=Ca(v,u?u.replace(/([^,])+/g,S=>_.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,w=>/&/.test(w)?w.replace(/&/g,S):S?S+" "+w:w)):_):v!=null&&(_=/^--/.test(_)?_:_.replace(/[A-Z]/g,"-$&").toLowerCase(),m+=Ca.p?Ca.p(_,v):_+":"+v+";")}return c+(u&&m?u+"{"+m+"}":m)+h},Ci={},Jp=o=>{if(typeof o=="object"){let u="";for(let c in o)u+=c+Jp(o[c]);return u}return o},My=(o,u,c,h,m)=>{let _=Jp(o),v=Ci[_]||(Ci[_]=(w=>{let y=0,E=11;for(;y<w.length;)E=101*E+w.charCodeAt(y++)>>>0;return"go"+E})(_));if(!Ci[v]){let w=_!==o?o:(y=>{let E,z,B=[{}];for(;E=Ay.exec(y.replace(Ry,""));)E[4]?B.shift():E[3]?(z=E[3].replace(mp," ").trim(),B.unshift(B[0][z]=B[0][z]||{})):B[0][E[1]]=E[2].replace(mp," ").trim();return B[0]})(o);Ci[v]=Ca(m?{["@keyframes "+v]:w}:w,c?"":"."+v)}let S=c&&Ci.g?Ci.g:null;return c&&(Ci.g=Ci[v]),((w,y,E,z)=>{z?y.data=y.data.replace(z,w):y.data.indexOf(w)===-1&&(y.data=E?w+y.data:y.data+w)})(Ci[v],u,h,S),v},Cy=(o,u,c)=>o.reduce((h,m,_)=>{let v=u[_];if(v&&v.call){let S=v(c),w=S&&S.props&&S.props.className||/^go/.test(S)&&S;v=w?"."+w:S&&typeof S=="object"?S.props?"":Ca(S,""):S===!1?"":S}return h+m+(v??"")},"");function wu(o){let u=this||{},c=o.call?o(u.p):o;return My(c.unshift?c.raw?Cy(c,[].slice.call(arguments,1),u.p):c.reduce((h,m)=>Object.assign(h,m&&m.call?m(u.p):m),{}):c,Oy(u.target),u.g,u.o,u.k)}let Fp,Hf,kf;wu.bind({g:1});let Di=wu.bind({k:1});function Ny(o,u,c,h){Ca.p=u,Fp=o,Hf=c,kf=h}function Na(o,u){let c=this||{};return function(){let h=arguments;function m(_,v){let S=Object.assign({},_),w=S.className||m.className;c.p=Object.assign({theme:Hf&&Hf()},S),c.o=/ *go\d+/.test(w),S.className=wu.apply(c,h)+(w?" "+w:"");let y=o;return o[0]&&(y=S.as||o,delete S.as),kf&&y[0]&&kf(S),Fp(y,S)}return m}}var zy=o=>typeof o=="function",_u=(o,u)=>zy(o)?o(u):o,Dy=(()=>{let o=0;return()=>(++o).toString()})(),Wp=(()=>{let o;return()=>{if(o===void 0&&typeof window<"u"){let u=matchMedia("(prefers-reduced-motion: reduce)");o=!u||u.matches}return o}})(),By=20,Ip=(o,u)=>{switch(u.type){case 0:return{...o,toasts:[u.toast,...o.toasts].slice(0,By)};case 1:return{...o,toasts:o.toasts.map(_=>_.id===u.toast.id?{..._,...u.toast}:_)};case 2:let{toast:c}=u;return Ip(o,{type:o.toasts.find(_=>_.id===c.id)?1:0,toast:c});case 3:let{toastId:h}=u;return{...o,toasts:o.toasts.map(_=>_.id===h||h===void 0?{..._,dismissed:!0,visible:!1}:_)};case 4:return u.toastId===void 0?{...o,toasts:[]}:{...o,toasts:o.toasts.filter(_=>_.id!==u.toastId)};case 5:return{...o,pausedAt:u.time};case 6:let m=u.time-(o.pausedAt||0);return{...o,pausedAt:void 0,toasts:o.toasts.map(_=>({..._,pauseDuration:_.pauseDuration+m}))}}},fu=[],fr={toasts:[],pausedAt:void 0},pr=o=>{fr=Ip(fr,o),fu.forEach(u=>{u(fr)})},jy={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Uy=(o={})=>{let[u,c]=M.useState(fr),h=M.useRef(fr);M.useEffect(()=>(h.current!==fr&&c(fr),fu.push(c),()=>{let _=fu.indexOf(c);_>-1&&fu.splice(_,1)}),[]);let m=u.toasts.map(_=>{var v,S,w;return{...o,...o[_.type],..._,removeDelay:_.removeDelay||((v=o[_.type])==null?void 0:v.removeDelay)||o?.removeDelay,duration:_.duration||((S=o[_.type])==null?void 0:S.duration)||o?.duration||jy[_.type],style:{...o.style,...(w=o[_.type])==null?void 0:w.style,..._.style}}});return{...u,toasts:m}},Py=(o,u="blank",c)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:u,ariaProps:{role:"status","aria-live":"polite"},message:o,pauseDuration:0,...c,id:c?.id||Dy()}),Os=o=>(u,c)=>{let h=Py(u,o,c);return pr({type:2,toast:h}),h.id},Ge=(o,u)=>Os("blank")(o,u);Ge.error=Os("error");Ge.success=Os("success");Ge.loading=Os("loading");Ge.custom=Os("custom");Ge.dismiss=o=>{pr({type:3,toastId:o})};Ge.remove=o=>pr({type:4,toastId:o});Ge.promise=(o,u,c)=>{let h=Ge.loading(u.loading,{...c,...c?.loading});return typeof o=="function"&&(o=o()),o.then(m=>{let _=u.success?_u(u.success,m):void 0;return _?Ge.success(_,{id:h,...c,...c?.success}):Ge.dismiss(h),m}).catch(m=>{let _=u.error?_u(u.error,m):void 0;_?Ge.error(_,{id:h,...c,...c?.error}):Ge.dismiss(h)}),o};var Zy=(o,u)=>{pr({type:1,toast:{id:o,height:u}})},Hy=()=>{pr({type:5,time:Date.now()})},xs=new Map,ky=1e3,qy=(o,u=ky)=>{if(xs.has(o))return;let c=setTimeout(()=>{xs.delete(o),pr({type:4,toastId:o})},u);xs.set(o,c)},Yy=o=>{let{toasts:u,pausedAt:c}=Uy(o);M.useEffect(()=>{if(c)return;let _=Date.now(),v=u.map(S=>{if(S.duration===1/0)return;let w=(S.duration||0)+S.pauseDuration-(_-S.createdAt);if(w<0){S.visible&&Ge.dismiss(S.id);return}return setTimeout(()=>Ge.dismiss(S.id),w)});return()=>{v.forEach(S=>S&&clearTimeout(S))}},[u,c]);let h=M.useCallback(()=>{c&&pr({type:6,time:Date.now()})},[c]),m=M.useCallback((_,v)=>{let{reverseOrder:S=!1,gutter:w=8,defaultPosition:y}=v||{},E=u.filter(J=>(J.position||y)===(_.position||y)&&J.height),z=E.findIndex(J=>J.id===_.id),B=E.filter((J,N)=>N<z&&J.visible).length;return E.filter(J=>J.visible).slice(...S?[B+1]:[0,B]).reduce((J,N)=>J+(N.height||0)+w,0)},[u]);return M.useEffect(()=>{u.forEach(_=>{if(_.dismissed)qy(_.id,_.removeDelay);else{let v=xs.get(_.id);v&&(clearTimeout(v),xs.delete(_.id))}})},[u]),{toasts:u,handlers:{updateHeight:Zy,startPause:Hy,endPause:h,calculateOffset:m}}},Gy=Di`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Vy=Di`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Xy=Di`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Qy=Na("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${o=>o.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Gy} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Vy} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${o=>o.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Xy} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Ky=Di`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Jy=Na("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${o=>o.secondary||"#e0e0e0"};
  border-right-color: ${o=>o.primary||"#616161"};
  animation: ${Ky} 1s linear infinite;
`,Fy=Di`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Wy=Di`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Iy=Na("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${o=>o.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Fy} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Wy} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${o=>o.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,$y=Na("div")`
  position: absolute;
`,t0=Na("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,e0=Di`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,n0=Na("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${e0} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,i0=({toast:o})=>{let{icon:u,type:c,iconTheme:h}=o;return u!==void 0?typeof u=="string"?M.createElement(n0,null,u):u:c==="blank"?null:M.createElement(t0,null,M.createElement(Jy,{...h}),c!=="loading"&&M.createElement($y,null,c==="error"?M.createElement(Qy,{...h}):M.createElement(Iy,{...h})))},a0=o=>`
0% {transform: translate3d(0,${o*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,r0=o=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${o*-150}%,-1px) scale(.6); opacity:0;}
`,o0="0%{opacity:0;} 100%{opacity:1;}",s0="0%{opacity:1;} 100%{opacity:0;}",l0=Na("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,u0=Na("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,c0=(o,u)=>{let c=o.includes("top")?1:-1,[h,m]=Wp()?[o0,s0]:[a0(c),r0(c)];return{animation:u?`${Di(h)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Di(m)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},f0=M.memo(({toast:o,position:u,style:c,children:h})=>{let m=o.height?c0(o.position||u||"top-center",o.visible):{opacity:0},_=M.createElement(i0,{toast:o}),v=M.createElement(u0,{...o.ariaProps},_u(o.message,o));return M.createElement(l0,{className:o.className,style:{...m,...c,...o.style}},typeof h=="function"?h({icon:_,message:v}):M.createElement(M.Fragment,null,_,v))});Ny(M.createElement);var h0=({id:o,className:u,style:c,onHeightUpdate:h,children:m})=>{let _=M.useCallback(v=>{if(v){let S=()=>{let w=v.getBoundingClientRect().height;h(o,w)};S(),new MutationObserver(S).observe(v,{subtree:!0,childList:!0,characterData:!0})}},[o,h]);return M.createElement("div",{ref:_,className:u,style:c},m)},d0=(o,u)=>{let c=o.includes("top"),h=c?{top:0}:{bottom:0},m=o.includes("center")?{justifyContent:"center"}:o.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:Wp()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${u*(c?1:-1)}px)`,...h,...m}},m0=wu`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,lu=16,p0=({reverseOrder:o,position:u="top-center",toastOptions:c,gutter:h,children:m,containerStyle:_,containerClassName:v})=>{let{toasts:S,handlers:w}=Yy(c);return M.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:lu,left:lu,right:lu,bottom:lu,pointerEvents:"none",..._},className:v,onMouseEnter:w.startPause,onMouseLeave:w.endPause},S.map(y=>{let E=y.position||u,z=w.calculateOffset(y,{reverseOrder:o,gutter:h,defaultPosition:u}),B=d0(E,z);return M.createElement(h0,{id:y.id,key:y.id,onHeightUpdate:w.updateHeight,className:y.visible?m0:"",style:B},y.type==="custom"?_u(y.message,y):m?m(y):M.createElement(f0,{toast:y,position:E}))}))},cr=Ge;function $p(o,u){return function(){return o.apply(u,arguments)}}const{toString:_0}=Object.prototype,{getPrototypeOf:ih}=Object,{iterator:Su,toStringTag:t_}=Symbol,Eu=(o=>u=>{const c=_0.call(u);return o[c]||(o[c]=c.slice(8,-1).toLowerCase())})(Object.create(null)),Bn=o=>(o=o.toLowerCase(),u=>Eu(u)===o),Tu=o=>u=>typeof u===o,{isArray:lo}=Array,Ss=Tu("undefined");function g0(o){return o!==null&&!Ss(o)&&o.constructor!==null&&!Ss(o.constructor)&&Ie(o.constructor.isBuffer)&&o.constructor.isBuffer(o)}const e_=Bn("ArrayBuffer");function v0(o){let u;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?u=ArrayBuffer.isView(o):u=o&&o.buffer&&e_(o.buffer),u}const y0=Tu("string"),Ie=Tu("function"),n_=Tu("number"),Lu=o=>o!==null&&typeof o=="object",b0=o=>o===!0||o===!1,hu=o=>{if(Eu(o)!=="object")return!1;const u=ih(o);return(u===null||u===Object.prototype||Object.getPrototypeOf(u)===null)&&!(t_ in o)&&!(Su in o)},x0=Bn("Date"),w0=Bn("File"),S0=Bn("Blob"),E0=Bn("FileList"),T0=o=>Lu(o)&&Ie(o.pipe),L0=o=>{let u;return o&&(typeof FormData=="function"&&o instanceof FormData||Ie(o.append)&&((u=Eu(o))==="formdata"||u==="object"&&Ie(o.toString)&&o.toString()==="[object FormData]"))},O0=Bn("URLSearchParams"),[A0,R0,M0,C0]=["ReadableStream","Request","Response","Headers"].map(Bn),N0=o=>o.trim?o.trim():o.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function As(o,u,{allOwnKeys:c=!1}={}){if(o===null||typeof o>"u")return;let h,m;if(typeof o!="object"&&(o=[o]),lo(o))for(h=0,m=o.length;h<m;h++)u.call(null,o[h],h,o);else{const _=c?Object.getOwnPropertyNames(o):Object.keys(o),v=_.length;let S;for(h=0;h<v;h++)S=_[h],u.call(null,o[S],S,o)}}function i_(o,u){u=u.toLowerCase();const c=Object.keys(o);let h=c.length,m;for(;h-- >0;)if(m=c[h],u===m.toLowerCase())return m;return null}const hr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,a_=o=>!Ss(o)&&o!==hr;function qf(){const{caseless:o}=a_(this)&&this||{},u={},c=(h,m)=>{const _=o&&i_(u,m)||m;hu(u[_])&&hu(h)?u[_]=qf(u[_],h):hu(h)?u[_]=qf({},h):lo(h)?u[_]=h.slice():u[_]=h};for(let h=0,m=arguments.length;h<m;h++)arguments[h]&&As(arguments[h],c);return u}const z0=(o,u,c,{allOwnKeys:h}={})=>(As(u,(m,_)=>{c&&Ie(m)?o[_]=$p(m,c):o[_]=m},{allOwnKeys:h}),o),D0=o=>(o.charCodeAt(0)===65279&&(o=o.slice(1)),o),B0=(o,u,c,h)=>{o.prototype=Object.create(u.prototype,h),o.prototype.constructor=o,Object.defineProperty(o,"super",{value:u.prototype}),c&&Object.assign(o.prototype,c)},j0=(o,u,c,h)=>{let m,_,v;const S={};if(u=u||{},o==null)return u;do{for(m=Object.getOwnPropertyNames(o),_=m.length;_-- >0;)v=m[_],(!h||h(v,o,u))&&!S[v]&&(u[v]=o[v],S[v]=!0);o=c!==!1&&ih(o)}while(o&&(!c||c(o,u))&&o!==Object.prototype);return u},U0=(o,u,c)=>{o=String(o),(c===void 0||c>o.length)&&(c=o.length),c-=u.length;const h=o.indexOf(u,c);return h!==-1&&h===c},P0=o=>{if(!o)return null;if(lo(o))return o;let u=o.length;if(!n_(u))return null;const c=new Array(u);for(;u-- >0;)c[u]=o[u];return c},Z0=(o=>u=>o&&u instanceof o)(typeof Uint8Array<"u"&&ih(Uint8Array)),H0=(o,u)=>{const h=(o&&o[Su]).call(o);let m;for(;(m=h.next())&&!m.done;){const _=m.value;u.call(o,_[0],_[1])}},k0=(o,u)=>{let c;const h=[];for(;(c=o.exec(u))!==null;)h.push(c);return h},q0=Bn("HTMLFormElement"),Y0=o=>o.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(c,h,m){return h.toUpperCase()+m}),pp=(({hasOwnProperty:o})=>(u,c)=>o.call(u,c))(Object.prototype),G0=Bn("RegExp"),r_=(o,u)=>{const c=Object.getOwnPropertyDescriptors(o),h={};As(c,(m,_)=>{let v;(v=u(m,_,o))!==!1&&(h[_]=v||m)}),Object.defineProperties(o,h)},V0=o=>{r_(o,(u,c)=>{if(Ie(o)&&["arguments","caller","callee"].indexOf(c)!==-1)return!1;const h=o[c];if(Ie(h)){if(u.enumerable=!1,"writable"in u){u.writable=!1;return}u.set||(u.set=()=>{throw Error("Can not rewrite read-only method '"+c+"'")})}})},X0=(o,u)=>{const c={},h=m=>{m.forEach(_=>{c[_]=!0})};return lo(o)?h(o):h(String(o).split(u)),c},Q0=()=>{},K0=(o,u)=>o!=null&&Number.isFinite(o=+o)?o:u;function J0(o){return!!(o&&Ie(o.append)&&o[t_]==="FormData"&&o[Su])}const F0=o=>{const u=new Array(10),c=(h,m)=>{if(Lu(h)){if(u.indexOf(h)>=0)return;if(!("toJSON"in h)){u[m]=h;const _=lo(h)?[]:{};return As(h,(v,S)=>{const w=c(v,m+1);!Ss(w)&&(_[S]=w)}),u[m]=void 0,_}}return h};return c(o,0)},W0=Bn("AsyncFunction"),I0=o=>o&&(Lu(o)||Ie(o))&&Ie(o.then)&&Ie(o.catch),o_=((o,u)=>o?setImmediate:u?((c,h)=>(hr.addEventListener("message",({source:m,data:_})=>{m===hr&&_===c&&h.length&&h.shift()()},!1),m=>{h.push(m),hr.postMessage(c,"*")}))(`axios@${Math.random()}`,[]):c=>setTimeout(c))(typeof setImmediate=="function",Ie(hr.postMessage)),$0=typeof queueMicrotask<"u"?queueMicrotask.bind(hr):typeof process<"u"&&process.nextTick||o_,tb=o=>o!=null&&Ie(o[Su]),G={isArray:lo,isArrayBuffer:e_,isBuffer:g0,isFormData:L0,isArrayBufferView:v0,isString:y0,isNumber:n_,isBoolean:b0,isObject:Lu,isPlainObject:hu,isReadableStream:A0,isRequest:R0,isResponse:M0,isHeaders:C0,isUndefined:Ss,isDate:x0,isFile:w0,isBlob:S0,isRegExp:G0,isFunction:Ie,isStream:T0,isURLSearchParams:O0,isTypedArray:Z0,isFileList:E0,forEach:As,merge:qf,extend:z0,trim:N0,stripBOM:D0,inherits:B0,toFlatObject:j0,kindOf:Eu,kindOfTest:Bn,endsWith:U0,toArray:P0,forEachEntry:H0,matchAll:k0,isHTMLForm:q0,hasOwnProperty:pp,hasOwnProp:pp,reduceDescriptors:r_,freezeMethods:V0,toObjectSet:X0,toCamelCase:Y0,noop:Q0,toFiniteNumber:K0,findKey:i_,global:hr,isContextDefined:a_,isSpecCompliantForm:J0,toJSONObject:F0,isAsyncFn:W0,isThenable:I0,setImmediate:o_,asap:$0,isIterable:tb};function wt(o,u,c,h,m){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=o,this.name="AxiosError",u&&(this.code=u),c&&(this.config=c),h&&(this.request=h),m&&(this.response=m,this.status=m.status?m.status:null)}G.inherits(wt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:G.toJSONObject(this.config),code:this.code,status:this.status}}});const s_=wt.prototype,l_={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(o=>{l_[o]={value:o}});Object.defineProperties(wt,l_);Object.defineProperty(s_,"isAxiosError",{value:!0});wt.from=(o,u,c,h,m,_)=>{const v=Object.create(s_);return G.toFlatObject(o,v,function(w){return w!==Error.prototype},S=>S!=="isAxiosError"),wt.call(v,o.message,u,c,h,m),v.cause=o,v.name=o.name,_&&Object.assign(v,_),v};const eb=null;function Yf(o){return G.isPlainObject(o)||G.isArray(o)}function u_(o){return G.endsWith(o,"[]")?o.slice(0,-2):o}function _p(o,u,c){return o?o.concat(u).map(function(m,_){return m=u_(m),!c&&_?"["+m+"]":m}).join(c?".":""):u}function nb(o){return G.isArray(o)&&!o.some(Yf)}const ib=G.toFlatObject(G,{},null,function(u){return/^is[A-Z]/.test(u)});function Ou(o,u,c){if(!G.isObject(o))throw new TypeError("target must be an object");u=u||new FormData,c=G.toFlatObject(c,{metaTokens:!0,dots:!1,indexes:!1},!1,function(U,Y){return!G.isUndefined(Y[U])});const h=c.metaTokens,m=c.visitor||E,_=c.dots,v=c.indexes,w=(c.Blob||typeof Blob<"u"&&Blob)&&G.isSpecCompliantForm(u);if(!G.isFunction(m))throw new TypeError("visitor must be a function");function y(N){if(N===null)return"";if(G.isDate(N))return N.toISOString();if(G.isBoolean(N))return N.toString();if(!w&&G.isBlob(N))throw new wt("Blob is not supported. Use a Buffer instead.");return G.isArrayBuffer(N)||G.isTypedArray(N)?w&&typeof Blob=="function"?new Blob([N]):Buffer.from(N):N}function E(N,U,Y){let X=N;if(N&&!Y&&typeof N=="object"){if(G.endsWith(U,"{}"))U=h?U:U.slice(0,-2),N=JSON.stringify(N);else if(G.isArray(N)&&nb(N)||(G.isFileList(N)||G.endsWith(U,"[]"))&&(X=G.toArray(N)))return U=u_(U),X.forEach(function(et,xt){!(G.isUndefined(et)||et===null)&&u.append(v===!0?_p([U],xt,_):v===null?U:U+"[]",y(et))}),!1}return Yf(N)?!0:(u.append(_p(Y,U,_),y(N)),!1)}const z=[],B=Object.assign(ib,{defaultVisitor:E,convertValue:y,isVisitable:Yf});function J(N,U){if(!G.isUndefined(N)){if(z.indexOf(N)!==-1)throw Error("Circular reference detected in "+U.join("."));z.push(N),G.forEach(N,function(X,it){(!(G.isUndefined(X)||X===null)&&m.call(u,X,G.isString(it)?it.trim():it,U,B))===!0&&J(X,U?U.concat(it):[it])}),z.pop()}}if(!G.isObject(o))throw new TypeError("data must be an object");return J(o),u}function gp(o){const u={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(o).replace(/[!'()~]|%20|%00/g,function(h){return u[h]})}function ah(o,u){this._pairs=[],o&&Ou(o,this,u)}const c_=ah.prototype;c_.append=function(u,c){this._pairs.push([u,c])};c_.toString=function(u){const c=u?function(h){return u.call(this,h,gp)}:gp;return this._pairs.map(function(m){return c(m[0])+"="+c(m[1])},"").join("&")};function ab(o){return encodeURIComponent(o).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function f_(o,u,c){if(!u)return o;const h=c&&c.encode||ab;G.isFunction(c)&&(c={serialize:c});const m=c&&c.serialize;let _;if(m?_=m(u,c):_=G.isURLSearchParams(u)?u.toString():new ah(u,c).toString(h),_){const v=o.indexOf("#");v!==-1&&(o=o.slice(0,v)),o+=(o.indexOf("?")===-1?"?":"&")+_}return o}class vp{constructor(){this.handlers=[]}use(u,c,h){return this.handlers.push({fulfilled:u,rejected:c,synchronous:h?h.synchronous:!1,runWhen:h?h.runWhen:null}),this.handlers.length-1}eject(u){this.handlers[u]&&(this.handlers[u]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(u){G.forEach(this.handlers,function(h){h!==null&&u(h)})}}const h_={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},rb=typeof URLSearchParams<"u"?URLSearchParams:ah,ob=typeof FormData<"u"?FormData:null,sb=typeof Blob<"u"?Blob:null,lb={isBrowser:!0,classes:{URLSearchParams:rb,FormData:ob,Blob:sb},protocols:["http","https","file","blob","url","data"]},rh=typeof window<"u"&&typeof document<"u",Gf=typeof navigator=="object"&&navigator||void 0,ub=rh&&(!Gf||["ReactNative","NativeScript","NS"].indexOf(Gf.product)<0),cb=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",fb=rh&&window.location.href||"http://localhost",hb=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:rh,hasStandardBrowserEnv:ub,hasStandardBrowserWebWorkerEnv:cb,navigator:Gf,origin:fb},Symbol.toStringTag,{value:"Module"})),je={...hb,...lb};function db(o,u){return Ou(o,new je.classes.URLSearchParams,Object.assign({visitor:function(c,h,m,_){return je.isNode&&G.isBuffer(c)?(this.append(h,c.toString("base64")),!1):_.defaultVisitor.apply(this,arguments)}},u))}function mb(o){return G.matchAll(/\w+|\[(\w*)]/g,o).map(u=>u[0]==="[]"?"":u[1]||u[0])}function pb(o){const u={},c=Object.keys(o);let h;const m=c.length;let _;for(h=0;h<m;h++)_=c[h],u[_]=o[_];return u}function d_(o){function u(c,h,m,_){let v=c[_++];if(v==="__proto__")return!0;const S=Number.isFinite(+v),w=_>=c.length;return v=!v&&G.isArray(m)?m.length:v,w?(G.hasOwnProp(m,v)?m[v]=[m[v],h]:m[v]=h,!S):((!m[v]||!G.isObject(m[v]))&&(m[v]=[]),u(c,h,m[v],_)&&G.isArray(m[v])&&(m[v]=pb(m[v])),!S)}if(G.isFormData(o)&&G.isFunction(o.entries)){const c={};return G.forEachEntry(o,(h,m)=>{u(mb(h),m,c,0)}),c}return null}function _b(o,u,c){if(G.isString(o))try{return(u||JSON.parse)(o),G.trim(o)}catch(h){if(h.name!=="SyntaxError")throw h}return(c||JSON.stringify)(o)}const Rs={transitional:h_,adapter:["xhr","http","fetch"],transformRequest:[function(u,c){const h=c.getContentType()||"",m=h.indexOf("application/json")>-1,_=G.isObject(u);if(_&&G.isHTMLForm(u)&&(u=new FormData(u)),G.isFormData(u))return m?JSON.stringify(d_(u)):u;if(G.isArrayBuffer(u)||G.isBuffer(u)||G.isStream(u)||G.isFile(u)||G.isBlob(u)||G.isReadableStream(u))return u;if(G.isArrayBufferView(u))return u.buffer;if(G.isURLSearchParams(u))return c.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),u.toString();let S;if(_){if(h.indexOf("application/x-www-form-urlencoded")>-1)return db(u,this.formSerializer).toString();if((S=G.isFileList(u))||h.indexOf("multipart/form-data")>-1){const w=this.env&&this.env.FormData;return Ou(S?{"files[]":u}:u,w&&new w,this.formSerializer)}}return _||m?(c.setContentType("application/json",!1),_b(u)):u}],transformResponse:[function(u){const c=this.transitional||Rs.transitional,h=c&&c.forcedJSONParsing,m=this.responseType==="json";if(G.isResponse(u)||G.isReadableStream(u))return u;if(u&&G.isString(u)&&(h&&!this.responseType||m)){const v=!(c&&c.silentJSONParsing)&&m;try{return JSON.parse(u)}catch(S){if(v)throw S.name==="SyntaxError"?wt.from(S,wt.ERR_BAD_RESPONSE,this,null,this.response):S}}return u}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:je.classes.FormData,Blob:je.classes.Blob},validateStatus:function(u){return u>=200&&u<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};G.forEach(["delete","get","head","post","put","patch"],o=>{Rs.headers[o]={}});const gb=G.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),vb=o=>{const u={};let c,h,m;return o&&o.split(`
`).forEach(function(v){m=v.indexOf(":"),c=v.substring(0,m).trim().toLowerCase(),h=v.substring(m+1).trim(),!(!c||u[c]&&gb[c])&&(c==="set-cookie"?u[c]?u[c].push(h):u[c]=[h]:u[c]=u[c]?u[c]+", "+h:h)}),u},yp=Symbol("internals");function ys(o){return o&&String(o).trim().toLowerCase()}function du(o){return o===!1||o==null?o:G.isArray(o)?o.map(du):String(o)}function yb(o){const u=Object.create(null),c=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let h;for(;h=c.exec(o);)u[h[1]]=h[2];return u}const bb=o=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(o.trim());function Df(o,u,c,h,m){if(G.isFunction(h))return h.call(this,u,c);if(m&&(u=c),!!G.isString(u)){if(G.isString(h))return u.indexOf(h)!==-1;if(G.isRegExp(h))return h.test(u)}}function xb(o){return o.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(u,c,h)=>c.toUpperCase()+h)}function wb(o,u){const c=G.toCamelCase(" "+u);["get","set","has"].forEach(h=>{Object.defineProperty(o,h+c,{value:function(m,_,v){return this[h].call(this,u,m,_,v)},configurable:!0})})}let $e=class{constructor(u){u&&this.set(u)}set(u,c,h){const m=this;function _(S,w,y){const E=ys(w);if(!E)throw new Error("header name must be a non-empty string");const z=G.findKey(m,E);(!z||m[z]===void 0||y===!0||y===void 0&&m[z]!==!1)&&(m[z||w]=du(S))}const v=(S,w)=>G.forEach(S,(y,E)=>_(y,E,w));if(G.isPlainObject(u)||u instanceof this.constructor)v(u,c);else if(G.isString(u)&&(u=u.trim())&&!bb(u))v(vb(u),c);else if(G.isObject(u)&&G.isIterable(u)){let S={},w,y;for(const E of u){if(!G.isArray(E))throw TypeError("Object iterator must return a key-value pair");S[y=E[0]]=(w=S[y])?G.isArray(w)?[...w,E[1]]:[w,E[1]]:E[1]}v(S,c)}else u!=null&&_(c,u,h);return this}get(u,c){if(u=ys(u),u){const h=G.findKey(this,u);if(h){const m=this[h];if(!c)return m;if(c===!0)return yb(m);if(G.isFunction(c))return c.call(this,m,h);if(G.isRegExp(c))return c.exec(m);throw new TypeError("parser must be boolean|regexp|function")}}}has(u,c){if(u=ys(u),u){const h=G.findKey(this,u);return!!(h&&this[h]!==void 0&&(!c||Df(this,this[h],h,c)))}return!1}delete(u,c){const h=this;let m=!1;function _(v){if(v=ys(v),v){const S=G.findKey(h,v);S&&(!c||Df(h,h[S],S,c))&&(delete h[S],m=!0)}}return G.isArray(u)?u.forEach(_):_(u),m}clear(u){const c=Object.keys(this);let h=c.length,m=!1;for(;h--;){const _=c[h];(!u||Df(this,this[_],_,u,!0))&&(delete this[_],m=!0)}return m}normalize(u){const c=this,h={};return G.forEach(this,(m,_)=>{const v=G.findKey(h,_);if(v){c[v]=du(m),delete c[_];return}const S=u?xb(_):String(_).trim();S!==_&&delete c[_],c[S]=du(m),h[S]=!0}),this}concat(...u){return this.constructor.concat(this,...u)}toJSON(u){const c=Object.create(null);return G.forEach(this,(h,m)=>{h!=null&&h!==!1&&(c[m]=u&&G.isArray(h)?h.join(", "):h)}),c}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([u,c])=>u+": "+c).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(u){return u instanceof this?u:new this(u)}static concat(u,...c){const h=new this(u);return c.forEach(m=>h.set(m)),h}static accessor(u){const h=(this[yp]=this[yp]={accessors:{}}).accessors,m=this.prototype;function _(v){const S=ys(v);h[S]||(wb(m,v),h[S]=!0)}return G.isArray(u)?u.forEach(_):_(u),this}};$e.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);G.reduceDescriptors($e.prototype,({value:o},u)=>{let c=u[0].toUpperCase()+u.slice(1);return{get:()=>o,set(h){this[c]=h}}});G.freezeMethods($e);function Bf(o,u){const c=this||Rs,h=u||c,m=$e.from(h.headers);let _=h.data;return G.forEach(o,function(S){_=S.call(c,_,m.normalize(),u?u.status:void 0)}),m.normalize(),_}function m_(o){return!!(o&&o.__CANCEL__)}function uo(o,u,c){wt.call(this,o??"canceled",wt.ERR_CANCELED,u,c),this.name="CanceledError"}G.inherits(uo,wt,{__CANCEL__:!0});function p_(o,u,c){const h=c.config.validateStatus;!c.status||!h||h(c.status)?o(c):u(new wt("Request failed with status code "+c.status,[wt.ERR_BAD_REQUEST,wt.ERR_BAD_RESPONSE][Math.floor(c.status/100)-4],c.config,c.request,c))}function Sb(o){const u=/^([-+\w]{1,25})(:?\/\/|:)/.exec(o);return u&&u[1]||""}function Eb(o,u){o=o||10;const c=new Array(o),h=new Array(o);let m=0,_=0,v;return u=u!==void 0?u:1e3,function(w){const y=Date.now(),E=h[_];v||(v=y),c[m]=w,h[m]=y;let z=_,B=0;for(;z!==m;)B+=c[z++],z=z%o;if(m=(m+1)%o,m===_&&(_=(_+1)%o),y-v<u)return;const J=E&&y-E;return J?Math.round(B*1e3/J):void 0}}function Tb(o,u){let c=0,h=1e3/u,m,_;const v=(y,E=Date.now())=>{c=E,m=null,_&&(clearTimeout(_),_=null),o.apply(null,y)};return[(...y)=>{const E=Date.now(),z=E-c;z>=h?v(y,E):(m=y,_||(_=setTimeout(()=>{_=null,v(m)},h-z)))},()=>m&&v(m)]}const gu=(o,u,c=3)=>{let h=0;const m=Eb(50,250);return Tb(_=>{const v=_.loaded,S=_.lengthComputable?_.total:void 0,w=v-h,y=m(w),E=v<=S;h=v;const z={loaded:v,total:S,progress:S?v/S:void 0,bytes:w,rate:y||void 0,estimated:y&&S&&E?(S-v)/y:void 0,event:_,lengthComputable:S!=null,[u?"download":"upload"]:!0};o(z)},c)},bp=(o,u)=>{const c=o!=null;return[h=>u[0]({lengthComputable:c,total:o,loaded:h}),u[1]]},xp=o=>(...u)=>G.asap(()=>o(...u)),Lb=je.hasStandardBrowserEnv?((o,u)=>c=>(c=new URL(c,je.origin),o.protocol===c.protocol&&o.host===c.host&&(u||o.port===c.port)))(new URL(je.origin),je.navigator&&/(msie|trident)/i.test(je.navigator.userAgent)):()=>!0,Ob=je.hasStandardBrowserEnv?{write(o,u,c,h,m,_){const v=[o+"="+encodeURIComponent(u)];G.isNumber(c)&&v.push("expires="+new Date(c).toGMTString()),G.isString(h)&&v.push("path="+h),G.isString(m)&&v.push("domain="+m),_===!0&&v.push("secure"),document.cookie=v.join("; ")},read(o){const u=document.cookie.match(new RegExp("(^|;\\s*)("+o+")=([^;]*)"));return u?decodeURIComponent(u[3]):null},remove(o){this.write(o,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ab(o){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(o)}function Rb(o,u){return u?o.replace(/\/?\/$/,"")+"/"+u.replace(/^\/+/,""):o}function __(o,u,c){let h=!Ab(u);return o&&(h||c==!1)?Rb(o,u):u}const wp=o=>o instanceof $e?{...o}:o;function mr(o,u){u=u||{};const c={};function h(y,E,z,B){return G.isPlainObject(y)&&G.isPlainObject(E)?G.merge.call({caseless:B},y,E):G.isPlainObject(E)?G.merge({},E):G.isArray(E)?E.slice():E}function m(y,E,z,B){if(G.isUndefined(E)){if(!G.isUndefined(y))return h(void 0,y,z,B)}else return h(y,E,z,B)}function _(y,E){if(!G.isUndefined(E))return h(void 0,E)}function v(y,E){if(G.isUndefined(E)){if(!G.isUndefined(y))return h(void 0,y)}else return h(void 0,E)}function S(y,E,z){if(z in u)return h(y,E);if(z in o)return h(void 0,y)}const w={url:_,method:_,data:_,baseURL:v,transformRequest:v,transformResponse:v,paramsSerializer:v,timeout:v,timeoutMessage:v,withCredentials:v,withXSRFToken:v,adapter:v,responseType:v,xsrfCookieName:v,xsrfHeaderName:v,onUploadProgress:v,onDownloadProgress:v,decompress:v,maxContentLength:v,maxBodyLength:v,beforeRedirect:v,transport:v,httpAgent:v,httpsAgent:v,cancelToken:v,socketPath:v,responseEncoding:v,validateStatus:S,headers:(y,E,z)=>m(wp(y),wp(E),z,!0)};return G.forEach(Object.keys(Object.assign({},o,u)),function(E){const z=w[E]||m,B=z(o[E],u[E],E);G.isUndefined(B)&&z!==S||(c[E]=B)}),c}const g_=o=>{const u=mr({},o);let{data:c,withXSRFToken:h,xsrfHeaderName:m,xsrfCookieName:_,headers:v,auth:S}=u;u.headers=v=$e.from(v),u.url=f_(__(u.baseURL,u.url,u.allowAbsoluteUrls),o.params,o.paramsSerializer),S&&v.set("Authorization","Basic "+btoa((S.username||"")+":"+(S.password?unescape(encodeURIComponent(S.password)):"")));let w;if(G.isFormData(c)){if(je.hasStandardBrowserEnv||je.hasStandardBrowserWebWorkerEnv)v.setContentType(void 0);else if((w=v.getContentType())!==!1){const[y,...E]=w?w.split(";").map(z=>z.trim()).filter(Boolean):[];v.setContentType([y||"multipart/form-data",...E].join("; "))}}if(je.hasStandardBrowserEnv&&(h&&G.isFunction(h)&&(h=h(u)),h||h!==!1&&Lb(u.url))){const y=m&&_&&Ob.read(_);y&&v.set(m,y)}return u},Mb=typeof XMLHttpRequest<"u",Cb=Mb&&function(o){return new Promise(function(c,h){const m=g_(o);let _=m.data;const v=$e.from(m.headers).normalize();let{responseType:S,onUploadProgress:w,onDownloadProgress:y}=m,E,z,B,J,N;function U(){J&&J(),N&&N(),m.cancelToken&&m.cancelToken.unsubscribe(E),m.signal&&m.signal.removeEventListener("abort",E)}let Y=new XMLHttpRequest;Y.open(m.method.toUpperCase(),m.url,!0),Y.timeout=m.timeout;function X(){if(!Y)return;const et=$e.from("getAllResponseHeaders"in Y&&Y.getAllResponseHeaders()),lt={data:!S||S==="text"||S==="json"?Y.responseText:Y.response,status:Y.status,statusText:Y.statusText,headers:et,config:o,request:Y};p_(function(Nt){c(Nt),U()},function(Nt){h(Nt),U()},lt),Y=null}"onloadend"in Y?Y.onloadend=X:Y.onreadystatechange=function(){!Y||Y.readyState!==4||Y.status===0&&!(Y.responseURL&&Y.responseURL.indexOf("file:")===0)||setTimeout(X)},Y.onabort=function(){Y&&(h(new wt("Request aborted",wt.ECONNABORTED,o,Y)),Y=null)},Y.onerror=function(){h(new wt("Network Error",wt.ERR_NETWORK,o,Y)),Y=null},Y.ontimeout=function(){let xt=m.timeout?"timeout of "+m.timeout+"ms exceeded":"timeout exceeded";const lt=m.transitional||h_;m.timeoutErrorMessage&&(xt=m.timeoutErrorMessage),h(new wt(xt,lt.clarifyTimeoutError?wt.ETIMEDOUT:wt.ECONNABORTED,o,Y)),Y=null},_===void 0&&v.setContentType(null),"setRequestHeader"in Y&&G.forEach(v.toJSON(),function(xt,lt){Y.setRequestHeader(lt,xt)}),G.isUndefined(m.withCredentials)||(Y.withCredentials=!!m.withCredentials),S&&S!=="json"&&(Y.responseType=m.responseType),y&&([B,N]=gu(y,!0),Y.addEventListener("progress",B)),w&&Y.upload&&([z,J]=gu(w),Y.upload.addEventListener("progress",z),Y.upload.addEventListener("loadend",J)),(m.cancelToken||m.signal)&&(E=et=>{Y&&(h(!et||et.type?new uo(null,o,Y):et),Y.abort(),Y=null)},m.cancelToken&&m.cancelToken.subscribe(E),m.signal&&(m.signal.aborted?E():m.signal.addEventListener("abort",E)));const it=Sb(m.url);if(it&&je.protocols.indexOf(it)===-1){h(new wt("Unsupported protocol "+it+":",wt.ERR_BAD_REQUEST,o));return}Y.send(_||null)})},Nb=(o,u)=>{const{length:c}=o=o?o.filter(Boolean):[];if(u||c){let h=new AbortController,m;const _=function(y){if(!m){m=!0,S();const E=y instanceof Error?y:this.reason;h.abort(E instanceof wt?E:new uo(E instanceof Error?E.message:E))}};let v=u&&setTimeout(()=>{v=null,_(new wt(`timeout ${u} of ms exceeded`,wt.ETIMEDOUT))},u);const S=()=>{o&&(v&&clearTimeout(v),v=null,o.forEach(y=>{y.unsubscribe?y.unsubscribe(_):y.removeEventListener("abort",_)}),o=null)};o.forEach(y=>y.addEventListener("abort",_));const{signal:w}=h;return w.unsubscribe=()=>G.asap(S),w}},zb=function*(o,u){let c=o.byteLength;if(c<u){yield o;return}let h=0,m;for(;h<c;)m=h+u,yield o.slice(h,m),h=m},Db=async function*(o,u){for await(const c of Bb(o))yield*zb(c,u)},Bb=async function*(o){if(o[Symbol.asyncIterator]){yield*o;return}const u=o.getReader();try{for(;;){const{done:c,value:h}=await u.read();if(c)break;yield h}}finally{await u.cancel()}},Sp=(o,u,c,h)=>{const m=Db(o,u);let _=0,v,S=w=>{v||(v=!0,h&&h(w))};return new ReadableStream({async pull(w){try{const{done:y,value:E}=await m.next();if(y){S(),w.close();return}let z=E.byteLength;if(c){let B=_+=z;c(B)}w.enqueue(new Uint8Array(E))}catch(y){throw S(y),y}},cancel(w){return S(w),m.return()}},{highWaterMark:2})},Au=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",v_=Au&&typeof ReadableStream=="function",jb=Au&&(typeof TextEncoder=="function"?(o=>u=>o.encode(u))(new TextEncoder):async o=>new Uint8Array(await new Response(o).arrayBuffer())),y_=(o,...u)=>{try{return!!o(...u)}catch{return!1}},Ub=v_&&y_(()=>{let o=!1;const u=new Request(je.origin,{body:new ReadableStream,method:"POST",get duplex(){return o=!0,"half"}}).headers.has("Content-Type");return o&&!u}),Ep=64*1024,Vf=v_&&y_(()=>G.isReadableStream(new Response("").body)),vu={stream:Vf&&(o=>o.body)};Au&&(o=>{["text","arrayBuffer","blob","formData","stream"].forEach(u=>{!vu[u]&&(vu[u]=G.isFunction(o[u])?c=>c[u]():(c,h)=>{throw new wt(`Response type '${u}' is not supported`,wt.ERR_NOT_SUPPORT,h)})})})(new Response);const Pb=async o=>{if(o==null)return 0;if(G.isBlob(o))return o.size;if(G.isSpecCompliantForm(o))return(await new Request(je.origin,{method:"POST",body:o}).arrayBuffer()).byteLength;if(G.isArrayBufferView(o)||G.isArrayBuffer(o))return o.byteLength;if(G.isURLSearchParams(o)&&(o=o+""),G.isString(o))return(await jb(o)).byteLength},Zb=async(o,u)=>{const c=G.toFiniteNumber(o.getContentLength());return c??Pb(u)},Hb=Au&&(async o=>{let{url:u,method:c,data:h,signal:m,cancelToken:_,timeout:v,onDownloadProgress:S,onUploadProgress:w,responseType:y,headers:E,withCredentials:z="same-origin",fetchOptions:B}=g_(o);y=y?(y+"").toLowerCase():"text";let J=Nb([m,_&&_.toAbortSignal()],v),N;const U=J&&J.unsubscribe&&(()=>{J.unsubscribe()});let Y;try{if(w&&Ub&&c!=="get"&&c!=="head"&&(Y=await Zb(E,h))!==0){let lt=new Request(u,{method:"POST",body:h,duplex:"half"}),Ct;if(G.isFormData(h)&&(Ct=lt.headers.get("content-type"))&&E.setContentType(Ct),lt.body){const[Nt,Pt]=bp(Y,gu(xp(w)));h=Sp(lt.body,Ep,Nt,Pt)}}G.isString(z)||(z=z?"include":"omit");const X="credentials"in Request.prototype;N=new Request(u,{...B,signal:J,method:c.toUpperCase(),headers:E.normalize().toJSON(),body:h,duplex:"half",credentials:X?z:void 0});let it=await fetch(N,B);const et=Vf&&(y==="stream"||y==="response");if(Vf&&(S||et&&U)){const lt={};["status","statusText","headers"].forEach(jt=>{lt[jt]=it[jt]});const Ct=G.toFiniteNumber(it.headers.get("content-length")),[Nt,Pt]=S&&bp(Ct,gu(xp(S),!0))||[];it=new Response(Sp(it.body,Ep,Nt,()=>{Pt&&Pt(),U&&U()}),lt)}y=y||"text";let xt=await vu[G.findKey(vu,y)||"text"](it,o);return!et&&U&&U(),await new Promise((lt,Ct)=>{p_(lt,Ct,{data:xt,headers:$e.from(it.headers),status:it.status,statusText:it.statusText,config:o,request:N})})}catch(X){throw U&&U(),X&&X.name==="TypeError"&&/Load failed|fetch/i.test(X.message)?Object.assign(new wt("Network Error",wt.ERR_NETWORK,o,N),{cause:X.cause||X}):wt.from(X,X&&X.code,o,N)}}),Xf={http:eb,xhr:Cb,fetch:Hb};G.forEach(Xf,(o,u)=>{if(o){try{Object.defineProperty(o,"name",{value:u})}catch{}Object.defineProperty(o,"adapterName",{value:u})}});const Tp=o=>`- ${o}`,kb=o=>G.isFunction(o)||o===null||o===!1,b_={getAdapter:o=>{o=G.isArray(o)?o:[o];const{length:u}=o;let c,h;const m={};for(let _=0;_<u;_++){c=o[_];let v;if(h=c,!kb(c)&&(h=Xf[(v=String(c)).toLowerCase()],h===void 0))throw new wt(`Unknown adapter '${v}'`);if(h)break;m[v||"#"+_]=h}if(!h){const _=Object.entries(m).map(([S,w])=>`adapter ${S} `+(w===!1?"is not supported by the environment":"is not available in the build"));let v=u?_.length>1?`since :
`+_.map(Tp).join(`
`):" "+Tp(_[0]):"as no adapter specified";throw new wt("There is no suitable adapter to dispatch the request "+v,"ERR_NOT_SUPPORT")}return h},adapters:Xf};function jf(o){if(o.cancelToken&&o.cancelToken.throwIfRequested(),o.signal&&o.signal.aborted)throw new uo(null,o)}function Lp(o){return jf(o),o.headers=$e.from(o.headers),o.data=Bf.call(o,o.transformRequest),["post","put","patch"].indexOf(o.method)!==-1&&o.headers.setContentType("application/x-www-form-urlencoded",!1),b_.getAdapter(o.adapter||Rs.adapter)(o).then(function(h){return jf(o),h.data=Bf.call(o,o.transformResponse,h),h.headers=$e.from(h.headers),h},function(h){return m_(h)||(jf(o),h&&h.response&&(h.response.data=Bf.call(o,o.transformResponse,h.response),h.response.headers=$e.from(h.response.headers))),Promise.reject(h)})}const x_="1.10.0",Ru={};["object","boolean","number","function","string","symbol"].forEach((o,u)=>{Ru[o]=function(h){return typeof h===o||"a"+(u<1?"n ":" ")+o}});const Op={};Ru.transitional=function(u,c,h){function m(_,v){return"[Axios v"+x_+"] Transitional option '"+_+"'"+v+(h?". "+h:"")}return(_,v,S)=>{if(u===!1)throw new wt(m(v," has been removed"+(c?" in "+c:"")),wt.ERR_DEPRECATED);return c&&!Op[v]&&(Op[v]=!0,console.warn(m(v," has been deprecated since v"+c+" and will be removed in the near future"))),u?u(_,v,S):!0}};Ru.spelling=function(u){return(c,h)=>(console.warn(`${h} is likely a misspelling of ${u}`),!0)};function qb(o,u,c){if(typeof o!="object")throw new wt("options must be an object",wt.ERR_BAD_OPTION_VALUE);const h=Object.keys(o);let m=h.length;for(;m-- >0;){const _=h[m],v=u[_];if(v){const S=o[_],w=S===void 0||v(S,_,o);if(w!==!0)throw new wt("option "+_+" must be "+w,wt.ERR_BAD_OPTION_VALUE);continue}if(c!==!0)throw new wt("Unknown option "+_,wt.ERR_BAD_OPTION)}}const mu={assertOptions:qb,validators:Ru},Fn=mu.validators;let dr=class{constructor(u){this.defaults=u||{},this.interceptors={request:new vp,response:new vp}}async request(u,c){try{return await this._request(u,c)}catch(h){if(h instanceof Error){let m={};Error.captureStackTrace?Error.captureStackTrace(m):m=new Error;const _=m.stack?m.stack.replace(/^.+\n/,""):"";try{h.stack?_&&!String(h.stack).endsWith(_.replace(/^.+\n.+\n/,""))&&(h.stack+=`
`+_):h.stack=_}catch{}}throw h}}_request(u,c){typeof u=="string"?(c=c||{},c.url=u):c=u||{},c=mr(this.defaults,c);const{transitional:h,paramsSerializer:m,headers:_}=c;h!==void 0&&mu.assertOptions(h,{silentJSONParsing:Fn.transitional(Fn.boolean),forcedJSONParsing:Fn.transitional(Fn.boolean),clarifyTimeoutError:Fn.transitional(Fn.boolean)},!1),m!=null&&(G.isFunction(m)?c.paramsSerializer={serialize:m}:mu.assertOptions(m,{encode:Fn.function,serialize:Fn.function},!0)),c.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?c.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:c.allowAbsoluteUrls=!0),mu.assertOptions(c,{baseUrl:Fn.spelling("baseURL"),withXsrfToken:Fn.spelling("withXSRFToken")},!0),c.method=(c.method||this.defaults.method||"get").toLowerCase();let v=_&&G.merge(_.common,_[c.method]);_&&G.forEach(["delete","get","head","post","put","patch","common"],N=>{delete _[N]}),c.headers=$e.concat(v,_);const S=[];let w=!0;this.interceptors.request.forEach(function(U){typeof U.runWhen=="function"&&U.runWhen(c)===!1||(w=w&&U.synchronous,S.unshift(U.fulfilled,U.rejected))});const y=[];this.interceptors.response.forEach(function(U){y.push(U.fulfilled,U.rejected)});let E,z=0,B;if(!w){const N=[Lp.bind(this),void 0];for(N.unshift.apply(N,S),N.push.apply(N,y),B=N.length,E=Promise.resolve(c);z<B;)E=E.then(N[z++],N[z++]);return E}B=S.length;let J=c;for(z=0;z<B;){const N=S[z++],U=S[z++];try{J=N(J)}catch(Y){U.call(this,Y);break}}try{E=Lp.call(this,J)}catch(N){return Promise.reject(N)}for(z=0,B=y.length;z<B;)E=E.then(y[z++],y[z++]);return E}getUri(u){u=mr(this.defaults,u);const c=__(u.baseURL,u.url,u.allowAbsoluteUrls);return f_(c,u.params,u.paramsSerializer)}};G.forEach(["delete","get","head","options"],function(u){dr.prototype[u]=function(c,h){return this.request(mr(h||{},{method:u,url:c,data:(h||{}).data}))}});G.forEach(["post","put","patch"],function(u){function c(h){return function(_,v,S){return this.request(mr(S||{},{method:u,headers:h?{"Content-Type":"multipart/form-data"}:{},url:_,data:v}))}}dr.prototype[u]=c(),dr.prototype[u+"Form"]=c(!0)});let Yb=class w_{constructor(u){if(typeof u!="function")throw new TypeError("executor must be a function.");let c;this.promise=new Promise(function(_){c=_});const h=this;this.promise.then(m=>{if(!h._listeners)return;let _=h._listeners.length;for(;_-- >0;)h._listeners[_](m);h._listeners=null}),this.promise.then=m=>{let _;const v=new Promise(S=>{h.subscribe(S),_=S}).then(m);return v.cancel=function(){h.unsubscribe(_)},v},u(function(_,v,S){h.reason||(h.reason=new uo(_,v,S),c(h.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(u){if(this.reason){u(this.reason);return}this._listeners?this._listeners.push(u):this._listeners=[u]}unsubscribe(u){if(!this._listeners)return;const c=this._listeners.indexOf(u);c!==-1&&this._listeners.splice(c,1)}toAbortSignal(){const u=new AbortController,c=h=>{u.abort(h)};return this.subscribe(c),u.signal.unsubscribe=()=>this.unsubscribe(c),u.signal}static source(){let u;return{token:new w_(function(m){u=m}),cancel:u}}};function Gb(o){return function(c){return o.apply(null,c)}}function Vb(o){return G.isObject(o)&&o.isAxiosError===!0}const Qf={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Qf).forEach(([o,u])=>{Qf[u]=o});function S_(o){const u=new dr(o),c=$p(dr.prototype.request,u);return G.extend(c,dr.prototype,u,{allOwnKeys:!0}),G.extend(c,u,null,{allOwnKeys:!0}),c.create=function(m){return S_(mr(o,m))},c}const qt=S_(Rs);qt.Axios=dr;qt.CanceledError=uo;qt.CancelToken=Yb;qt.isCancel=m_;qt.VERSION=x_;qt.toFormData=Ou;qt.AxiosError=wt;qt.Cancel=qt.CanceledError;qt.all=function(u){return Promise.all(u)};qt.spread=Gb;qt.isAxiosError=Vb;qt.mergeConfig=mr;qt.AxiosHeaders=$e;qt.formToJSON=o=>d_(G.isHTMLForm(o)?new FormData(o):o);qt.getAdapter=b_.getAdapter;qt.HttpStatusCode=Qf;qt.default=qt;const{Axios:kx,AxiosError:qx,CanceledError:Yx,isCancel:Gx,CancelToken:Vx,VERSION:Xx,all:Qx,Cancel:Kx,isAxiosError:Jx,spread:Fx,toFormData:Wx,AxiosHeaders:Ix,HttpStatusCode:$x,formToJSON:t1,getAdapter:e1,mergeConfig:n1}=qt,E_=M.createContext(),oh=()=>{const o=M.useContext(E_);if(!o)throw new Error("useAuth must be used within an AuthProvider");return o},Xb=({children:o})=>{const[u,c]=M.useState(null),[h,m]=M.useState(!0),[_,v]=M.useState(localStorage.getItem("token"));M.useEffect(()=>{_?qt.defaults.headers.common.Authorization=`Bearer ${_}`:delete qt.defaults.headers.common.Authorization},[_]),M.useEffect(()=>{(async()=>{if(_)try{const J=await qt.get("http://localhost:5000/api/users/profile");c(J.data.data.user)}catch(J){console.error("Auth check failed:",J),y()}m(!1)})()},[_]);const S=async(B,J)=>{try{const N=await qt.post("http://localhost:5000/api/users/login",{email:B,password:J}),{user:U,token:Y}=N.data.data;return c(U),v(Y),localStorage.setItem("token",Y),cr.success("Login successful!"),{success:!0}}catch(N){const U=N.response?.data?.message||"Login failed";return cr.error(U),{success:!1,error:U}}},w=async B=>{try{const J=await qt.post("http://localhost:5000/api/users/register",B),{user:N,token:U}=J.data.data;return c(N),v(U),localStorage.setItem("token",U),cr.success("Registration successful!"),{success:!0}}catch(J){const N=J.response?.data?.message||"Registration failed";return cr.error(N),{success:!1,error:N}}},y=()=>{c(null),v(null),localStorage.removeItem("token"),delete qt.defaults.headers.common.Authorization,cr.success("Logged out successfully")},z={user:u,token:_,loading:h,login:S,register:w,logout:y,updateProfile:async B=>{try{const J=await qt.put("http://localhost:5000/api/users/profile",B);return c(J.data.data.user),cr.success("Profile updated successfully!"),{success:!0}}catch(J){const N=J.response?.data?.message||"Profile update failed";return cr.error(N),{success:!1,error:N}}},isAuthenticated:!!u};return T.jsx(E_.Provider,{value:z,children:o})};function Qb({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const Kb=M.forwardRef(Qb);function Jb({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const Kf=M.forwardRef(Jb);function Fb({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const Ap=M.forwardRef(Fb);function Wb({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const Ib=M.forwardRef(Wb);function $b({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const Rp=M.forwardRef($b);function tx({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))}const Jf=M.forwardRef(tx);function ex({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Ff=M.forwardRef(ex);function nx({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))}const ix=M.forwardRef(nx);function ax({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))}const rx=M.forwardRef(ax);function ox({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const T_=M.forwardRef(ox);function sx({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}const lx=M.forwardRef(sx);function ux({title:o,titleId:u,...c},h){return M.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":u},c),o?M.createElement("title",{id:u},o):null,M.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const cx=M.forwardRef(ux),fx=()=>{const{user:o,logout:u,isAuthenticated:c}=oh(),h=Bi(),m=bu(),[_,v]=M.useState(!1),S=()=>{u(),m("/"),v(!1)},w=[{name:"Home",href:"/",icon:ix},{name:"Report Issue",href:"/report",icon:T_},{name:"Dashboard",href:"/dashboard",icon:Kf}],y=E=>h.pathname===E;return T.jsxs("nav",{className:"bg-white shadow-lg border-b border-gray-200",children:[T.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:T.jsxs("div",{className:"flex justify-between h-16",children:[T.jsxs("div",{className:"flex",children:[T.jsx("div",{className:"flex-shrink-0 flex items-center",children:T.jsxs(we,{to:"/",className:"flex items-center space-x-2",children:[T.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:T.jsx("span",{className:"text-white font-bold text-lg",children:"C"})}),T.jsx("span",{className:"text-xl font-bold text-gray-900",children:"CityPulse"})]})}),T.jsx("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:w.map(E=>{const z=E.icon;return T.jsxs(we,{to:E.href,className:`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${y(E.href)?"border-blue-500 text-gray-900":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"}`,children:[T.jsx(z,{className:"w-4 h-4 mr-2"}),E.name]},E.name)})})]}),T.jsx("div",{className:"hidden sm:ml-6 sm:flex sm:items-center",children:c?T.jsxs("div",{className:"flex items-center space-x-4",children:[T.jsxs("span",{className:"text-sm text-gray-700",children:["Welcome, ",o?.firstName||o?.username]}),T.jsxs(we,{to:"/profile",className:`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md ${y("/profile")?"text-blue-700 bg-blue-100":"text-gray-500 hover:text-gray-700"}`,children:[T.jsx(lx,{className:"w-4 h-4 mr-2"}),"Profile"]}),T.jsx("button",{onClick:S,className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 hover:text-gray-700",children:"Logout"})]}):T.jsxs("div",{className:"flex items-center space-x-4",children:[T.jsx(we,{to:"/login",className:"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium",children:"Login"}),T.jsx(we,{to:"/register",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Sign Up"})]})}),T.jsx("div",{className:"sm:hidden flex items-center",children:T.jsx("button",{onClick:()=>v(!_),className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",children:_?T.jsx(cx,{className:"block h-6 w-6"}):T.jsx(Kb,{className:"block h-6 w-6"})})})]})}),_&&T.jsxs("div",{className:"sm:hidden",children:[T.jsx("div",{className:"pt-2 pb-3 space-y-1",children:w.map(E=>{const z=E.icon;return T.jsx(we,{to:E.href,onClick:()=>v(!1),className:`block pl-3 pr-4 py-2 border-l-4 text-base font-medium ${y(E.href)?"bg-blue-50 border-blue-500 text-blue-700":"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700"}`,children:T.jsxs("div",{className:"flex items-center",children:[T.jsx(z,{className:"w-5 h-5 mr-3"}),E.name]})},E.name)})}),T.jsx("div",{className:"pt-4 pb-3 border-t border-gray-200",children:c?T.jsxs("div",{className:"space-y-1",children:[T.jsxs("div",{className:"px-4 py-2",children:[T.jsxs("div",{className:"text-base font-medium text-gray-800",children:[o?.firstName," ",o?.lastName]}),T.jsx("div",{className:"text-sm font-medium text-gray-500",children:o?.email})]}),T.jsx(we,{to:"/profile",onClick:()=>v(!1),className:"block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100",children:"Profile"}),T.jsx("button",{onClick:S,className:"block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100",children:"Logout"})]}):T.jsxs("div",{className:"space-y-1",children:[T.jsx(we,{to:"/login",onClick:()=>v(!1),className:"block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100",children:"Login"}),T.jsx(we,{to:"/register",onClick:()=>v(!1),className:"block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100",children:"Sign Up"})]})})]})]})};function L_(o,u){const c=M.useRef(u);M.useEffect(function(){u!==c.current&&o.attributionControl!=null&&(c.current!=null&&o.attributionControl.removeAttribution(c.current),u!=null&&o.attributionControl.addAttribution(u)),c.current=u},[o,u])}const hx=1;function dx(o){return Object.freeze({__version:hx,map:o})}function mx(o,u){return Object.freeze({...o,...u})}const sh=M.createContext(null);function O_(){const o=M.use(sh);if(o==null)throw new Error("No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>");return o}function px(o){function u(c,h){const{instance:m,context:_}=o(c).current;M.useImperativeHandle(h,()=>m);const{children:v}=c;return v==null?null:Uf.createElement(sh,{value:_},v)}return M.forwardRef(u)}function _x(o){function u(c,h){const[m,_]=M.useState(!1),{instance:v}=o(c,_).current;M.useImperativeHandle(h,()=>v),M.useEffect(function(){m&&v.update()},[v,m,c.children]);const S=v._contentNode;return S?Ty.createPortal(c.children,S):null}return M.forwardRef(u)}function gx(o){function u(c,h){const{instance:m}=o(c).current;return M.useImperativeHandle(h,()=>m),null}return M.forwardRef(u)}function A_(o,u){const c=M.useRef(void 0);M.useEffect(function(){return u!=null&&o.instance.on(u),c.current=u,function(){c.current!=null&&o.instance.off(c.current),c.current=null}},[o,u])}function lh(o,u){const c=o.pane??u.pane;return c?{...o,pane:c}:o}function vx(o,u){return function(h,m){const _=O_(),v=o(lh(h,_),_);return L_(_.map,h.attribution),A_(v.current,h.eventHandlers),u(v.current,_,h,m),v}}var bs={exports:{}};/* @preserve
 * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com
 * (c) 2010-2023 Vladimir Agafonkin, (c) 2010-2011 CloudMade
 */var yx=bs.exports,Mp;function bx(){return Mp||(Mp=1,function(o,u){(function(c,h){h(u)})(yx,function(c){var h="1.9.4";function m(n){var a,s,d,p;for(s=1,d=arguments.length;s<d;s++){p=arguments[s];for(a in p)n[a]=p[a]}return n}var _=Object.create||function(){function n(){}return function(a){return n.prototype=a,new n}}();function v(n,a){var s=Array.prototype.slice;if(n.bind)return n.bind.apply(n,s.call(arguments,1));var d=s.call(arguments,2);return function(){return n.apply(a,d.length?d.concat(s.call(arguments)):arguments)}}var S=0;function w(n){return"_leaflet_id"in n||(n._leaflet_id=++S),n._leaflet_id}function y(n,a,s){var d,p,b,A;return A=function(){d=!1,p&&(b.apply(s,p),p=!1)},b=function(){d?p=arguments:(n.apply(s,arguments),setTimeout(A,a),d=!0)},b}function E(n,a,s){var d=a[1],p=a[0],b=d-p;return n===d&&s?n:((n-p)%b+b)%b+p}function z(){return!1}function B(n,a){if(a===!1)return n;var s=Math.pow(10,a===void 0?6:a);return Math.round(n*s)/s}function J(n){return n.trim?n.trim():n.replace(/^\s+|\s+$/g,"")}function N(n){return J(n).split(/\s+/)}function U(n,a){Object.prototype.hasOwnProperty.call(n,"options")||(n.options=n.options?_(n.options):{});for(var s in a)n.options[s]=a[s];return n.options}function Y(n,a,s){var d=[];for(var p in n)d.push(encodeURIComponent(s?p.toUpperCase():p)+"="+encodeURIComponent(n[p]));return(!a||a.indexOf("?")===-1?"?":"&")+d.join("&")}var X=/\{ *([\w_ -]+) *\}/g;function it(n,a){return n.replace(X,function(s,d){var p=a[d];if(p===void 0)throw new Error("No value provided for variable "+s);return typeof p=="function"&&(p=p(a)),p})}var et=Array.isArray||function(n){return Object.prototype.toString.call(n)==="[object Array]"};function xt(n,a){for(var s=0;s<n.length;s++)if(n[s]===a)return s;return-1}var lt="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function Ct(n){return window["webkit"+n]||window["moz"+n]||window["ms"+n]}var Nt=0;function Pt(n){var a=+new Date,s=Math.max(0,16-(a-Nt));return Nt=a+s,window.setTimeout(n,s)}var jt=window.requestAnimationFrame||Ct("RequestAnimationFrame")||Pt,me=window.cancelAnimationFrame||Ct("CancelAnimationFrame")||Ct("CancelRequestAnimationFrame")||function(n){window.clearTimeout(n)};function Xt(n,a,s){if(s&&jt===Pt)n.call(a);else return jt.call(window,v(n,a))}function Zt(n){n&&me.call(window,n)}var ti={__proto__:null,extend:m,create:_,bind:v,get lastId(){return S},stamp:w,throttle:y,wrapNum:E,falseFn:z,formatNum:B,trim:J,splitWords:N,setOptions:U,getParamString:Y,template:it,isArray:et,indexOf:xt,emptyImageUrl:lt,requestFn:jt,cancelFn:me,requestAnimFrame:Xt,cancelAnimFrame:Zt};function Se(){}Se.extend=function(n){var a=function(){U(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},s=a.__super__=this.prototype,d=_(s);d.constructor=a,a.prototype=d;for(var p in this)Object.prototype.hasOwnProperty.call(this,p)&&p!=="prototype"&&p!=="__super__"&&(a[p]=this[p]);return n.statics&&m(a,n.statics),n.includes&&(pe(n.includes),m.apply(null,[d].concat(n.includes))),m(d,n),delete d.statics,delete d.includes,d.options&&(d.options=s.options?_(s.options):{},m(d.options,n.options)),d._initHooks=[],d.callInitHooks=function(){if(!this._initHooksCalled){s.callInitHooks&&s.callInitHooks.call(this),this._initHooksCalled=!0;for(var b=0,A=d._initHooks.length;b<A;b++)d._initHooks[b].call(this)}},a},Se.include=function(n){var a=this.prototype.options;return m(this.prototype,n),n.options&&(this.prototype.options=a,this.mergeOptions(n.options)),this},Se.mergeOptions=function(n){return m(this.prototype.options,n),this},Se.addInitHook=function(n){var a=Array.prototype.slice.call(arguments,1),s=typeof n=="function"?n:function(){this[n].apply(this,a)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(s),this};function pe(n){if(!(typeof L>"u"||!L||!L.Mixin)){n=et(n)?n:[n];for(var a=0;a<n.length;a++)n[a]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var V={on:function(n,a,s){if(typeof n=="object")for(var d in n)this._on(d,n[d],a);else{n=N(n);for(var p=0,b=n.length;p<b;p++)this._on(n[p],a,s)}return this},off:function(n,a,s){if(!arguments.length)delete this._events;else if(typeof n=="object")for(var d in n)this._off(d,n[d],a);else{n=N(n);for(var p=arguments.length===1,b=0,A=n.length;b<A;b++)p?this._off(n[b]):this._off(n[b],a,s)}return this},_on:function(n,a,s,d){if(typeof a!="function"){console.warn("wrong listener type: "+typeof a);return}if(this._listens(n,a,s)===!1){s===this&&(s=void 0);var p={fn:a,ctx:s};d&&(p.once=!0),this._events=this._events||{},this._events[n]=this._events[n]||[],this._events[n].push(p)}},_off:function(n,a,s){var d,p,b;if(this._events&&(d=this._events[n],!!d)){if(arguments.length===1){if(this._firingCount)for(p=0,b=d.length;p<b;p++)d[p].fn=z;delete this._events[n];return}if(typeof a!="function"){console.warn("wrong listener type: "+typeof a);return}var A=this._listens(n,a,s);if(A!==!1){var j=d[A];this._firingCount&&(j.fn=z,this._events[n]=d=d.slice()),d.splice(A,1)}}},fire:function(n,a,s){if(!this.listens(n,s))return this;var d=m({},a,{type:n,target:this,sourceTarget:a&&a.sourceTarget||this});if(this._events){var p=this._events[n];if(p){this._firingCount=this._firingCount+1||1;for(var b=0,A=p.length;b<A;b++){var j=p[b],q=j.fn;j.once&&this.off(n,q,j.ctx),q.call(j.ctx||this,d)}this._firingCount--}}return s&&this._propagateEvent(d),this},listens:function(n,a,s,d){typeof n!="string"&&console.warn('"string" type argument expected');var p=a;typeof a!="function"&&(d=!!a,p=void 0,s=void 0);var b=this._events&&this._events[n];if(b&&b.length&&this._listens(n,p,s)!==!1)return!0;if(d){for(var A in this._eventParents)if(this._eventParents[A].listens(n,a,s,d))return!0}return!1},_listens:function(n,a,s){if(!this._events)return!1;var d=this._events[n]||[];if(!a)return!!d.length;s===this&&(s=void 0);for(var p=0,b=d.length;p<b;p++)if(d[p].fn===a&&d[p].ctx===s)return p;return!1},once:function(n,a,s){if(typeof n=="object")for(var d in n)this._on(d,n[d],a,!0);else{n=N(n);for(var p=0,b=n.length;p<b;p++)this._on(n[p],a,s,!0)}return this},addEventParent:function(n){return this._eventParents=this._eventParents||{},this._eventParents[w(n)]=n,this},removeEventParent:function(n){return this._eventParents&&delete this._eventParents[w(n)],this},_propagateEvent:function(n){for(var a in this._eventParents)this._eventParents[a].fire(n.type,m({layer:n.target,propagatedFrom:n.target},n),!0)}};V.addEventListener=V.on,V.removeEventListener=V.clearAllEventListeners=V.off,V.addOneTimeEventListener=V.once,V.fireEvent=V.fire,V.hasEventListeners=V.listens;var rt=Se.extend(V);function $(n,a,s){this.x=s?Math.round(n):n,this.y=s?Math.round(a):a}var Ut=Math.trunc||function(n){return n>0?Math.floor(n):Math.ceil(n)};$.prototype={clone:function(){return new $(this.x,this.y)},add:function(n){return this.clone()._add(O(n))},_add:function(n){return this.x+=n.x,this.y+=n.y,this},subtract:function(n){return this.clone()._subtract(O(n))},_subtract:function(n){return this.x-=n.x,this.y-=n.y,this},divideBy:function(n){return this.clone()._divideBy(n)},_divideBy:function(n){return this.x/=n,this.y/=n,this},multiplyBy:function(n){return this.clone()._multiplyBy(n)},_multiplyBy:function(n){return this.x*=n,this.y*=n,this},scaleBy:function(n){return new $(this.x*n.x,this.y*n.y)},unscaleBy:function(n){return new $(this.x/n.x,this.y/n.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=Ut(this.x),this.y=Ut(this.y),this},distanceTo:function(n){n=O(n);var a=n.x-this.x,s=n.y-this.y;return Math.sqrt(a*a+s*s)},equals:function(n){return n=O(n),n.x===this.x&&n.y===this.y},contains:function(n){return n=O(n),Math.abs(n.x)<=Math.abs(this.x)&&Math.abs(n.y)<=Math.abs(this.y)},toString:function(){return"Point("+B(this.x)+", "+B(this.y)+")"}};function O(n,a,s){return n instanceof $?n:et(n)?new $(n[0],n[1]):n==null?n:typeof n=="object"&&"x"in n&&"y"in n?new $(n.x,n.y):new $(n,a,s)}function Q(n,a){if(n)for(var s=a?[n,a]:n,d=0,p=s.length;d<p;d++)this.extend(s[d])}Q.prototype={extend:function(n){var a,s;if(!n)return this;if(n instanceof $||typeof n[0]=="number"||"x"in n)a=s=O(n);else if(n=nt(n),a=n.min,s=n.max,!a||!s)return this;return!this.min&&!this.max?(this.min=a.clone(),this.max=s.clone()):(this.min.x=Math.min(a.x,this.min.x),this.max.x=Math.max(s.x,this.max.x),this.min.y=Math.min(a.y,this.min.y),this.max.y=Math.max(s.y,this.max.y)),this},getCenter:function(n){return O((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,n)},getBottomLeft:function(){return O(this.min.x,this.max.y)},getTopRight:function(){return O(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(n){var a,s;return typeof n[0]=="number"||n instanceof $?n=O(n):n=nt(n),n instanceof Q?(a=n.min,s=n.max):a=s=n,a.x>=this.min.x&&s.x<=this.max.x&&a.y>=this.min.y&&s.y<=this.max.y},intersects:function(n){n=nt(n);var a=this.min,s=this.max,d=n.min,p=n.max,b=p.x>=a.x&&d.x<=s.x,A=p.y>=a.y&&d.y<=s.y;return b&&A},overlaps:function(n){n=nt(n);var a=this.min,s=this.max,d=n.min,p=n.max,b=p.x>a.x&&d.x<s.x,A=p.y>a.y&&d.y<s.y;return b&&A},isValid:function(){return!!(this.min&&this.max)},pad:function(n){var a=this.min,s=this.max,d=Math.abs(a.x-s.x)*n,p=Math.abs(a.y-s.y)*n;return nt(O(a.x-d,a.y-p),O(s.x+d,s.y+p))},equals:function(n){return n?(n=nt(n),this.min.equals(n.getTopLeft())&&this.max.equals(n.getBottomRight())):!1}};function nt(n,a){return!n||n instanceof Q?n:new Q(n,a)}function tt(n,a){if(n)for(var s=a?[n,a]:n,d=0,p=s.length;d<p;d++)this.extend(s[d])}tt.prototype={extend:function(n){var a=this._southWest,s=this._northEast,d,p;if(n instanceof ft)d=n,p=n;else if(n instanceof tt){if(d=n._southWest,p=n._northEast,!d||!p)return this}else return n?this.extend(at(n)||st(n)):this;return!a&&!s?(this._southWest=new ft(d.lat,d.lng),this._northEast=new ft(p.lat,p.lng)):(a.lat=Math.min(d.lat,a.lat),a.lng=Math.min(d.lng,a.lng),s.lat=Math.max(p.lat,s.lat),s.lng=Math.max(p.lng,s.lng)),this},pad:function(n){var a=this._southWest,s=this._northEast,d=Math.abs(a.lat-s.lat)*n,p=Math.abs(a.lng-s.lng)*n;return new tt(new ft(a.lat-d,a.lng-p),new ft(s.lat+d,s.lng+p))},getCenter:function(){return new ft((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new ft(this.getNorth(),this.getWest())},getSouthEast:function(){return new ft(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(n){typeof n[0]=="number"||n instanceof ft||"lat"in n?n=at(n):n=st(n);var a=this._southWest,s=this._northEast,d,p;return n instanceof tt?(d=n.getSouthWest(),p=n.getNorthEast()):d=p=n,d.lat>=a.lat&&p.lat<=s.lat&&d.lng>=a.lng&&p.lng<=s.lng},intersects:function(n){n=st(n);var a=this._southWest,s=this._northEast,d=n.getSouthWest(),p=n.getNorthEast(),b=p.lat>=a.lat&&d.lat<=s.lat,A=p.lng>=a.lng&&d.lng<=s.lng;return b&&A},overlaps:function(n){n=st(n);var a=this._southWest,s=this._northEast,d=n.getSouthWest(),p=n.getNorthEast(),b=p.lat>a.lat&&d.lat<s.lat,A=p.lng>a.lng&&d.lng<s.lng;return b&&A},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(n,a){return n?(n=st(n),this._southWest.equals(n.getSouthWest(),a)&&this._northEast.equals(n.getNorthEast(),a)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function st(n,a){return n instanceof tt?n:new tt(n,a)}function ft(n,a,s){if(isNaN(n)||isNaN(a))throw new Error("Invalid LatLng object: ("+n+", "+a+")");this.lat=+n,this.lng=+a,s!==void 0&&(this.alt=+s)}ft.prototype={equals:function(n,a){if(!n)return!1;n=at(n);var s=Math.max(Math.abs(this.lat-n.lat),Math.abs(this.lng-n.lng));return s<=(a===void 0?1e-9:a)},toString:function(n){return"LatLng("+B(this.lat,n)+", "+B(this.lng,n)+")"},distanceTo:function(n){return zt.distance(this,at(n))},wrap:function(){return zt.wrapLatLng(this)},toBounds:function(n){var a=180*n/40075017,s=a/Math.cos(Math.PI/180*this.lat);return st([this.lat-a,this.lng-s],[this.lat+a,this.lng+s])},clone:function(){return new ft(this.lat,this.lng,this.alt)}};function at(n,a,s){return n instanceof ft?n:et(n)&&typeof n[0]!="object"?n.length===3?new ft(n[0],n[1],n[2]):n.length===2?new ft(n[0],n[1]):null:n==null?n:typeof n=="object"&&"lat"in n?new ft(n.lat,"lng"in n?n.lng:n.lon,n.alt):a===void 0?null:new ft(n,a,s)}var te={latLngToPoint:function(n,a){var s=this.projection.project(n),d=this.scale(a);return this.transformation._transform(s,d)},pointToLatLng:function(n,a){var s=this.scale(a),d=this.transformation.untransform(n,s);return this.projection.unproject(d)},project:function(n){return this.projection.project(n)},unproject:function(n){return this.projection.unproject(n)},scale:function(n){return 256*Math.pow(2,n)},zoom:function(n){return Math.log(n/256)/Math.LN2},getProjectedBounds:function(n){if(this.infinite)return null;var a=this.projection.bounds,s=this.scale(n),d=this.transformation.transform(a.min,s),p=this.transformation.transform(a.max,s);return new Q(d,p)},infinite:!1,wrapLatLng:function(n){var a=this.wrapLng?E(n.lng,this.wrapLng,!0):n.lng,s=this.wrapLat?E(n.lat,this.wrapLat,!0):n.lat,d=n.alt;return new ft(s,a,d)},wrapLatLngBounds:function(n){var a=n.getCenter(),s=this.wrapLatLng(a),d=a.lat-s.lat,p=a.lng-s.lng;if(d===0&&p===0)return n;var b=n.getSouthWest(),A=n.getNorthEast(),j=new ft(b.lat-d,b.lng-p),q=new ft(A.lat-d,A.lng-p);return new tt(j,q)}},zt=m({},te,{wrapLng:[-180,180],R:6371e3,distance:function(n,a){var s=Math.PI/180,d=n.lat*s,p=a.lat*s,b=Math.sin((a.lat-n.lat)*s/2),A=Math.sin((a.lng-n.lng)*s/2),j=b*b+Math.cos(d)*Math.cos(p)*A*A,q=2*Math.atan2(Math.sqrt(j),Math.sqrt(1-j));return this.R*q}}),En=6378137,za={R:En,MAX_LATITUDE:85.0511287798,project:function(n){var a=Math.PI/180,s=this.MAX_LATITUDE,d=Math.max(Math.min(s,n.lat),-s),p=Math.sin(d*a);return new $(this.R*n.lng*a,this.R*Math.log((1+p)/(1-p))/2)},unproject:function(n){var a=180/Math.PI;return new ft((2*Math.atan(Math.exp(n.y/this.R))-Math.PI/2)*a,n.x*a/this.R)},bounds:function(){var n=En*Math.PI;return new Q([-n,-n],[n,n])}()};function ji(n,a,s,d){if(et(n)){this._a=n[0],this._b=n[1],this._c=n[2],this._d=n[3];return}this._a=n,this._b=a,this._c=s,this._d=d}ji.prototype={transform:function(n,a){return this._transform(n.clone(),a)},_transform:function(n,a){return a=a||1,n.x=a*(this._a*n.x+this._b),n.y=a*(this._c*n.y+this._d),n},untransform:function(n,a){return a=a||1,new $((n.x/a-this._b)/this._a,(n.y/a-this._d)/this._c)}};function ei(n,a,s,d){return new ji(n,a,s,d)}var Da=m({},zt,{code:"EPSG:3857",projection:za,transformation:function(){var n=.5/(Math.PI*za.R);return ei(n,.5,-n,.5)}()}),co=m({},Da,{code:"EPSG:900913"});function Cs(n){return document.createElementNS("http://www.w3.org/2000/svg",n)}function Ns(n,a){var s="",d,p,b,A,j,q;for(d=0,b=n.length;d<b;d++){for(j=n[d],p=0,A=j.length;p<A;p++)q=j[p],s+=(p?"L":"M")+q.x+" "+q.y;s+=a?dt.svg?"z":"x":""}return s||"M0 0"}var Ve=document.documentElement.style,_r="ActiveXObject"in window,zs=_r&&!document.addEventListener,fo="msLaunchUri"in navigator&&!("documentMode"in document),Ui=Xe("webkit"),Ds=Xe("android"),ho=Xe("android 2")||Xe("android 3"),Mu=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),Cu=Ds&&Xe("Google")&&Mu<537&&!("AudioNode"in window),ni=!!window.opera,Me=!fo&&Xe("chrome"),Tn=Xe("gecko")&&!Ui&&!ni&&!_r,Ue=!Me&&Xe("safari"),Bs=Xe("phantom"),js="OTransition"in Ve,Nu=navigator.platform.indexOf("Win")===0,Ba=_r&&"transition"in Ve,Pi="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!ho,jn="MozPerspective"in Ve,gr=!window.L_DISABLE_3D&&(Ba||Pi||jn)&&!js&&!Bs,Ln=typeof orientation<"u"||Xe("mobile"),zu=Ln&&Ui,Us=Ln&&Pi,mo=!window.PointerEvent&&window.MSPointerEvent,vr=!!(window.PointerEvent||mo),Zi="ontouchstart"in window||!!window.TouchEvent,Du=!window.L_NO_TOUCH&&(Zi||vr),Ps=Ln&&ni,Zs=Ln&&Tn,po=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,_o=function(){var n=!1;try{var a=Object.defineProperty({},"passive",{get:function(){n=!0}});window.addEventListener("testPassiveEventSupport",z,a),window.removeEventListener("testPassiveEventSupport",z,a)}catch{}return n}(),Hs=function(){return!!document.createElement("canvas").getContext}(),go=!!(document.createElementNS&&Cs("svg").createSVGRect),Un=!!go&&function(){var n=document.createElement("div");return n.innerHTML="<svg/>",(n.firstChild&&n.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),_e=!go&&function(){try{var n=document.createElement("div");n.innerHTML='<v:shape adj="1"/>';var a=n.firstChild;return a.style.behavior="url(#default#VML)",a&&typeof a.adj=="object"}catch{return!1}}(),Ce=navigator.platform.indexOf("Mac")===0,Hi=navigator.platform.indexOf("Linux")===0;function Xe(n){return navigator.userAgent.toLowerCase().indexOf(n)>=0}var dt={ie:_r,ielt9:zs,edge:fo,webkit:Ui,android:Ds,android23:ho,androidStock:Cu,opera:ni,chrome:Me,gecko:Tn,safari:Ue,phantom:Bs,opera12:js,win:Nu,ie3d:Ba,webkit3d:Pi,gecko3d:jn,any3d:gr,mobile:Ln,mobileWebkit:zu,mobileWebkit3d:Us,msPointer:mo,pointer:vr,touch:Du,touchNative:Zi,mobileOpera:Ps,mobileGecko:Zs,retina:po,passiveEvents:_o,canvas:Hs,svg:go,vml:_e,inlineSvg:Un,mac:Ce,linux:Hi},ks=dt.msPointer?"MSPointerDown":"pointerdown",vo=dt.msPointer?"MSPointerMove":"pointermove",ki=dt.msPointer?"MSPointerUp":"pointerup",yr=dt.msPointer?"MSPointerCancel":"pointercancel",Pn={touchstart:ks,touchmove:vo,touchend:ki,touchcancel:yr},ii={touchstart:Gs,touchmove:ja,touchend:ja,touchcancel:ja},dn={},ai=!1;function ue(n,a,s){return a==="touchstart"&&Bu(),ii[a]?(s=ii[a].bind(this,s),n.addEventListener(Pn[a],s,!1),s):(console.warn("wrong event specified:",a),z)}function qs(n,a,s){if(!Pn[a]){console.warn("wrong event specified:",a);return}n.removeEventListener(Pn[a],s,!1)}function Ys(n){dn[n.pointerId]=n}function ri(n){dn[n.pointerId]&&(dn[n.pointerId]=n)}function oi(n){delete dn[n.pointerId]}function Bu(){ai||(document.addEventListener(ks,Ys,!0),document.addEventListener(vo,ri,!0),document.addEventListener(ki,oi,!0),document.addEventListener(yr,oi,!0),ai=!0)}function ja(n,a){if(a.pointerType!==(a.MSPOINTER_TYPE_MOUSE||"mouse")){a.touches=[];for(var s in dn)a.touches.push(dn[s]);a.changedTouches=[a],n(a)}}function Gs(n,a){a.MSPOINTER_TYPE_TOUCH&&a.pointerType===a.MSPOINTER_TYPE_TOUCH&&re(a),ja(n,a)}function ju(n){var a={},s,d;for(d in n)s=n[d],a[d]=s&&s.bind?s.bind(n):s;return n=a,a.type="dblclick",a.detail=2,a.isTrusted=!1,a._simulated=!0,a}var br=200;function xr(n,a){n.addEventListener("dblclick",a);var s=0,d;function p(b){if(b.detail!==1){d=b.detail;return}if(!(b.pointerType==="mouse"||b.sourceCapabilities&&!b.sourceCapabilities.firesTouchEvents)){var A=xo(b);if(!(A.some(function(q){return q instanceof HTMLLabelElement&&q.attributes.for})&&!A.some(function(q){return q instanceof HTMLInputElement||q instanceof HTMLSelectElement}))){var j=Date.now();j-s<=br?(d++,d===2&&a(ju(b))):d=1,s=j}}}return n.addEventListener("click",p),{dblclick:a,simDblclick:p}}function On(n,a){n.removeEventListener("dblclick",a.dblclick),n.removeEventListener("click",a.simDblclick)}var Ua=Vi(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),qi=Vi(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),si=qi==="webkitTransition"||qi==="OTransition"?qi+"End":"transitionend";function wr(n){return typeof n=="string"?document.getElementById(n):n}function li(n,a){var s=n.style[a]||n.currentStyle&&n.currentStyle[a];if((!s||s==="auto")&&document.defaultView){var d=document.defaultView.getComputedStyle(n,null);s=d?d[a]:null}return s==="auto"?null:s}function Rt(n,a,s){var d=document.createElement(n);return d.className=a||"",s&&s.appendChild(d),d}function Kt(n){var a=n.parentNode;a&&a.removeChild(n)}function Ee(n){for(;n.firstChild;)n.removeChild(n.firstChild)}function ui(n){var a=n.parentNode;a&&a.lastChild!==n&&a.appendChild(n)}function Yi(n){var a=n.parentNode;a&&a.firstChild!==n&&a.insertBefore(n,a.firstChild)}function Gi(n,a){if(n.classList!==void 0)return n.classList.contains(a);var s=Te(n);return s.length>0&&new RegExp("(^|\\s)"+a+"(\\s|$)").test(s)}function bt(n,a){if(n.classList!==void 0)for(var s=N(a),d=0,p=s.length;d<p;d++)n.classList.add(s[d]);else if(!Gi(n,a)){var b=Te(n);yo(n,(b?b+" ":"")+a)}}function Jt(n,a){n.classList!==void 0?n.classList.remove(a):yo(n,J((" "+Te(n)+" ").replace(" "+a+" "," ")))}function yo(n,a){n.className.baseVal===void 0?n.className=a:n.className.baseVal=a}function Te(n){return n.correspondingElement&&(n=n.correspondingElement),n.className.baseVal===void 0?n.className:n.className.baseVal}function Ne(n,a){"opacity"in n.style?n.style.opacity=a:"filter"in n.style&&Vs(n,a)}function Vs(n,a){var s=!1,d="DXImageTransform.Microsoft.Alpha";try{s=n.filters.item(d)}catch{if(a===1)return}a=Math.round(a*100),s?(s.Enabled=a!==100,s.Opacity=a):n.style.filter+=" progid:"+d+"(opacity="+a+")"}function Vi(n){for(var a=document.documentElement.style,s=0;s<n.length;s++)if(n[s]in a)return n[s];return!1}function Qe(n,a,s){var d=a||new $(0,0);n.style[Ua]=(dt.ie3d?"translate("+d.x+"px,"+d.y+"px)":"translate3d("+d.x+"px,"+d.y+"px,0)")+(s?" scale("+s+")":"")}function ee(n,a){n._leaflet_pos=a,dt.any3d?Qe(n,a):(n.style.left=a.x+"px",n.style.top=a.y+"px")}function Zn(n){return n._leaflet_pos||new $(0,0)}var mn,Pa,Sr;if("onselectstart"in document)mn=function(){yt(window,"selectstart",re)},Pa=function(){Dt(window,"selectstart",re)};else{var Xi=Vi(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);mn=function(){if(Xi){var n=document.documentElement.style;Sr=n[Xi],n[Xi]="none"}},Pa=function(){Xi&&(document.documentElement.style[Xi]=Sr,Sr=void 0)}}function Za(){yt(window,"dragstart",re)}function bo(){Dt(window,"dragstart",re)}var Er,Qi;function Ha(n){for(;n.tabIndex===-1;)n=n.parentNode;n.style&&(Ki(),Er=n,Qi=n.style.outlineStyle,n.style.outlineStyle="none",yt(window,"keydown",Ki))}function Ki(){Er&&(Er.style.outlineStyle=Qi,Er=void 0,Qi=void 0,Dt(window,"keydown",Ki))}function ci(n){do n=n.parentNode;while((!n.offsetWidth||!n.offsetHeight)&&n!==document.body);return n}function Hn(n){var a=n.getBoundingClientRect();return{x:a.width/n.offsetWidth||1,y:a.height/n.offsetHeight||1,boundingClientRect:a}}var Xs={__proto__:null,TRANSFORM:Ua,TRANSITION:qi,TRANSITION_END:si,get:wr,getStyle:li,create:Rt,remove:Kt,empty:Ee,toFront:ui,toBack:Yi,hasClass:Gi,addClass:bt,removeClass:Jt,setClass:yo,getClass:Te,setOpacity:Ne,testProp:Vi,setTransform:Qe,setPosition:ee,getPosition:Zn,get disableTextSelection(){return mn},get enableTextSelection(){return Pa},disableImageDrag:Za,enableImageDrag:bo,preventOutline:Ha,restoreOutline:Ki,getSizedParentNode:ci,getScale:Hn};function yt(n,a,s,d){if(a&&typeof a=="object")for(var p in a)fi(n,p,a[p],s);else{a=N(a);for(var b=0,A=a.length;b<A;b++)fi(n,a[b],s,d)}return this}var tn="_leaflet_events";function Dt(n,a,s,d){if(arguments.length===1)pn(n),delete n[tn];else if(a&&typeof a=="object")for(var p in a)_n(n,p,a[p],s);else if(a=N(a),arguments.length===2)pn(n,function(j){return xt(a,j)!==-1});else for(var b=0,A=a.length;b<A;b++)_n(n,a[b],s,d);return this}function pn(n,a){for(var s in n[tn]){var d=s.split(/\d/)[0];(!a||a(d))&&_n(n,d,null,null,s)}}var ka={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function fi(n,a,s,d){var p=a+w(s)+(d?"_"+w(d):"");if(n[tn]&&n[tn][p])return this;var b=function(j){return s.call(d||n,j||window.event)},A=b;!dt.touchNative&&dt.pointer&&a.indexOf("touch")===0?b=ue(n,a,b):dt.touch&&a==="dblclick"?b=xr(n,b):"addEventListener"in n?a==="touchstart"||a==="touchmove"||a==="wheel"||a==="mousewheel"?n.addEventListener(ka[a]||a,b,dt.passiveEvents?{passive:!1}:!1):a==="mouseenter"||a==="mouseleave"?(b=function(j){j=j||window.event,di(n,j)&&A(j)},n.addEventListener(ka[a],b,!1)):n.addEventListener(a,A,!1):n.attachEvent("on"+a,b),n[tn]=n[tn]||{},n[tn][p]=b}function _n(n,a,s,d,p){p=p||a+w(s)+(d?"_"+w(d):"");var b=n[tn]&&n[tn][p];if(!b)return this;!dt.touchNative&&dt.pointer&&a.indexOf("touch")===0?qs(n,a,b):dt.touch&&a==="dblclick"?On(n,b):"removeEventListener"in n?n.removeEventListener(ka[a]||a,b,!1):n.detachEvent("on"+a,b),n[tn][p]=null}function An(n){return n.stopPropagation?n.stopPropagation():n.originalEvent?n.originalEvent._stopped=!0:n.cancelBubble=!0,this}function Ji(n){return fi(n,"wheel",An),this}function Fi(n){return yt(n,"mousedown touchstart dblclick contextmenu",An),n._leaflet_disable_click=!0,this}function re(n){return n.preventDefault?n.preventDefault():n.returnValue=!1,this}function gn(n){return re(n),An(n),this}function xo(n){if(n.composedPath)return n.composedPath();for(var a=[],s=n.target;s;)a.push(s),s=s.parentNode;return a}function Le(n,a){if(!a)return new $(n.clientX,n.clientY);var s=Hn(a),d=s.boundingClientRect;return new $((n.clientX-d.left)/s.x-a.clientLeft,(n.clientY-d.top)/s.y-a.clientTop)}var hi=dt.linux&&dt.chrome?window.devicePixelRatio:dt.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function qa(n){return dt.edge?n.wheelDeltaY/2:n.deltaY&&n.deltaMode===0?-n.deltaY/hi:n.deltaY&&n.deltaMode===1?-n.deltaY*20:n.deltaY&&n.deltaMode===2?-n.deltaY*60:n.deltaX||n.deltaZ?0:n.wheelDelta?(n.wheelDeltaY||n.wheelDelta)/2:n.detail&&Math.abs(n.detail)<32765?-n.detail*20:n.detail?n.detail/-32765*60:0}function di(n,a){var s=a.relatedTarget;if(!s)return!0;try{for(;s&&s!==n;)s=s.parentNode}catch{return!1}return s!==n}var Uu={__proto__:null,on:yt,off:Dt,stopPropagation:An,disableScrollPropagation:Ji,disableClickPropagation:Fi,preventDefault:re,stop:gn,getPropagationPath:xo,getMousePosition:Le,getWheelDelta:qa,isExternalTarget:di,addListener:yt,removeListener:Dt},Tr=rt.extend({run:function(n,a,s,d){this.stop(),this._el=n,this._inProgress=!0,this._duration=s||.25,this._easeOutPower=1/Math.max(d||.5,.2),this._startPos=Zn(n),this._offset=a.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=Xt(this._animate,this),this._step()},_step:function(n){var a=+new Date-this._startTime,s=this._duration*1e3;a<s?this._runFrame(this._easeOut(a/s),n):(this._runFrame(1),this._complete())},_runFrame:function(n,a){var s=this._startPos.add(this._offset.multiplyBy(n));a&&s._round(),ee(this._el,s),this.fire("step")},_complete:function(){Zt(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(n){return 1-Math.pow(1-n,this._easeOutPower)}}),Tt=rt.extend({options:{crs:Da,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(n,a){a=U(this,a),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(n),this._initLayout(),this._onResize=v(this._onResize,this),this._initEvents(),a.maxBounds&&this.setMaxBounds(a.maxBounds),a.zoom!==void 0&&(this._zoom=this._limitZoom(a.zoom)),a.center&&a.zoom!==void 0&&this.setView(at(a.center),a.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=qi&&dt.any3d&&!dt.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),yt(this._proxy,si,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(n,a,s){if(a=a===void 0?this._zoom:this._limitZoom(a),n=this._limitCenter(at(n),a,this.options.maxBounds),s=s||{},this._stop(),this._loaded&&!s.reset&&s!==!0){s.animate!==void 0&&(s.zoom=m({animate:s.animate},s.zoom),s.pan=m({animate:s.animate,duration:s.duration},s.pan));var d=this._zoom!==a?this._tryAnimatedZoom&&this._tryAnimatedZoom(n,a,s.zoom):this._tryAnimatedPan(n,s.pan);if(d)return clearTimeout(this._sizeTimer),this}return this._resetView(n,a,s.pan&&s.pan.noMoveStart),this},setZoom:function(n,a){return this._loaded?this.setView(this.getCenter(),n,{zoom:a}):(this._zoom=n,this)},zoomIn:function(n,a){return n=n||(dt.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+n,a)},zoomOut:function(n,a){return n=n||(dt.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-n,a)},setZoomAround:function(n,a,s){var d=this.getZoomScale(a),p=this.getSize().divideBy(2),b=n instanceof $?n:this.latLngToContainerPoint(n),A=b.subtract(p).multiplyBy(1-1/d),j=this.containerPointToLatLng(p.add(A));return this.setView(j,a,{zoom:s})},_getBoundsCenterZoom:function(n,a){a=a||{},n=n.getBounds?n.getBounds():st(n);var s=O(a.paddingTopLeft||a.padding||[0,0]),d=O(a.paddingBottomRight||a.padding||[0,0]),p=this.getBoundsZoom(n,!1,s.add(d));if(p=typeof a.maxZoom=="number"?Math.min(a.maxZoom,p):p,p===1/0)return{center:n.getCenter(),zoom:p};var b=d.subtract(s).divideBy(2),A=this.project(n.getSouthWest(),p),j=this.project(n.getNorthEast(),p),q=this.unproject(A.add(j).divideBy(2).add(b),p);return{center:q,zoom:p}},fitBounds:function(n,a){if(n=st(n),!n.isValid())throw new Error("Bounds are not valid.");var s=this._getBoundsCenterZoom(n,a);return this.setView(s.center,s.zoom,a)},fitWorld:function(n){return this.fitBounds([[-90,-180],[90,180]],n)},panTo:function(n,a){return this.setView(n,this._zoom,{pan:a})},panBy:function(n,a){if(n=O(n).round(),a=a||{},!n.x&&!n.y)return this.fire("moveend");if(a.animate!==!0&&!this.getSize().contains(n))return this._resetView(this.unproject(this.project(this.getCenter()).add(n)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new Tr,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),a.noMoveStart||this.fire("movestart"),a.animate!==!1){bt(this._mapPane,"leaflet-pan-anim");var s=this._getMapPanePos().subtract(n).round();this._panAnim.run(this._mapPane,s,a.duration||.25,a.easeLinearity)}else this._rawPanBy(n),this.fire("move").fire("moveend");return this},flyTo:function(n,a,s){if(s=s||{},s.animate===!1||!dt.any3d)return this.setView(n,a,s);this._stop();var d=this.project(this.getCenter()),p=this.project(n),b=this.getSize(),A=this._zoom;n=at(n),a=a===void 0?A:a;var j=Math.max(b.x,b.y),q=j*this.getZoomScale(A,a),I=p.distanceTo(d)||1,ot=1.42,ut=ot*ot;function ht(It){var bn=It?-1:1,Cn=It?q:j,Vn=q*q-j*j+bn*ut*ut*I*I,Nn=2*Cn*ut*I,er=Vn/Nn,Pr=Math.sqrt(er*er+1)-er,nr=Pr<1e-9?-18:Math.log(Pr);return nr}function vt(It){return(Math.exp(It)-Math.exp(-It))/2}function Ft(It){return(Math.exp(It)+Math.exp(-It))/2}function ne(It){return vt(It)/Ft(It)}var ve=ht(0);function Ke(It){return j*(Ft(ve)/Ft(ve+ot*It))}function fl(It){return j*(Ft(ve)*ne(ve+ot*It)-vt(ve))/ut}function hl(It){return 1-Math.pow(1-It,1.5)}var tr=Date.now(),la=(ht(1)-ve)/ot,dl=s.duration?1e3*s.duration:1e3*la*.8;function ua(){var It=(Date.now()-tr)/dl,bn=hl(It)*la;It<=1?(this._flyToFrame=Xt(ua,this),this._move(this.unproject(d.add(p.subtract(d).multiplyBy(fl(bn)/I)),A),this.getScaleZoom(j/Ke(bn),A),{flyTo:!0})):this._move(n,a)._moveEnd(!0)}return this._moveStart(!0,s.noMoveStart),ua.call(this),this},flyToBounds:function(n,a){var s=this._getBoundsCenterZoom(n,a);return this.flyTo(s.center,s.zoom,a)},setMaxBounds:function(n){return n=st(n),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),n.isValid()?(this.options.maxBounds=n,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(n){var a=this.options.minZoom;return this.options.minZoom=n,this._loaded&&a!==n&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(n):this},setMaxZoom:function(n){var a=this.options.maxZoom;return this.options.maxZoom=n,this._loaded&&a!==n&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(n):this},panInsideBounds:function(n,a){this._enforcingBounds=!0;var s=this.getCenter(),d=this._limitCenter(s,this._zoom,st(n));return s.equals(d)||this.panTo(d,a),this._enforcingBounds=!1,this},panInside:function(n,a){a=a||{};var s=O(a.paddingTopLeft||a.padding||[0,0]),d=O(a.paddingBottomRight||a.padding||[0,0]),p=this.project(this.getCenter()),b=this.project(n),A=this.getPixelBounds(),j=nt([A.min.add(s),A.max.subtract(d)]),q=j.getSize();if(!j.contains(b)){this._enforcingBounds=!0;var I=b.subtract(j.getCenter()),ot=j.extend(b).getSize().subtract(q);p.x+=I.x<0?-ot.x:ot.x,p.y+=I.y<0?-ot.y:ot.y,this.panTo(this.unproject(p),a),this._enforcingBounds=!1}return this},invalidateSize:function(n){if(!this._loaded)return this;n=m({animate:!1,pan:!0},n===!0?{animate:!0}:n);var a=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var s=this.getSize(),d=a.divideBy(2).round(),p=s.divideBy(2).round(),b=d.subtract(p);return!b.x&&!b.y?this:(n.animate&&n.pan?this.panBy(b):(n.pan&&this._rawPanBy(b),this.fire("move"),n.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(v(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:a,newSize:s}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(n){if(n=this._locateOptions=m({timeout:1e4,watch:!1},n),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var a=v(this._handleGeolocationResponse,this),s=v(this._handleGeolocationError,this);return n.watch?this._locationWatchId=navigator.geolocation.watchPosition(a,s,n):navigator.geolocation.getCurrentPosition(a,s,n),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(n){if(this._container._leaflet_id){var a=n.code,s=n.message||(a===1?"permission denied":a===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:a,message:"Geolocation error: "+s+"."})}},_handleGeolocationResponse:function(n){if(this._container._leaflet_id){var a=n.coords.latitude,s=n.coords.longitude,d=new ft(a,s),p=d.toBounds(n.coords.accuracy*2),b=this._locateOptions;if(b.setView){var A=this.getBoundsZoom(p);this.setView(d,b.maxZoom?Math.min(A,b.maxZoom):A)}var j={latlng:d,bounds:p,timestamp:n.timestamp};for(var q in n.coords)typeof n.coords[q]=="number"&&(j[q]=n.coords[q]);this.fire("locationfound",j)}},addHandler:function(n,a){if(!a)return this;var s=this[n]=new a(this);return this._handlers.push(s),this.options[n]&&s.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),Kt(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(Zt(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var n;for(n in this._layers)this._layers[n].remove();for(n in this._panes)Kt(this._panes[n]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(n,a){var s="leaflet-pane"+(n?" leaflet-"+n.replace("Pane","")+"-pane":""),d=Rt("div",s,a||this._mapPane);return n&&(this._panes[n]=d),d},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var n=this.getPixelBounds(),a=this.unproject(n.getBottomLeft()),s=this.unproject(n.getTopRight());return new tt(a,s)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(n,a,s){n=st(n),s=O(s||[0,0]);var d=this.getZoom()||0,p=this.getMinZoom(),b=this.getMaxZoom(),A=n.getNorthWest(),j=n.getSouthEast(),q=this.getSize().subtract(s),I=nt(this.project(j,d),this.project(A,d)).getSize(),ot=dt.any3d?this.options.zoomSnap:1,ut=q.x/I.x,ht=q.y/I.y,vt=a?Math.max(ut,ht):Math.min(ut,ht);return d=this.getScaleZoom(vt,d),ot&&(d=Math.round(d/(ot/100))*(ot/100),d=a?Math.ceil(d/ot)*ot:Math.floor(d/ot)*ot),Math.max(p,Math.min(b,d))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new $(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(n,a){var s=this._getTopLeftPoint(n,a);return new Q(s,s.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(n){return this.options.crs.getProjectedBounds(n===void 0?this.getZoom():n)},getPane:function(n){return typeof n=="string"?this._panes[n]:n},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(n,a){var s=this.options.crs;return a=a===void 0?this._zoom:a,s.scale(n)/s.scale(a)},getScaleZoom:function(n,a){var s=this.options.crs;a=a===void 0?this._zoom:a;var d=s.zoom(n*s.scale(a));return isNaN(d)?1/0:d},project:function(n,a){return a=a===void 0?this._zoom:a,this.options.crs.latLngToPoint(at(n),a)},unproject:function(n,a){return a=a===void 0?this._zoom:a,this.options.crs.pointToLatLng(O(n),a)},layerPointToLatLng:function(n){var a=O(n).add(this.getPixelOrigin());return this.unproject(a)},latLngToLayerPoint:function(n){var a=this.project(at(n))._round();return a._subtract(this.getPixelOrigin())},wrapLatLng:function(n){return this.options.crs.wrapLatLng(at(n))},wrapLatLngBounds:function(n){return this.options.crs.wrapLatLngBounds(st(n))},distance:function(n,a){return this.options.crs.distance(at(n),at(a))},containerPointToLayerPoint:function(n){return O(n).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(n){return O(n).add(this._getMapPanePos())},containerPointToLatLng:function(n){var a=this.containerPointToLayerPoint(O(n));return this.layerPointToLatLng(a)},latLngToContainerPoint:function(n){return this.layerPointToContainerPoint(this.latLngToLayerPoint(at(n)))},mouseEventToContainerPoint:function(n){return Le(n,this._container)},mouseEventToLayerPoint:function(n){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(n))},mouseEventToLatLng:function(n){return this.layerPointToLatLng(this.mouseEventToLayerPoint(n))},_initContainer:function(n){var a=this._container=wr(n);if(a){if(a._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");yt(a,"scroll",this._onScroll,this),this._containerId=w(a)},_initLayout:function(){var n=this._container;this._fadeAnimated=this.options.fadeAnimation&&dt.any3d,bt(n,"leaflet-container"+(dt.touch?" leaflet-touch":"")+(dt.retina?" leaflet-retina":"")+(dt.ielt9?" leaflet-oldie":"")+(dt.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var a=li(n,"position");a!=="absolute"&&a!=="relative"&&a!=="fixed"&&a!=="sticky"&&(n.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var n=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),ee(this._mapPane,new $(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(bt(n.markerPane,"leaflet-zoom-hide"),bt(n.shadowPane,"leaflet-zoom-hide"))},_resetView:function(n,a,s){ee(this._mapPane,new $(0,0));var d=!this._loaded;this._loaded=!0,a=this._limitZoom(a),this.fire("viewprereset");var p=this._zoom!==a;this._moveStart(p,s)._move(n,a)._moveEnd(p),this.fire("viewreset"),d&&this.fire("load")},_moveStart:function(n,a){return n&&this.fire("zoomstart"),a||this.fire("movestart"),this},_move:function(n,a,s,d){a===void 0&&(a=this._zoom);var p=this._zoom!==a;return this._zoom=a,this._lastCenter=n,this._pixelOrigin=this._getNewPixelOrigin(n),d?s&&s.pinch&&this.fire("zoom",s):((p||s&&s.pinch)&&this.fire("zoom",s),this.fire("move",s)),this},_moveEnd:function(n){return n&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return Zt(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(n){ee(this._mapPane,this._getMapPanePos().subtract(n))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(n){this._targets={},this._targets[w(this._container)]=this;var a=n?Dt:yt;a(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&a(window,"resize",this._onResize,this),dt.any3d&&this.options.transform3DLimit&&(n?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){Zt(this._resizeRequest),this._resizeRequest=Xt(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var n=this._getMapPanePos();Math.max(Math.abs(n.x),Math.abs(n.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(n,a){for(var s=[],d,p=a==="mouseout"||a==="mouseover",b=n.target||n.srcElement,A=!1;b;){if(d=this._targets[w(b)],d&&(a==="click"||a==="preclick")&&this._draggableMoved(d)){A=!0;break}if(d&&d.listens(a,!0)&&(p&&!di(b,n)||(s.push(d),p))||b===this._container)break;b=b.parentNode}return!s.length&&!A&&!p&&this.listens(a,!0)&&(s=[this]),s},_isClickDisabled:function(n){for(;n&&n!==this._container;){if(n._leaflet_disable_click)return!0;n=n.parentNode}},_handleDOMEvent:function(n){var a=n.target||n.srcElement;if(!(!this._loaded||a._leaflet_disable_events||n.type==="click"&&this._isClickDisabled(a))){var s=n.type;s==="mousedown"&&Ha(a),this._fireDOMEvent(n,s)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(n,a,s){if(n.type==="click"){var d=m({},n);d.type="preclick",this._fireDOMEvent(d,d.type,s)}var p=this._findEventTargets(n,a);if(s){for(var b=[],A=0;A<s.length;A++)s[A].listens(a,!0)&&b.push(s[A]);p=b.concat(p)}if(p.length){a==="contextmenu"&&re(n);var j=p[0],q={originalEvent:n};if(n.type!=="keypress"&&n.type!=="keydown"&&n.type!=="keyup"){var I=j.getLatLng&&(!j._radius||j._radius<=10);q.containerPoint=I?this.latLngToContainerPoint(j.getLatLng()):this.mouseEventToContainerPoint(n),q.layerPoint=this.containerPointToLayerPoint(q.containerPoint),q.latlng=I?j.getLatLng():this.layerPointToLatLng(q.layerPoint)}for(A=0;A<p.length;A++)if(p[A].fire(a,q,!0),q.originalEvent._stopped||p[A].options.bubblingMouseEvents===!1&&xt(this._mouseEvents,a)!==-1)return}},_draggableMoved:function(n){return n=n.dragging&&n.dragging.enabled()?n:this,n.dragging&&n.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var n=0,a=this._handlers.length;n<a;n++)this._handlers[n].disable()},whenReady:function(n,a){return this._loaded?n.call(a||this,{target:this}):this.on("load",n,a),this},_getMapPanePos:function(){return Zn(this._mapPane)||new $(0,0)},_moved:function(){var n=this._getMapPanePos();return n&&!n.equals([0,0])},_getTopLeftPoint:function(n,a){var s=n&&a!==void 0?this._getNewPixelOrigin(n,a):this.getPixelOrigin();return s.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(n,a){var s=this.getSize()._divideBy(2);return this.project(n,a)._subtract(s)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(n,a,s){var d=this._getNewPixelOrigin(s,a);return this.project(n,a)._subtract(d)},_latLngBoundsToNewLayerBounds:function(n,a,s){var d=this._getNewPixelOrigin(s,a);return nt([this.project(n.getSouthWest(),a)._subtract(d),this.project(n.getNorthWest(),a)._subtract(d),this.project(n.getSouthEast(),a)._subtract(d),this.project(n.getNorthEast(),a)._subtract(d)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(n){return this.latLngToLayerPoint(n).subtract(this._getCenterLayerPoint())},_limitCenter:function(n,a,s){if(!s)return n;var d=this.project(n,a),p=this.getSize().divideBy(2),b=new Q(d.subtract(p),d.add(p)),A=this._getBoundsOffset(b,s,a);return Math.abs(A.x)<=1&&Math.abs(A.y)<=1?n:this.unproject(d.add(A),a)},_limitOffset:function(n,a){if(!a)return n;var s=this.getPixelBounds(),d=new Q(s.min.add(n),s.max.add(n));return n.add(this._getBoundsOffset(d,a))},_getBoundsOffset:function(n,a,s){var d=nt(this.project(a.getNorthEast(),s),this.project(a.getSouthWest(),s)),p=d.min.subtract(n.min),b=d.max.subtract(n.max),A=this._rebound(p.x,-b.x),j=this._rebound(p.y,-b.y);return new $(A,j)},_rebound:function(n,a){return n+a>0?Math.round(n-a)/2:Math.max(0,Math.ceil(n))-Math.max(0,Math.floor(a))},_limitZoom:function(n){var a=this.getMinZoom(),s=this.getMaxZoom(),d=dt.any3d?this.options.zoomSnap:1;return d&&(n=Math.round(n/d)*d),Math.max(a,Math.min(s,n))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){Jt(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(n,a){var s=this._getCenterOffset(n)._trunc();return(a&&a.animate)!==!0&&!this.getSize().contains(s)?!1:(this.panBy(s,a),!0)},_createAnimProxy:function(){var n=this._proxy=Rt("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(n),this.on("zoomanim",function(a){var s=Ua,d=this._proxy.style[s];Qe(this._proxy,this.project(a.center,a.zoom),this.getZoomScale(a.zoom,1)),d===this._proxy.style[s]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){Kt(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var n=this.getCenter(),a=this.getZoom();Qe(this._proxy,this.project(n,a),this.getZoomScale(a,1))},_catchTransitionEnd:function(n){this._animatingZoom&&n.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(n,a,s){if(this._animatingZoom)return!0;if(s=s||{},!this._zoomAnimated||s.animate===!1||this._nothingToAnimate()||Math.abs(a-this._zoom)>this.options.zoomAnimationThreshold)return!1;var d=this.getZoomScale(a),p=this._getCenterOffset(n)._divideBy(1-1/d);return s.animate!==!0&&!this.getSize().contains(p)?!1:(Xt(function(){this._moveStart(!0,s.noMoveStart||!1)._animateZoom(n,a,!0)},this),!0)},_animateZoom:function(n,a,s,d){this._mapPane&&(s&&(this._animatingZoom=!0,this._animateToCenter=n,this._animateToZoom=a,bt(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:n,zoom:a,noUpdate:d}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(v(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&Jt(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function Ya(n,a){return new Tt(n,a)}var ze=Se.extend({options:{position:"topright"},initialize:function(n){U(this,n)},getPosition:function(){return this.options.position},setPosition:function(n){var a=this._map;return a&&a.removeControl(this),this.options.position=n,a&&a.addControl(this),this},getContainer:function(){return this._container},addTo:function(n){this.remove(),this._map=n;var a=this._container=this.onAdd(n),s=this.getPosition(),d=n._controlCorners[s];return bt(a,"leaflet-control"),s.indexOf("bottom")!==-1?d.insertBefore(a,d.firstChild):d.appendChild(a),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(Kt(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(n){this._map&&n&&n.screenX>0&&n.screenY>0&&this._map.getContainer().focus()}}),Wi=function(n){return new ze(n)};Tt.include({addControl:function(n){return n.addTo(this),this},removeControl:function(n){return n.remove(),this},_initControlPos:function(){var n=this._controlCorners={},a="leaflet-",s=this._controlContainer=Rt("div",a+"control-container",this._container);function d(p,b){var A=a+p+" "+a+b;n[p+b]=Rt("div",A,s)}d("top","left"),d("top","right"),d("bottom","left"),d("bottom","right")},_clearControlPos:function(){for(var n in this._controlCorners)Kt(this._controlCorners[n]);Kt(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var Qs=ze.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(n,a,s,d){return s<d?-1:d<s?1:0}},initialize:function(n,a,s){U(this,s),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var d in n)this._addLayer(n[d],d);for(d in a)this._addLayer(a[d],d,!0)},onAdd:function(n){this._initLayout(),this._update(),this._map=n,n.on("zoomend",this._checkDisabledLayers,this);for(var a=0;a<this._layers.length;a++)this._layers[a].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(n){return ze.prototype.addTo.call(this,n),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var n=0;n<this._layers.length;n++)this._layers[n].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(n,a){return this._addLayer(n,a),this._map?this._update():this},addOverlay:function(n,a){return this._addLayer(n,a,!0),this._map?this._update():this},removeLayer:function(n){n.off("add remove",this._onLayerChange,this);var a=this._getLayer(w(n));return a&&this._layers.splice(this._layers.indexOf(a),1),this._map?this._update():this},expand:function(){bt(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var n=this._map.getSize().y-(this._container.offsetTop+50);return n<this._section.clientHeight?(bt(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=n+"px"):Jt(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return Jt(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var n="leaflet-control-layers",a=this._container=Rt("div",n),s=this.options.collapsed;a.setAttribute("aria-haspopup",!0),Fi(a),Ji(a);var d=this._section=Rt("section",n+"-list");s&&(this._map.on("click",this.collapse,this),yt(a,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var p=this._layersLink=Rt("a",n+"-toggle",a);p.href="#",p.title="Layers",p.setAttribute("role","button"),yt(p,{keydown:function(b){b.keyCode===13&&this._expandSafely()},click:function(b){re(b),this._expandSafely()}},this),s||this.expand(),this._baseLayersList=Rt("div",n+"-base",d),this._separator=Rt("div",n+"-separator",d),this._overlaysList=Rt("div",n+"-overlays",d),a.appendChild(d)},_getLayer:function(n){for(var a=0;a<this._layers.length;a++)if(this._layers[a]&&w(this._layers[a].layer)===n)return this._layers[a]},_addLayer:function(n,a,s){this._map&&n.on("add remove",this._onLayerChange,this),this._layers.push({layer:n,name:a,overlay:s}),this.options.sortLayers&&this._layers.sort(v(function(d,p){return this.options.sortFunction(d.layer,p.layer,d.name,p.name)},this)),this.options.autoZIndex&&n.setZIndex&&(this._lastZIndex++,n.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;Ee(this._baseLayersList),Ee(this._overlaysList),this._layerControlInputs=[];var n,a,s,d,p=0;for(s=0;s<this._layers.length;s++)d=this._layers[s],this._addItem(d),a=a||d.overlay,n=n||!d.overlay,p+=d.overlay?0:1;return this.options.hideSingleBase&&(n=n&&p>1,this._baseLayersList.style.display=n?"":"none"),this._separator.style.display=a&&n?"":"none",this},_onLayerChange:function(n){this._handlingClick||this._update();var a=this._getLayer(w(n.target)),s=a.overlay?n.type==="add"?"overlayadd":"overlayremove":n.type==="add"?"baselayerchange":null;s&&this._map.fire(s,a)},_createRadioElement:function(n,a){var s='<input type="radio" class="leaflet-control-layers-selector" name="'+n+'"'+(a?' checked="checked"':"")+"/>",d=document.createElement("div");return d.innerHTML=s,d.firstChild},_addItem:function(n){var a=document.createElement("label"),s=this._map.hasLayer(n.layer),d;n.overlay?(d=document.createElement("input"),d.type="checkbox",d.className="leaflet-control-layers-selector",d.defaultChecked=s):d=this._createRadioElement("leaflet-base-layers_"+w(this),s),this._layerControlInputs.push(d),d.layerId=w(n.layer),yt(d,"click",this._onInputClick,this);var p=document.createElement("span");p.innerHTML=" "+n.name;var b=document.createElement("span");a.appendChild(b),b.appendChild(d),b.appendChild(p);var A=n.overlay?this._overlaysList:this._baseLayersList;return A.appendChild(a),this._checkDisabledLayers(),a},_onInputClick:function(){if(!this._preventClick){var n=this._layerControlInputs,a,s,d=[],p=[];this._handlingClick=!0;for(var b=n.length-1;b>=0;b--)a=n[b],s=this._getLayer(a.layerId).layer,a.checked?d.push(s):a.checked||p.push(s);for(b=0;b<p.length;b++)this._map.hasLayer(p[b])&&this._map.removeLayer(p[b]);for(b=0;b<d.length;b++)this._map.hasLayer(d[b])||this._map.addLayer(d[b]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var n=this._layerControlInputs,a,s,d=this._map.getZoom(),p=n.length-1;p>=0;p--)a=n[p],s=this._getLayer(a.layerId).layer,a.disabled=s.options.minZoom!==void 0&&d<s.options.minZoom||s.options.maxZoom!==void 0&&d>s.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var n=this._section;this._preventClick=!0,yt(n,"click",re),this.expand();var a=this;setTimeout(function(){Dt(n,"click",re),a._preventClick=!1})}}),Pu=function(n,a,s){return new Qs(n,a,s)},wo=ze.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(n){var a="leaflet-control-zoom",s=Rt("div",a+" leaflet-bar"),d=this.options;return this._zoomInButton=this._createButton(d.zoomInText,d.zoomInTitle,a+"-in",s,this._zoomIn),this._zoomOutButton=this._createButton(d.zoomOutText,d.zoomOutTitle,a+"-out",s,this._zoomOut),this._updateDisabled(),n.on("zoomend zoomlevelschange",this._updateDisabled,this),s},onRemove:function(n){n.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(n){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(n.shiftKey?3:1))},_zoomOut:function(n){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(n.shiftKey?3:1))},_createButton:function(n,a,s,d,p){var b=Rt("a",s,d);return b.innerHTML=n,b.href="#",b.title=a,b.setAttribute("role","button"),b.setAttribute("aria-label",a),Fi(b),yt(b,"click",gn),yt(b,"click",p,this),yt(b,"click",this._refocusOnMap,this),b},_updateDisabled:function(){var n=this._map,a="leaflet-disabled";Jt(this._zoomInButton,a),Jt(this._zoomOutButton,a),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||n._zoom===n.getMinZoom())&&(bt(this._zoomOutButton,a),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||n._zoom===n.getMaxZoom())&&(bt(this._zoomInButton,a),this._zoomInButton.setAttribute("aria-disabled","true"))}});Tt.mergeOptions({zoomControl:!0}),Tt.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new wo,this.addControl(this.zoomControl))});var So=function(n){return new wo(n)},Ks=ze.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(n){var a="leaflet-control-scale",s=Rt("div",a),d=this.options;return this._addScales(d,a+"-line",s),n.on(d.updateWhenIdle?"moveend":"move",this._update,this),n.whenReady(this._update,this),s},onRemove:function(n){n.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(n,a,s){n.metric&&(this._mScale=Rt("div",a,s)),n.imperial&&(this._iScale=Rt("div",a,s))},_update:function(){var n=this._map,a=n.getSize().y/2,s=n.distance(n.containerPointToLatLng([0,a]),n.containerPointToLatLng([this.options.maxWidth,a]));this._updateScales(s)},_updateScales:function(n){this.options.metric&&n&&this._updateMetric(n),this.options.imperial&&n&&this._updateImperial(n)},_updateMetric:function(n){var a=this._getRoundNum(n),s=a<1e3?a+" m":a/1e3+" km";this._updateScale(this._mScale,s,a/n)},_updateImperial:function(n){var a=n*3.2808399,s,d,p;a>5280?(s=a/5280,d=this._getRoundNum(s),this._updateScale(this._iScale,d+" mi",d/s)):(p=this._getRoundNum(a),this._updateScale(this._iScale,p+" ft",p/a))},_updateScale:function(n,a,s){n.style.width=Math.round(this.options.maxWidth*s)+"px",n.innerHTML=a},_getRoundNum:function(n){var a=Math.pow(10,(Math.floor(n)+"").length-1),s=n/a;return s=s>=10?10:s>=5?5:s>=3?3:s>=2?2:1,a*s}}),Zu=function(n){return new Ks(n)},Hu='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',Eo=ze.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(dt.inlineSvg?Hu+" ":"")+"Leaflet</a>"},initialize:function(n){U(this,n),this._attributions={}},onAdd:function(n){n.attributionControl=this,this._container=Rt("div","leaflet-control-attribution"),Fi(this._container);for(var a in n._layers)n._layers[a].getAttribution&&this.addAttribution(n._layers[a].getAttribution());return this._update(),n.on("layeradd",this._addAttribution,this),this._container},onRemove:function(n){n.off("layeradd",this._addAttribution,this)},_addAttribution:function(n){n.layer.getAttribution&&(this.addAttribution(n.layer.getAttribution()),n.layer.once("remove",function(){this.removeAttribution(n.layer.getAttribution())},this))},setPrefix:function(n){return this.options.prefix=n,this._update(),this},addAttribution:function(n){return n?(this._attributions[n]||(this._attributions[n]=0),this._attributions[n]++,this._update(),this):this},removeAttribution:function(n){return n?(this._attributions[n]&&(this._attributions[n]--,this._update()),this):this},_update:function(){if(this._map){var n=[];for(var a in this._attributions)this._attributions[a]&&n.push(a);var s=[];this.options.prefix&&s.push(this.options.prefix),n.length&&s.push(n.join(", ")),this._container.innerHTML=s.join(' <span aria-hidden="true">|</span> ')}}});Tt.mergeOptions({attributionControl:!0}),Tt.addInitHook(function(){this.options.attributionControl&&new Eo().addTo(this)});var ku=function(n){return new Eo(n)};ze.Layers=Qs,ze.Zoom=wo,ze.Scale=Ks,ze.Attribution=Eo,Wi.layers=Pu,Wi.zoom=So,Wi.scale=Zu,Wi.attribution=ku;var en=Se.extend({initialize:function(n){this._map=n},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});en.addTo=function(n,a){return n.addHandler(a,this),this};var qu={Events:V},Js=dt.touch?"touchstart mousedown":"mousedown",kn=rt.extend({options:{clickTolerance:3},initialize:function(n,a,s,d){U(this,d),this._element=n,this._dragStartTarget=a||n,this._preventOutline=s},enable:function(){this._enabled||(yt(this._dragStartTarget,Js,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(kn._dragging===this&&this.finishDrag(!0),Dt(this._dragStartTarget,Js,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(n){if(this._enabled&&(this._moved=!1,!Gi(this._element,"leaflet-zoom-anim"))){if(n.touches&&n.touches.length!==1){kn._dragging===this&&this.finishDrag();return}if(!(kn._dragging||n.shiftKey||n.which!==1&&n.button!==1&&!n.touches)&&(kn._dragging=this,this._preventOutline&&Ha(this._element),Za(),mn(),!this._moving)){this.fire("down");var a=n.touches?n.touches[0]:n,s=ci(this._element);this._startPoint=new $(a.clientX,a.clientY),this._startPos=Zn(this._element),this._parentScale=Hn(s);var d=n.type==="mousedown";yt(document,d?"mousemove":"touchmove",this._onMove,this),yt(document,d?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(n){if(this._enabled){if(n.touches&&n.touches.length>1){this._moved=!0;return}var a=n.touches&&n.touches.length===1?n.touches[0]:n,s=new $(a.clientX,a.clientY)._subtract(this._startPoint);!s.x&&!s.y||Math.abs(s.x)+Math.abs(s.y)<this.options.clickTolerance||(s.x/=this._parentScale.x,s.y/=this._parentScale.y,re(n),this._moved||(this.fire("dragstart"),this._moved=!0,bt(document.body,"leaflet-dragging"),this._lastTarget=n.target||n.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),bt(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(s),this._moving=!0,this._lastEvent=n,this._updatePosition())}},_updatePosition:function(){var n={originalEvent:this._lastEvent};this.fire("predrag",n),ee(this._element,this._newPos),this.fire("drag",n)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(n){Jt(document.body,"leaflet-dragging"),this._lastTarget&&(Jt(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),Dt(document,"mousemove touchmove",this._onMove,this),Dt(document,"mouseup touchend touchcancel",this._onUp,this),bo(),Pa();var a=this._moved&&this._moving;this._moving=!1,kn._dragging=!1,a&&this.fire("dragend",{noInertia:n,distance:this._newPos.distanceTo(this._startPos)})}});function Fs(n,a,s){var d,p=[1,4,2,8],b,A,j,q,I,ot,ut,ht;for(b=0,ot=n.length;b<ot;b++)n[b]._code=mi(n[b],a);for(j=0;j<4;j++){for(ut=p[j],d=[],b=0,ot=n.length,A=ot-1;b<ot;A=b++)q=n[b],I=n[A],q._code&ut?I._code&ut||(ht=Or(I,q,ut,a,s),ht._code=mi(ht,a),d.push(ht)):(I._code&ut&&(ht=Or(I,q,ut,a,s),ht._code=mi(ht,a),d.push(ht)),d.push(q));n=d}return n}function Lr(n,a){var s,d,p,b,A,j,q,I,ot;if(!n||n.length===0)throw new Error("latlngs not passed");ge(n)||(console.warn("latlngs are not flat! Only the first ring will be used"),n=n[0]);var ut=at([0,0]),ht=st(n),vt=ht.getNorthWest().distanceTo(ht.getSouthWest())*ht.getNorthEast().distanceTo(ht.getNorthWest());vt<1700&&(ut=To(n));var Ft=n.length,ne=[];for(s=0;s<Ft;s++){var ve=at(n[s]);ne.push(a.project(at([ve.lat-ut.lat,ve.lng-ut.lng])))}for(j=q=I=0,s=0,d=Ft-1;s<Ft;d=s++)p=ne[s],b=ne[d],A=p.y*b.x-b.y*p.x,q+=(p.x+b.x)*A,I+=(p.y+b.y)*A,j+=A*3;j===0?ot=ne[0]:ot=[q/j,I/j];var Ke=a.unproject(O(ot));return at([Ke.lat+ut.lat,Ke.lng+ut.lng])}function To(n){for(var a=0,s=0,d=0,p=0;p<n.length;p++){var b=at(n[p]);a+=b.lat,s+=b.lng,d++}return at([a/d,s/d])}var Yu={__proto__:null,clipPolygon:Fs,polygonCenter:Lr,centroid:To};function Ws(n,a){if(!a||!n.length)return n.slice();var s=a*a;return n=Xu(n,s),n=Vu(n,s),n}function Lo(n,a,s){return Math.sqrt(pi(n,a,s,!0))}function Gu(n,a,s){return pi(n,a,s)}function Vu(n,a){var s=n.length,d=typeof Uint8Array<"u"?Uint8Array:Array,p=new d(s);p[0]=p[s-1]=1,Oo(n,p,a,0,s-1);var b,A=[];for(b=0;b<s;b++)p[b]&&A.push(n[b]);return A}function Oo(n,a,s,d,p){var b=0,A,j,q;for(j=d+1;j<=p-1;j++)q=pi(n[j],n[d],n[p],!0),q>b&&(A=j,b=q);b>s&&(a[A]=1,Oo(n,a,s,d,A),Oo(n,a,s,A,p))}function Xu(n,a){for(var s=[n[0]],d=1,p=0,b=n.length;d<b;d++)Qu(n[d],n[p])>a&&(s.push(n[d]),p=d);return p<b-1&&s.push(n[b-1]),s}var Is;function $s(n,a,s,d,p){var b=d?Is:mi(n,s),A=mi(a,s),j,q,I;for(Is=A;;){if(!(b|A))return[n,a];if(b&A)return!1;j=b||A,q=Or(n,a,j,s,p),I=mi(q,s),j===b?(n=q,b=I):(a=q,A=I)}}function Or(n,a,s,d,p){var b=a.x-n.x,A=a.y-n.y,j=d.min,q=d.max,I,ot;return s&8?(I=n.x+b*(q.y-n.y)/A,ot=q.y):s&4?(I=n.x+b*(j.y-n.y)/A,ot=j.y):s&2?(I=q.x,ot=n.y+A*(q.x-n.x)/b):s&1&&(I=j.x,ot=n.y+A*(j.x-n.x)/b),new $(I,ot,p)}function mi(n,a){var s=0;return n.x<a.min.x?s|=1:n.x>a.max.x&&(s|=2),n.y<a.min.y?s|=4:n.y>a.max.y&&(s|=8),s}function Qu(n,a){var s=a.x-n.x,d=a.y-n.y;return s*s+d*d}function pi(n,a,s,d){var p=a.x,b=a.y,A=s.x-p,j=s.y-b,q=A*A+j*j,I;return q>0&&(I=((n.x-p)*A+(n.y-b)*j)/q,I>1?(p=s.x,b=s.y):I>0&&(p+=A*I,b+=j*I)),A=n.x-p,j=n.y-b,d?A*A+j*j:new $(p,b)}function ge(n){return!et(n[0])||typeof n[0][0]!="object"&&typeof n[0][0]<"u"}function tl(n){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),ge(n)}function Ao(n,a){var s,d,p,b,A,j,q,I;if(!n||n.length===0)throw new Error("latlngs not passed");ge(n)||(console.warn("latlngs are not flat! Only the first ring will be used"),n=n[0]);var ot=at([0,0]),ut=st(n),ht=ut.getNorthWest().distanceTo(ut.getSouthWest())*ut.getNorthEast().distanceTo(ut.getNorthWest());ht<1700&&(ot=To(n));var vt=n.length,Ft=[];for(s=0;s<vt;s++){var ne=at(n[s]);Ft.push(a.project(at([ne.lat-ot.lat,ne.lng-ot.lng])))}for(s=0,d=0;s<vt-1;s++)d+=Ft[s].distanceTo(Ft[s+1])/2;if(d===0)I=Ft[0];else for(s=0,b=0;s<vt-1;s++)if(A=Ft[s],j=Ft[s+1],p=A.distanceTo(j),b+=p,b>d){q=(b-d)/p,I=[j.x-q*(j.x-A.x),j.y-q*(j.y-A.y)];break}var ve=a.unproject(O(I));return at([ve.lat+ot.lat,ve.lng+ot.lng])}var el={__proto__:null,simplify:Ws,pointToSegmentDistance:Lo,closestPointOnSegment:Gu,clipSegment:$s,_getEdgeIntersection:Or,_getBitCode:mi,_sqClosestPointOnSegment:pi,isFlat:ge,_flat:tl,polylineCenter:Ao},Ar={project:function(n){return new $(n.lng,n.lat)},unproject:function(n){return new ft(n.y,n.x)},bounds:new Q([-180,-90],[180,90])},Rr={R:6378137,R_MINOR:6356752314245179e-9,bounds:new Q([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(n){var a=Math.PI/180,s=this.R,d=n.lat*a,p=this.R_MINOR/s,b=Math.sqrt(1-p*p),A=b*Math.sin(d),j=Math.tan(Math.PI/4-d/2)/Math.pow((1-A)/(1+A),b/2);return d=-s*Math.log(Math.max(j,1e-10)),new $(n.lng*a*s,d)},unproject:function(n){for(var a=180/Math.PI,s=this.R,d=this.R_MINOR/s,p=Math.sqrt(1-d*d),b=Math.exp(-n.y/s),A=Math.PI/2-2*Math.atan(b),j=0,q=.1,I;j<15&&Math.abs(q)>1e-7;j++)I=p*Math.sin(A),I=Math.pow((1-I)/(1+I),p/2),q=Math.PI/2-2*Math.atan(b*I)-A,A+=q;return new ft(A*a,n.x*a/s)}},nl={__proto__:null,LonLat:Ar,Mercator:Rr,SphericalMercator:za},Ii=m({},zt,{code:"EPSG:3395",projection:Rr,transformation:function(){var n=.5/(Math.PI*Rr.R);return ei(n,.5,-n,.5)}()}),il=m({},zt,{code:"EPSG:4326",projection:Ar,transformation:ei(1/180,1,-1/180,.5)}),Ku=m({},te,{projection:Ar,transformation:ei(1,0,-1,0),scale:function(n){return Math.pow(2,n)},zoom:function(n){return Math.log(n)/Math.LN2},distance:function(n,a){var s=a.lng-n.lng,d=a.lat-n.lat;return Math.sqrt(s*s+d*d)},infinite:!0});te.Earth=zt,te.EPSG3395=Ii,te.EPSG3857=Da,te.EPSG900913=co,te.EPSG4326=il,te.Simple=Ku;var nn=rt.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(n){return n.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(n){return n&&n.removeLayer(this),this},getPane:function(n){return this._map.getPane(n?this.options[n]||n:this.options.pane)},addInteractiveTarget:function(n){return this._map._targets[w(n)]=this,this},removeInteractiveTarget:function(n){return delete this._map._targets[w(n)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(n){var a=n.target;if(a.hasLayer(this)){if(this._map=a,this._zoomAnimated=a._zoomAnimated,this.getEvents){var s=this.getEvents();a.on(s,this),this.once("remove",function(){a.off(s,this)},this)}this.onAdd(a),this.fire("add"),a.fire("layeradd",{layer:this})}}});Tt.include({addLayer:function(n){if(!n._layerAdd)throw new Error("The provided object is not a Layer.");var a=w(n);return this._layers[a]?this:(this._layers[a]=n,n._mapToAdd=this,n.beforeAdd&&n.beforeAdd(this),this.whenReady(n._layerAdd,n),this)},removeLayer:function(n){var a=w(n);return this._layers[a]?(this._loaded&&n.onRemove(this),delete this._layers[a],this._loaded&&(this.fire("layerremove",{layer:n}),n.fire("remove")),n._map=n._mapToAdd=null,this):this},hasLayer:function(n){return w(n)in this._layers},eachLayer:function(n,a){for(var s in this._layers)n.call(a,this._layers[s]);return this},_addLayers:function(n){n=n?et(n)?n:[n]:[];for(var a=0,s=n.length;a<s;a++)this.addLayer(n[a])},_addZoomLimit:function(n){(!isNaN(n.options.maxZoom)||!isNaN(n.options.minZoom))&&(this._zoomBoundLayers[w(n)]=n,this._updateZoomLevels())},_removeZoomLimit:function(n){var a=w(n);this._zoomBoundLayers[a]&&(delete this._zoomBoundLayers[a],this._updateZoomLevels())},_updateZoomLevels:function(){var n=1/0,a=-1/0,s=this._getZoomSpan();for(var d in this._zoomBoundLayers){var p=this._zoomBoundLayers[d].options;n=p.minZoom===void 0?n:Math.min(n,p.minZoom),a=p.maxZoom===void 0?a:Math.max(a,p.maxZoom)}this._layersMaxZoom=a===-1/0?void 0:a,this._layersMinZoom=n===1/0?void 0:n,s!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var _i=nn.extend({initialize:function(n,a){U(this,a),this._layers={};var s,d;if(n)for(s=0,d=n.length;s<d;s++)this.addLayer(n[s])},addLayer:function(n){var a=this.getLayerId(n);return this._layers[a]=n,this._map&&this._map.addLayer(n),this},removeLayer:function(n){var a=n in this._layers?n:this.getLayerId(n);return this._map&&this._layers[a]&&this._map.removeLayer(this._layers[a]),delete this._layers[a],this},hasLayer:function(n){var a=typeof n=="number"?n:this.getLayerId(n);return a in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(n){var a=Array.prototype.slice.call(arguments,1),s,d;for(s in this._layers)d=this._layers[s],d[n]&&d[n].apply(d,a);return this},onAdd:function(n){this.eachLayer(n.addLayer,n)},onRemove:function(n){this.eachLayer(n.removeLayer,n)},eachLayer:function(n,a){for(var s in this._layers)n.call(a,this._layers[s]);return this},getLayer:function(n){return this._layers[n]},getLayers:function(){var n=[];return this.eachLayer(n.push,n),n},setZIndex:function(n){return this.invoke("setZIndex",n)},getLayerId:function(n){return w(n)}}),al=function(n,a){return new _i(n,a)},Pe=_i.extend({addLayer:function(n){return this.hasLayer(n)?this:(n.addEventParent(this),_i.prototype.addLayer.call(this,n),this.fire("layeradd",{layer:n}))},removeLayer:function(n){return this.hasLayer(n)?(n in this._layers&&(n=this._layers[n]),n.removeEventParent(this),_i.prototype.removeLayer.call(this,n),this.fire("layerremove",{layer:n})):this},setStyle:function(n){return this.invoke("setStyle",n)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var n=new tt;for(var a in this._layers){var s=this._layers[a];n.extend(s.getBounds?s.getBounds():s.getLatLng())}return n}}),Ga=function(n,a){return new Pe(n,a)},$i=Se.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(n){U(this,n)},createIcon:function(n){return this._createIcon("icon",n)},createShadow:function(n){return this._createIcon("shadow",n)},_createIcon:function(n,a){var s=this._getIconUrl(n);if(!s){if(n==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var d=this._createImg(s,a&&a.tagName==="IMG"?a:null);return this._setIconStyles(d,n),(this.options.crossOrigin||this.options.crossOrigin==="")&&(d.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),d},_setIconStyles:function(n,a){var s=this.options,d=s[a+"Size"];typeof d=="number"&&(d=[d,d]);var p=O(d),b=O(a==="shadow"&&s.shadowAnchor||s.iconAnchor||p&&p.divideBy(2,!0));n.className="leaflet-marker-"+a+" "+(s.className||""),b&&(n.style.marginLeft=-b.x+"px",n.style.marginTop=-b.y+"px"),p&&(n.style.width=p.x+"px",n.style.height=p.y+"px")},_createImg:function(n,a){return a=a||document.createElement("img"),a.src=n,a},_getIconUrl:function(n){return dt.retina&&this.options[n+"RetinaUrl"]||this.options[n+"Url"]}});function Mr(n){return new $i(n)}var ta=$i.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(n){return typeof ta.imagePath!="string"&&(ta.imagePath=this._detectIconPath()),(this.options.imagePath||ta.imagePath)+$i.prototype._getIconUrl.call(this,n)},_stripUrl:function(n){var a=function(s,d,p){var b=d.exec(s);return b&&b[p]};return n=a(n,/^url\((['"])?(.+)\1\)$/,2),n&&a(n,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var n=Rt("div","leaflet-default-icon-path",document.body),a=li(n,"background-image")||li(n,"backgroundImage");if(document.body.removeChild(n),a=this._stripUrl(a),a)return a;var s=document.querySelector('link[href$="leaflet.css"]');return s?s.href.substring(0,s.href.length-11-1):""}}),Ro=en.extend({initialize:function(n){this._marker=n},addHooks:function(){var n=this._marker._icon;this._draggable||(this._draggable=new kn(n,n,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),bt(n,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&Jt(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(n){var a=this._marker,s=a._map,d=this._marker.options.autoPanSpeed,p=this._marker.options.autoPanPadding,b=Zn(a._icon),A=s.getPixelBounds(),j=s.getPixelOrigin(),q=nt(A.min._subtract(j).add(p),A.max._subtract(j).subtract(p));if(!q.contains(b)){var I=O((Math.max(q.max.x,b.x)-q.max.x)/(A.max.x-q.max.x)-(Math.min(q.min.x,b.x)-q.min.x)/(A.min.x-q.min.x),(Math.max(q.max.y,b.y)-q.max.y)/(A.max.y-q.max.y)-(Math.min(q.min.y,b.y)-q.min.y)/(A.min.y-q.min.y)).multiplyBy(d);s.panBy(I,{animate:!1}),this._draggable._newPos._add(I),this._draggable._startPos._add(I),ee(a._icon,this._draggable._newPos),this._onDrag(n),this._panRequest=Xt(this._adjustPan.bind(this,n))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(n){this._marker.options.autoPan&&(Zt(this._panRequest),this._panRequest=Xt(this._adjustPan.bind(this,n)))},_onDrag:function(n){var a=this._marker,s=a._shadow,d=Zn(a._icon),p=a._map.layerPointToLatLng(d);s&&ee(s,d),a._latlng=p,n.latlng=p,n.oldLatLng=this._oldLatLng,a.fire("move",n).fire("drag",n)},_onDragEnd:function(n){Zt(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",n)}}),ea=nn.extend({options:{icon:new ta,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(n,a){U(this,a),this._latlng=at(n)},onAdd:function(n){this._zoomAnimated=this._zoomAnimated&&n.options.markerZoomAnimation,this._zoomAnimated&&n.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(n){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&n.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(n){var a=this._latlng;return this._latlng=at(n),this.update(),this.fire("move",{oldLatLng:a,latlng:this._latlng})},setZIndexOffset:function(n){return this.options.zIndexOffset=n,this.update()},getIcon:function(){return this.options.icon},setIcon:function(n){return this.options.icon=n,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var n=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(n)}return this},_initIcon:function(){var n=this.options,a="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),s=n.icon.createIcon(this._icon),d=!1;s!==this._icon&&(this._icon&&this._removeIcon(),d=!0,n.title&&(s.title=n.title),s.tagName==="IMG"&&(s.alt=n.alt||"")),bt(s,a),n.keyboard&&(s.tabIndex="0",s.setAttribute("role","button")),this._icon=s,n.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&yt(s,"focus",this._panOnFocus,this);var p=n.icon.createShadow(this._shadow),b=!1;p!==this._shadow&&(this._removeShadow(),b=!0),p&&(bt(p,a),p.alt=""),this._shadow=p,n.opacity<1&&this._updateOpacity(),d&&this.getPane().appendChild(this._icon),this._initInteraction(),p&&b&&this.getPane(n.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&Dt(this._icon,"focus",this._panOnFocus,this),Kt(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&Kt(this._shadow),this._shadow=null},_setPos:function(n){this._icon&&ee(this._icon,n),this._shadow&&ee(this._shadow,n),this._zIndex=n.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(n){this._icon&&(this._icon.style.zIndex=this._zIndex+n)},_animateZoom:function(n){var a=this._map._latLngToNewLayerPoint(this._latlng,n.zoom,n.center).round();this._setPos(a)},_initInteraction:function(){if(this.options.interactive&&(bt(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),Ro)){var n=this.options.draggable;this.dragging&&(n=this.dragging.enabled(),this.dragging.disable()),this.dragging=new Ro(this),n&&this.dragging.enable()}},setOpacity:function(n){return this.options.opacity=n,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var n=this.options.opacity;this._icon&&Ne(this._icon,n),this._shadow&&Ne(this._shadow,n)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var n=this._map;if(n){var a=this.options.icon.options,s=a.iconSize?O(a.iconSize):O(0,0),d=a.iconAnchor?O(a.iconAnchor):O(0,0);n.panInside(this._latlng,{paddingTopLeft:d,paddingBottomRight:s.subtract(d)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function Mo(n,a){return new ea(n,a)}var Rn=nn.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(n){this._renderer=n.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(n){return U(this,n),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&n&&Object.prototype.hasOwnProperty.call(n,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),Va=Rn.extend({options:{fill:!0,radius:10},initialize:function(n,a){U(this,a),this._latlng=at(n),this._radius=this.options.radius},setLatLng:function(n){var a=this._latlng;return this._latlng=at(n),this.redraw(),this.fire("move",{oldLatLng:a,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(n){return this.options.radius=this._radius=n,this.redraw()},getRadius:function(){return this._radius},setStyle:function(n){var a=n&&n.radius||this._radius;return Rn.prototype.setStyle.call(this,n),this.setRadius(a),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var n=this._radius,a=this._radiusY||n,s=this._clickTolerance(),d=[n+s,a+s];this._pxBounds=new Q(this._point.subtract(d),this._point.add(d))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(n){return n.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function rl(n,a){return new Va(n,a)}var Co=Va.extend({initialize:function(n,a,s){if(typeof a=="number"&&(a=m({},s,{radius:a})),U(this,a),this._latlng=at(n),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(n){return this._mRadius=n,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var n=[this._radius,this._radiusY||this._radius];return new tt(this._map.layerPointToLatLng(this._point.subtract(n)),this._map.layerPointToLatLng(this._point.add(n)))},setStyle:Rn.prototype.setStyle,_project:function(){var n=this._latlng.lng,a=this._latlng.lat,s=this._map,d=s.options.crs;if(d.distance===zt.distance){var p=Math.PI/180,b=this._mRadius/zt.R/p,A=s.project([a+b,n]),j=s.project([a-b,n]),q=A.add(j).divideBy(2),I=s.unproject(q).lat,ot=Math.acos((Math.cos(b*p)-Math.sin(a*p)*Math.sin(I*p))/(Math.cos(a*p)*Math.cos(I*p)))/p;(isNaN(ot)||ot===0)&&(ot=b/Math.cos(Math.PI/180*a)),this._point=q.subtract(s.getPixelOrigin()),this._radius=isNaN(ot)?0:q.x-s.project([I,n-ot]).x,this._radiusY=q.y-A.y}else{var ut=d.unproject(d.project(this._latlng).subtract([this._mRadius,0]));this._point=s.latLngToLayerPoint(this._latlng),this._radius=this._point.x-s.latLngToLayerPoint(ut).x}this._updateBounds()}});function Ju(n,a,s){return new Co(n,a,s)}var Mn=Rn.extend({options:{smoothFactor:1,noClip:!1},initialize:function(n,a){U(this,a),this._setLatLngs(n)},getLatLngs:function(){return this._latlngs},setLatLngs:function(n){return this._setLatLngs(n),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(n){for(var a=1/0,s=null,d=pi,p,b,A=0,j=this._parts.length;A<j;A++)for(var q=this._parts[A],I=1,ot=q.length;I<ot;I++){p=q[I-1],b=q[I];var ut=d(n,p,b,!0);ut<a&&(a=ut,s=d(n,p,b))}return s&&(s.distance=Math.sqrt(a)),s},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Ao(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(n,a){return a=a||this._defaultShape(),n=at(n),a.push(n),this._bounds.extend(n),this.redraw()},_setLatLngs:function(n){this._bounds=new tt,this._latlngs=this._convertLatLngs(n)},_defaultShape:function(){return ge(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(n){for(var a=[],s=ge(n),d=0,p=n.length;d<p;d++)s?(a[d]=at(n[d]),this._bounds.extend(a[d])):a[d]=this._convertLatLngs(n[d]);return a},_project:function(){var n=new Q;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,n),this._bounds.isValid()&&n.isValid()&&(this._rawPxBounds=n,this._updateBounds())},_updateBounds:function(){var n=this._clickTolerance(),a=new $(n,n);this._rawPxBounds&&(this._pxBounds=new Q([this._rawPxBounds.min.subtract(a),this._rawPxBounds.max.add(a)]))},_projectLatlngs:function(n,a,s){var d=n[0]instanceof ft,p=n.length,b,A;if(d){for(A=[],b=0;b<p;b++)A[b]=this._map.latLngToLayerPoint(n[b]),s.extend(A[b]);a.push(A)}else for(b=0;b<p;b++)this._projectLatlngs(n[b],a,s)},_clipPoints:function(){var n=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(n))){if(this.options.noClip){this._parts=this._rings;return}var a=this._parts,s,d,p,b,A,j,q;for(s=0,p=0,b=this._rings.length;s<b;s++)for(q=this._rings[s],d=0,A=q.length;d<A-1;d++)j=$s(q[d],q[d+1],n,d,!0),j&&(a[p]=a[p]||[],a[p].push(j[0]),(j[1]!==q[d+1]||d===A-2)&&(a[p].push(j[1]),p++))}},_simplifyPoints:function(){for(var n=this._parts,a=this.options.smoothFactor,s=0,d=n.length;s<d;s++)n[s]=Ws(n[s],a)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(n,a){var s,d,p,b,A,j,q=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(n))return!1;for(s=0,b=this._parts.length;s<b;s++)for(j=this._parts[s],d=0,A=j.length,p=A-1;d<A;p=d++)if(!(!a&&d===0)&&Lo(n,j[p],j[d])<=q)return!0;return!1}});function Fu(n,a){return new Mn(n,a)}Mn._flat=tl;var na=Mn.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Lr(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(n){var a=Mn.prototype._convertLatLngs.call(this,n),s=a.length;return s>=2&&a[0]instanceof ft&&a[0].equals(a[s-1])&&a.pop(),a},_setLatLngs:function(n){Mn.prototype._setLatLngs.call(this,n),ge(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return ge(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var n=this._renderer._bounds,a=this.options.weight,s=new $(a,a);if(n=new Q(n.min.subtract(s),n.max.add(s)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(n))){if(this.options.noClip){this._parts=this._rings;return}for(var d=0,p=this._rings.length,b;d<p;d++)b=Fs(this._rings[d],n,!0),b.length&&this._parts.push(b)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(n){var a=!1,s,d,p,b,A,j,q,I;if(!this._pxBounds||!this._pxBounds.contains(n))return!1;for(b=0,q=this._parts.length;b<q;b++)for(s=this._parts[b],A=0,I=s.length,j=I-1;A<I;j=A++)d=s[A],p=s[j],d.y>n.y!=p.y>n.y&&n.x<(p.x-d.x)*(n.y-d.y)/(p.y-d.y)+d.x&&(a=!a);return a||Mn.prototype._containsPoint.call(this,n,!0)}});function Ze(n,a){return new na(n,a)}var He=Pe.extend({initialize:function(n,a){U(this,a),this._layers={},n&&this.addData(n)},addData:function(n){var a=et(n)?n:n.features,s,d,p;if(a){for(s=0,d=a.length;s<d;s++)p=a[s],(p.geometries||p.geometry||p.features||p.coordinates)&&this.addData(p);return this}var b=this.options;if(b.filter&&!b.filter(n))return this;var A=Xa(n,b);return A?(A.feature=ia(n),A.defaultOptions=A.options,this.resetStyle(A),b.onEachFeature&&b.onEachFeature(n,A),this.addLayer(A)):this},resetStyle:function(n){return n===void 0?this.eachLayer(this.resetStyle,this):(n.options=m({},n.defaultOptions),this._setLayerStyle(n,this.options.style),this)},setStyle:function(n){return this.eachLayer(function(a){this._setLayerStyle(a,n)},this)},_setLayerStyle:function(n,a){n.setStyle&&(typeof a=="function"&&(a=a(n.feature)),n.setStyle(a))}});function Xa(n,a){var s=n.type==="Feature"?n.geometry:n,d=s?s.coordinates:null,p=[],b=a&&a.pointToLayer,A=a&&a.coordsToLatLng||Cr,j,q,I,ot;if(!d&&!s)return null;switch(s.type){case"Point":return j=A(d),No(b,n,j,a);case"MultiPoint":for(I=0,ot=d.length;I<ot;I++)j=A(d[I]),p.push(No(b,n,j,a));return new Pe(p);case"LineString":case"MultiLineString":return q=Qa(d,s.type==="LineString"?0:1,A),new Mn(q,a);case"Polygon":case"MultiPolygon":return q=Qa(d,s.type==="Polygon"?1:2,A),new na(q,a);case"GeometryCollection":for(I=0,ot=s.geometries.length;I<ot;I++){var ut=Xa({geometry:s.geometries[I],type:"Feature",properties:n.properties},a);ut&&p.push(ut)}return new Pe(p);case"FeatureCollection":for(I=0,ot=s.features.length;I<ot;I++){var ht=Xa(s.features[I],a);ht&&p.push(ht)}return new Pe(p);default:throw new Error("Invalid GeoJSON object.")}}function No(n,a,s,d){return n?n(a,s):new ea(s,d&&d.markersInheritOptions&&d)}function Cr(n){return new ft(n[1],n[0],n[2])}function Qa(n,a,s){for(var d=[],p=0,b=n.length,A;p<b;p++)A=a?Qa(n[p],a-1,s):(s||Cr)(n[p]),d.push(A);return d}function Ka(n,a){return n=at(n),n.alt!==void 0?[B(n.lng,a),B(n.lat,a),B(n.alt,a)]:[B(n.lng,a),B(n.lat,a)]}function Nr(n,a,s,d){for(var p=[],b=0,A=n.length;b<A;b++)p.push(a?Nr(n[b],ge(n[b])?0:a-1,s,d):Ka(n[b],d));return!a&&s&&p.length>0&&p.push(p[0].slice()),p}function an(n,a){return n.feature?m({},n.feature,{geometry:a}):ia(a)}function ia(n){return n.type==="Feature"||n.type==="FeatureCollection"?n:{type:"Feature",properties:{},geometry:n}}var gi={toGeoJSON:function(n){return an(this,{type:"Point",coordinates:Ka(this.getLatLng(),n)})}};ea.include(gi),Co.include(gi),Va.include(gi),Mn.include({toGeoJSON:function(n){var a=!ge(this._latlngs),s=Nr(this._latlngs,a?1:0,!1,n);return an(this,{type:(a?"Multi":"")+"LineString",coordinates:s})}}),na.include({toGeoJSON:function(n){var a=!ge(this._latlngs),s=a&&!ge(this._latlngs[0]),d=Nr(this._latlngs,s?2:a?1:0,!0,n);return a||(d=[d]),an(this,{type:(s?"Multi":"")+"Polygon",coordinates:d})}}),_i.include({toMultiPoint:function(n){var a=[];return this.eachLayer(function(s){a.push(s.toGeoJSON(n).geometry.coordinates)}),an(this,{type:"MultiPoint",coordinates:a})},toGeoJSON:function(n){var a=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(a==="MultiPoint")return this.toMultiPoint(n);var s=a==="GeometryCollection",d=[];return this.eachLayer(function(p){if(p.toGeoJSON){var b=p.toGeoJSON(n);if(s)d.push(b.geometry);else{var A=ia(b);A.type==="FeatureCollection"?d.push.apply(d,A.features):d.push(A)}}}),s?an(this,{geometries:d,type:"GeometryCollection"}):{type:"FeatureCollection",features:d}}});function zr(n,a){return new He(n,a)}var ol=zr,vn=nn.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(n,a,s){this._url=n,this._bounds=st(a),U(this,s)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(bt(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){Kt(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(n){return this.options.opacity=n,this._image&&this._updateOpacity(),this},setStyle:function(n){return n.opacity&&this.setOpacity(n.opacity),this},bringToFront:function(){return this._map&&ui(this._image),this},bringToBack:function(){return this._map&&Yi(this._image),this},setUrl:function(n){return this._url=n,this._image&&(this._image.src=n),this},setBounds:function(n){return this._bounds=st(n),this._map&&this._reset(),this},getEvents:function(){var n={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(n.zoomanim=this._animateZoom),n},setZIndex:function(n){return this.options.zIndex=n,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var n=this._url.tagName==="IMG",a=this._image=n?this._url:Rt("img");if(bt(a,"leaflet-image-layer"),this._zoomAnimated&&bt(a,"leaflet-zoom-animated"),this.options.className&&bt(a,this.options.className),a.onselectstart=z,a.onmousemove=z,a.onload=v(this.fire,this,"load"),a.onerror=v(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(a.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),n){this._url=a.src;return}a.src=this._url,a.alt=this.options.alt},_animateZoom:function(n){var a=this._map.getZoomScale(n.zoom),s=this._map._latLngBoundsToNewLayerBounds(this._bounds,n.zoom,n.center).min;Qe(this._image,s,a)},_reset:function(){var n=this._image,a=new Q(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),s=a.getSize();ee(n,a.min),n.style.width=s.x+"px",n.style.height=s.y+"px"},_updateOpacity:function(){Ne(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var n=this.options.errorOverlayUrl;n&&this._url!==n&&(this._url=n,this._image.src=n)},getCenter:function(){return this._bounds.getCenter()}}),aa=function(n,a,s){return new vn(n,a,s)},Dr=vn.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var n=this._url.tagName==="VIDEO",a=this._image=n?this._url:Rt("video");if(bt(a,"leaflet-image-layer"),this._zoomAnimated&&bt(a,"leaflet-zoom-animated"),this.options.className&&bt(a,this.options.className),a.onselectstart=z,a.onmousemove=z,a.onloadeddata=v(this.fire,this,"load"),n){for(var s=a.getElementsByTagName("source"),d=[],p=0;p<s.length;p++)d.push(s[p].src);this._url=s.length>0?d:[a.src];return}et(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(a.style,"objectFit")&&(a.style.objectFit="fill"),a.autoplay=!!this.options.autoplay,a.loop=!!this.options.loop,a.muted=!!this.options.muted,a.playsInline=!!this.options.playsInline;for(var b=0;b<this._url.length;b++){var A=Rt("source");A.src=this._url[b],a.appendChild(A)}}});function sl(n,a,s){return new Dr(n,a,s)}var qn=vn.extend({_initImage:function(){var n=this._image=this._url;bt(n,"leaflet-image-layer"),this._zoomAnimated&&bt(n,"leaflet-zoom-animated"),this.options.className&&bt(n,this.options.className),n.onselectstart=z,n.onmousemove=z}});function ll(n,a,s){return new qn(n,a,s)}var rn=nn.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(n,a){n&&(n instanceof ft||et(n))?(this._latlng=at(n),U(this,a)):(U(this,n),this._source=a),this.options.content&&(this._content=this.options.content)},openOn:function(n){return n=arguments.length?n:this._source._map,n.hasLayer(this)||n.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(n){return this._map?this.close():(arguments.length?this._source=n:n=this._source,this._prepareOpen(),this.openOn(n._map)),this},onAdd:function(n){this._zoomAnimated=n._zoomAnimated,this._container||this._initLayout(),n._fadeAnimated&&Ne(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),n._fadeAnimated&&Ne(this._container,1),this.bringToFront(),this.options.interactive&&(bt(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(n){n._fadeAnimated?(Ne(this._container,0),this._removeTimeout=setTimeout(v(Kt,void 0,this._container),200)):Kt(this._container),this.options.interactive&&(Jt(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(n){return this._latlng=at(n),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(n){return this._content=n,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var n={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(n.zoomanim=this._animateZoom),n},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&ui(this._container),this},bringToBack:function(){return this._map&&Yi(this._container),this},_prepareOpen:function(n){var a=this._source;if(!a._map)return!1;if(a instanceof Pe){a=null;var s=this._source._layers;for(var d in s)if(s[d]._map){a=s[d];break}if(!a)return!1;this._source=a}if(!n)if(a.getCenter)n=a.getCenter();else if(a.getLatLng)n=a.getLatLng();else if(a.getBounds)n=a.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(n),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var n=this._contentNode,a=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof a=="string")n.innerHTML=a;else{for(;n.hasChildNodes();)n.removeChild(n.firstChild);n.appendChild(a)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var n=this._map.latLngToLayerPoint(this._latlng),a=O(this.options.offset),s=this._getAnchor();this._zoomAnimated?ee(this._container,n.add(s)):a=a.add(n).add(s);var d=this._containerBottom=-a.y,p=this._containerLeft=-Math.round(this._containerWidth/2)+a.x;this._container.style.bottom=d+"px",this._container.style.left=p+"px"}},_getAnchor:function(){return[0,0]}});Tt.include({_initOverlay:function(n,a,s,d){var p=a;return p instanceof n||(p=new n(d).setContent(a)),s&&p.setLatLng(s),p}}),nn.include({_initOverlay:function(n,a,s,d){var p=s;return p instanceof n?(U(p,d),p._source=this):(p=a&&!d?a:new n(d,this),p.setContent(s)),p}});var Ja=rn.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(n){return n=arguments.length?n:this._source._map,!n.hasLayer(this)&&n._popup&&n._popup.options.autoClose&&n.removeLayer(n._popup),n._popup=this,rn.prototype.openOn.call(this,n)},onAdd:function(n){rn.prototype.onAdd.call(this,n),n.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof Rn||this._source.on("preclick",An))},onRemove:function(n){rn.prototype.onRemove.call(this,n),n.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof Rn||this._source.off("preclick",An))},getEvents:function(){var n=rn.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(n.preclick=this.close),this.options.keepInView&&(n.moveend=this._adjustPan),n},_initLayout:function(){var n="leaflet-popup",a=this._container=Rt("div",n+" "+(this.options.className||"")+" leaflet-zoom-animated"),s=this._wrapper=Rt("div",n+"-content-wrapper",a);if(this._contentNode=Rt("div",n+"-content",s),Fi(a),Ji(this._contentNode),yt(a,"contextmenu",An),this._tipContainer=Rt("div",n+"-tip-container",a),this._tip=Rt("div",n+"-tip",this._tipContainer),this.options.closeButton){var d=this._closeButton=Rt("a",n+"-close-button",a);d.setAttribute("role","button"),d.setAttribute("aria-label","Close popup"),d.href="#close",d.innerHTML='<span aria-hidden="true">&#215;</span>',yt(d,"click",function(p){re(p),this.close()},this)}},_updateLayout:function(){var n=this._contentNode,a=n.style;a.width="",a.whiteSpace="nowrap";var s=n.offsetWidth;s=Math.min(s,this.options.maxWidth),s=Math.max(s,this.options.minWidth),a.width=s+1+"px",a.whiteSpace="",a.height="";var d=n.offsetHeight,p=this.options.maxHeight,b="leaflet-popup-scrolled";p&&d>p?(a.height=p+"px",bt(n,b)):Jt(n,b),this._containerWidth=this._container.offsetWidth},_animateZoom:function(n){var a=this._map._latLngToNewLayerPoint(this._latlng,n.zoom,n.center),s=this._getAnchor();ee(this._container,a.add(s))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var n=this._map,a=parseInt(li(this._container,"marginBottom"),10)||0,s=this._container.offsetHeight+a,d=this._containerWidth,p=new $(this._containerLeft,-s-this._containerBottom);p._add(Zn(this._container));var b=n.layerPointToContainerPoint(p),A=O(this.options.autoPanPadding),j=O(this.options.autoPanPaddingTopLeft||A),q=O(this.options.autoPanPaddingBottomRight||A),I=n.getSize(),ot=0,ut=0;b.x+d+q.x>I.x&&(ot=b.x+d-I.x+q.x),b.x-ot-j.x<0&&(ot=b.x-j.x),b.y+s+q.y>I.y&&(ut=b.y+s-I.y+q.y),b.y-ut-j.y<0&&(ut=b.y-j.y),(ot||ut)&&(this.options.keepInView&&(this._autopanning=!0),n.fire("autopanstart").panBy([ot,ut]))}},_getAnchor:function(){return O(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),Wu=function(n,a){return new Ja(n,a)};Tt.mergeOptions({closePopupOnClick:!0}),Tt.include({openPopup:function(n,a,s){return this._initOverlay(Ja,n,a,s).openOn(this),this},closePopup:function(n){return n=arguments.length?n:this._popup,n&&n.close(),this}}),nn.include({bindPopup:function(n,a){return this._popup=this._initOverlay(Ja,this._popup,n,a),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(n){return this._popup&&(this instanceof Pe||(this._popup._source=this),this._popup._prepareOpen(n||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(n){return this._popup&&this._popup.setContent(n),this},getPopup:function(){return this._popup},_openPopup:function(n){if(!(!this._popup||!this._map)){gn(n);var a=n.layer||n.target;if(this._popup._source===a&&!(a instanceof Rn)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(n.latlng);return}this._popup._source=a,this.openPopup(n.latlng)}},_movePopup:function(n){this._popup.setLatLng(n.latlng)},_onKeyPress:function(n){n.originalEvent.keyCode===13&&this._openPopup(n)}});var Br=rn.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(n){rn.prototype.onAdd.call(this,n),this.setOpacity(this.options.opacity),n.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(n){rn.prototype.onRemove.call(this,n),n.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var n=rn.prototype.getEvents.call(this);return this.options.permanent||(n.preclick=this.close),n},_initLayout:function(){var n="leaflet-tooltip",a=n+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=Rt("div",a),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+w(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(n){var a,s,d=this._map,p=this._container,b=d.latLngToContainerPoint(d.getCenter()),A=d.layerPointToContainerPoint(n),j=this.options.direction,q=p.offsetWidth,I=p.offsetHeight,ot=O(this.options.offset),ut=this._getAnchor();j==="top"?(a=q/2,s=I):j==="bottom"?(a=q/2,s=0):j==="center"?(a=q/2,s=I/2):j==="right"?(a=0,s=I/2):j==="left"?(a=q,s=I/2):A.x<b.x?(j="right",a=0,s=I/2):(j="left",a=q+(ot.x+ut.x)*2,s=I/2),n=n.subtract(O(a,s,!0)).add(ot).add(ut),Jt(p,"leaflet-tooltip-right"),Jt(p,"leaflet-tooltip-left"),Jt(p,"leaflet-tooltip-top"),Jt(p,"leaflet-tooltip-bottom"),bt(p,"leaflet-tooltip-"+j),ee(p,n)},_updatePosition:function(){var n=this._map.latLngToLayerPoint(this._latlng);this._setPosition(n)},setOpacity:function(n){this.options.opacity=n,this._container&&Ne(this._container,n)},_animateZoom:function(n){var a=this._map._latLngToNewLayerPoint(this._latlng,n.zoom,n.center);this._setPosition(a)},_getAnchor:function(){return O(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),Iu=function(n,a){return new Br(n,a)};Tt.include({openTooltip:function(n,a,s){return this._initOverlay(Br,n,a,s).openOn(this),this},closeTooltip:function(n){return n.close(),this}}),nn.include({bindTooltip:function(n,a){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(Br,this._tooltip,n,a),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(n){if(!(!n&&this._tooltipHandlersAdded)){var a=n?"off":"on",s={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?s.add=this._openTooltip:(s.mouseover=this._openTooltip,s.mouseout=this.closeTooltip,s.click=this._openTooltip,this._map?this._addFocusListeners():s.add=this._addFocusListeners),this._tooltip.options.sticky&&(s.mousemove=this._moveTooltip),this[a](s),this._tooltipHandlersAdded=!n}},openTooltip:function(n){return this._tooltip&&(this instanceof Pe||(this._tooltip._source=this),this._tooltip._prepareOpen(n)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(n){return this._tooltip&&this._tooltip.setContent(n),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(n){var a=typeof n.getElement=="function"&&n.getElement();a&&(yt(a,"focus",function(){this._tooltip._source=n,this.openTooltip()},this),yt(a,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(n){var a=typeof n.getElement=="function"&&n.getElement();a&&a.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(n){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var a=this;this._map.once("moveend",function(){a._openOnceFlag=!1,a._openTooltip(n)});return}this._tooltip._source=n.layer||n.target,this.openTooltip(this._tooltip.options.sticky?n.latlng:void 0)}},_moveTooltip:function(n){var a=n.latlng,s,d;this._tooltip.options.sticky&&n.originalEvent&&(s=this._map.mouseEventToContainerPoint(n.originalEvent),d=this._map.containerPointToLayerPoint(s),a=this._map.layerPointToLatLng(d)),this._tooltip.setLatLng(a)}});var zo=$i.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(n){var a=n&&n.tagName==="DIV"?n:document.createElement("div"),s=this.options;if(s.html instanceof Element?(Ee(a),a.appendChild(s.html)):a.innerHTML=s.html!==!1?s.html:"",s.bgPos){var d=O(s.bgPos);a.style.backgroundPosition=-d.x+"px "+-d.y+"px"}return this._setIconStyles(a,"icon"),a},createShadow:function(){return null}});function ul(n){return new zo(n)}$i.Default=ta;var vi=nn.extend({options:{tileSize:256,opacity:1,updateWhenIdle:dt.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(n){U(this,n)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(n){n._addZoomLimit(this)},onRemove:function(n){this._removeAllTiles(),Kt(this._container),n._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(ui(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(Yi(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(n){return this.options.opacity=n,this._updateOpacity(),this},setZIndex:function(n){return this.options.zIndex=n,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var n=this._clampZoom(this._map.getZoom());n!==this._tileZoom&&(this._tileZoom=n,this._updateLevels()),this._update()}return this},getEvents:function(){var n={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=y(this._onMoveEnd,this.options.updateInterval,this)),n.move=this._onMove),this._zoomAnimated&&(n.zoomanim=this._animateZoom),n},createTile:function(){return document.createElement("div")},getTileSize:function(){var n=this.options.tileSize;return n instanceof $?n:new $(n,n)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(n){for(var a=this.getPane().children,s=-n(-1/0,1/0),d=0,p=a.length,b;d<p;d++)b=a[d].style.zIndex,a[d]!==this._container&&b&&(s=n(s,+b));isFinite(s)&&(this.options.zIndex=s+n(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!dt.ielt9){Ne(this._container,this.options.opacity);var n=+new Date,a=!1,s=!1;for(var d in this._tiles){var p=this._tiles[d];if(!(!p.current||!p.loaded)){var b=Math.min(1,(n-p.loaded)/200);Ne(p.el,b),b<1?a=!0:(p.active?s=!0:this._onOpaqueTile(p),p.active=!0)}}s&&!this._noPrune&&this._pruneTiles(),a&&(Zt(this._fadeFrame),this._fadeFrame=Xt(this._updateOpacity,this))}},_onOpaqueTile:z,_initContainer:function(){this._container||(this._container=Rt("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var n=this._tileZoom,a=this.options.maxZoom;if(n!==void 0){for(var s in this._levels)s=Number(s),this._levels[s].el.children.length||s===n?(this._levels[s].el.style.zIndex=a-Math.abs(n-s),this._onUpdateLevel(s)):(Kt(this._levels[s].el),this._removeTilesAtZoom(s),this._onRemoveLevel(s),delete this._levels[s]);var d=this._levels[n],p=this._map;return d||(d=this._levels[n]={},d.el=Rt("div","leaflet-tile-container leaflet-zoom-animated",this._container),d.el.style.zIndex=a,d.origin=p.project(p.unproject(p.getPixelOrigin()),n).round(),d.zoom=n,this._setZoomTransform(d,p.getCenter(),p.getZoom()),z(d.el.offsetWidth),this._onCreateLevel(d)),this._level=d,d}},_onUpdateLevel:z,_onRemoveLevel:z,_onCreateLevel:z,_pruneTiles:function(){if(this._map){var n,a,s=this._map.getZoom();if(s>this.options.maxZoom||s<this.options.minZoom){this._removeAllTiles();return}for(n in this._tiles)a=this._tiles[n],a.retain=a.current;for(n in this._tiles)if(a=this._tiles[n],a.current&&!a.active){var d=a.coords;this._retainParent(d.x,d.y,d.z,d.z-5)||this._retainChildren(d.x,d.y,d.z,d.z+2)}for(n in this._tiles)this._tiles[n].retain||this._removeTile(n)}},_removeTilesAtZoom:function(n){for(var a in this._tiles)this._tiles[a].coords.z===n&&this._removeTile(a)},_removeAllTiles:function(){for(var n in this._tiles)this._removeTile(n)},_invalidateAll:function(){for(var n in this._levels)Kt(this._levels[n].el),this._onRemoveLevel(Number(n)),delete this._levels[n];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(n,a,s,d){var p=Math.floor(n/2),b=Math.floor(a/2),A=s-1,j=new $(+p,+b);j.z=+A;var q=this._tileCoordsToKey(j),I=this._tiles[q];return I&&I.active?(I.retain=!0,!0):(I&&I.loaded&&(I.retain=!0),A>d?this._retainParent(p,b,A,d):!1)},_retainChildren:function(n,a,s,d){for(var p=2*n;p<2*n+2;p++)for(var b=2*a;b<2*a+2;b++){var A=new $(p,b);A.z=s+1;var j=this._tileCoordsToKey(A),q=this._tiles[j];if(q&&q.active){q.retain=!0;continue}else q&&q.loaded&&(q.retain=!0);s+1<d&&this._retainChildren(p,b,s+1,d)}},_resetView:function(n){var a=n&&(n.pinch||n.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),a,a)},_animateZoom:function(n){this._setView(n.center,n.zoom,!0,n.noUpdate)},_clampZoom:function(n){var a=this.options;return a.minNativeZoom!==void 0&&n<a.minNativeZoom?a.minNativeZoom:a.maxNativeZoom!==void 0&&a.maxNativeZoom<n?a.maxNativeZoom:n},_setView:function(n,a,s,d){var p=Math.round(a);this.options.maxZoom!==void 0&&p>this.options.maxZoom||this.options.minZoom!==void 0&&p<this.options.minZoom?p=void 0:p=this._clampZoom(p);var b=this.options.updateWhenZooming&&p!==this._tileZoom;(!d||b)&&(this._tileZoom=p,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),p!==void 0&&this._update(n),s||this._pruneTiles(),this._noPrune=!!s),this._setZoomTransforms(n,a)},_setZoomTransforms:function(n,a){for(var s in this._levels)this._setZoomTransform(this._levels[s],n,a)},_setZoomTransform:function(n,a,s){var d=this._map.getZoomScale(s,n.zoom),p=n.origin.multiplyBy(d).subtract(this._map._getNewPixelOrigin(a,s)).round();dt.any3d?Qe(n.el,p,d):ee(n.el,p)},_resetGrid:function(){var n=this._map,a=n.options.crs,s=this._tileSize=this.getTileSize(),d=this._tileZoom,p=this._map.getPixelWorldBounds(this._tileZoom);p&&(this._globalTileRange=this._pxBoundsToTileRange(p)),this._wrapX=a.wrapLng&&!this.options.noWrap&&[Math.floor(n.project([0,a.wrapLng[0]],d).x/s.x),Math.ceil(n.project([0,a.wrapLng[1]],d).x/s.y)],this._wrapY=a.wrapLat&&!this.options.noWrap&&[Math.floor(n.project([a.wrapLat[0],0],d).y/s.x),Math.ceil(n.project([a.wrapLat[1],0],d).y/s.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(n){var a=this._map,s=a._animatingZoom?Math.max(a._animateToZoom,a.getZoom()):a.getZoom(),d=a.getZoomScale(s,this._tileZoom),p=a.project(n,this._tileZoom).floor(),b=a.getSize().divideBy(d*2);return new Q(p.subtract(b),p.add(b))},_update:function(n){var a=this._map;if(a){var s=this._clampZoom(a.getZoom());if(n===void 0&&(n=a.getCenter()),this._tileZoom!==void 0){var d=this._getTiledPixelBounds(n),p=this._pxBoundsToTileRange(d),b=p.getCenter(),A=[],j=this.options.keepBuffer,q=new Q(p.getBottomLeft().subtract([j,-j]),p.getTopRight().add([j,-j]));if(!(isFinite(p.min.x)&&isFinite(p.min.y)&&isFinite(p.max.x)&&isFinite(p.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var I in this._tiles){var ot=this._tiles[I].coords;(ot.z!==this._tileZoom||!q.contains(new $(ot.x,ot.y)))&&(this._tiles[I].current=!1)}if(Math.abs(s-this._tileZoom)>1){this._setView(n,s);return}for(var ut=p.min.y;ut<=p.max.y;ut++)for(var ht=p.min.x;ht<=p.max.x;ht++){var vt=new $(ht,ut);if(vt.z=this._tileZoom,!!this._isValidTile(vt)){var Ft=this._tiles[this._tileCoordsToKey(vt)];Ft?Ft.current=!0:A.push(vt)}}if(A.sort(function(ve,Ke){return ve.distanceTo(b)-Ke.distanceTo(b)}),A.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var ne=document.createDocumentFragment();for(ht=0;ht<A.length;ht++)this._addTile(A[ht],ne);this._level.el.appendChild(ne)}}}},_isValidTile:function(n){var a=this._map.options.crs;if(!a.infinite){var s=this._globalTileRange;if(!a.wrapLng&&(n.x<s.min.x||n.x>s.max.x)||!a.wrapLat&&(n.y<s.min.y||n.y>s.max.y))return!1}if(!this.options.bounds)return!0;var d=this._tileCoordsToBounds(n);return st(this.options.bounds).overlaps(d)},_keyToBounds:function(n){return this._tileCoordsToBounds(this._keyToTileCoords(n))},_tileCoordsToNwSe:function(n){var a=this._map,s=this.getTileSize(),d=n.scaleBy(s),p=d.add(s),b=a.unproject(d,n.z),A=a.unproject(p,n.z);return[b,A]},_tileCoordsToBounds:function(n){var a=this._tileCoordsToNwSe(n),s=new tt(a[0],a[1]);return this.options.noWrap||(s=this._map.wrapLatLngBounds(s)),s},_tileCoordsToKey:function(n){return n.x+":"+n.y+":"+n.z},_keyToTileCoords:function(n){var a=n.split(":"),s=new $(+a[0],+a[1]);return s.z=+a[2],s},_removeTile:function(n){var a=this._tiles[n];a&&(Kt(a.el),delete this._tiles[n],this.fire("tileunload",{tile:a.el,coords:this._keyToTileCoords(n)}))},_initTile:function(n){bt(n,"leaflet-tile");var a=this.getTileSize();n.style.width=a.x+"px",n.style.height=a.y+"px",n.onselectstart=z,n.onmousemove=z,dt.ielt9&&this.options.opacity<1&&Ne(n,this.options.opacity)},_addTile:function(n,a){var s=this._getTilePos(n),d=this._tileCoordsToKey(n),p=this.createTile(this._wrapCoords(n),v(this._tileReady,this,n));this._initTile(p),this.createTile.length<2&&Xt(v(this._tileReady,this,n,null,p)),ee(p,s),this._tiles[d]={el:p,coords:n,current:!0},a.appendChild(p),this.fire("tileloadstart",{tile:p,coords:n})},_tileReady:function(n,a,s){a&&this.fire("tileerror",{error:a,tile:s,coords:n});var d=this._tileCoordsToKey(n);s=this._tiles[d],s&&(s.loaded=+new Date,this._map._fadeAnimated?(Ne(s.el,0),Zt(this._fadeFrame),this._fadeFrame=Xt(this._updateOpacity,this)):(s.active=!0,this._pruneTiles()),a||(bt(s.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:s.el,coords:n})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),dt.ielt9||!this._map._fadeAnimated?Xt(this._pruneTiles,this):setTimeout(v(this._pruneTiles,this),250)))},_getTilePos:function(n){return n.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(n){var a=new $(this._wrapX?E(n.x,this._wrapX):n.x,this._wrapY?E(n.y,this._wrapY):n.y);return a.z=n.z,a},_pxBoundsToTileRange:function(n){var a=this.getTileSize();return new Q(n.min.unscaleBy(a).floor(),n.max.unscaleBy(a).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var n in this._tiles)if(!this._tiles[n].loaded)return!1;return!0}});function on(n){return new vi(n)}var yi=vi.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(n,a){this._url=n,a=U(this,a),a.detectRetina&&dt.retina&&a.maxZoom>0?(a.tileSize=Math.floor(a.tileSize/2),a.zoomReverse?(a.zoomOffset--,a.minZoom=Math.min(a.maxZoom,a.minZoom+1)):(a.zoomOffset++,a.maxZoom=Math.max(a.minZoom,a.maxZoom-1)),a.minZoom=Math.max(0,a.minZoom)):a.zoomReverse?a.minZoom=Math.min(a.maxZoom,a.minZoom):a.maxZoom=Math.max(a.minZoom,a.maxZoom),typeof a.subdomains=="string"&&(a.subdomains=a.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(n,a){return this._url===n&&a===void 0&&(a=!0),this._url=n,a||this.redraw(),this},createTile:function(n,a){var s=document.createElement("img");return yt(s,"load",v(this._tileOnLoad,this,a,s)),yt(s,"error",v(this._tileOnError,this,a,s)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(s.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(s.referrerPolicy=this.options.referrerPolicy),s.alt="",s.src=this.getTileUrl(n),s},getTileUrl:function(n){var a={r:dt.retina?"@2x":"",s:this._getSubdomain(n),x:n.x,y:n.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var s=this._globalTileRange.max.y-n.y;this.options.tms&&(a.y=s),a["-y"]=s}return it(this._url,m(a,this.options))},_tileOnLoad:function(n,a){dt.ielt9?setTimeout(v(n,this,null,a),0):n(null,a)},_tileOnError:function(n,a,s){var d=this.options.errorTileUrl;d&&a.getAttribute("src")!==d&&(a.src=d),n(s,a)},_onTileRemove:function(n){n.tile.onload=null},_getZoomForUrl:function(){var n=this._tileZoom,a=this.options.maxZoom,s=this.options.zoomReverse,d=this.options.zoomOffset;return s&&(n=a-n),n+d},_getSubdomain:function(n){var a=Math.abs(n.x+n.y)%this.options.subdomains.length;return this.options.subdomains[a]},_abortLoading:function(){var n,a;for(n in this._tiles)if(this._tiles[n].coords.z!==this._tileZoom&&(a=this._tiles[n].el,a.onload=z,a.onerror=z,!a.complete)){a.src=lt;var s=this._tiles[n].coords;Kt(a),delete this._tiles[n],this.fire("tileabort",{tile:a,coords:s})}},_removeTile:function(n){var a=this._tiles[n];if(a)return a.el.setAttribute("src",lt),vi.prototype._removeTile.call(this,n)},_tileReady:function(n,a,s){if(!(!this._map||s&&s.getAttribute("src")===lt))return vi.prototype._tileReady.call(this,n,a,s)}});function ke(n,a){return new yi(n,a)}var qe=yi.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(n,a){this._url=n;var s=m({},this.defaultWmsParams);for(var d in a)d in this.options||(s[d]=a[d]);a=U(this,a);var p=a.detectRetina&&dt.retina?2:1,b=this.getTileSize();s.width=b.x*p,s.height=b.y*p,this.wmsParams=s},onAdd:function(n){this._crs=this.options.crs||n.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var a=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[a]=this._crs.code,yi.prototype.onAdd.call(this,n)},getTileUrl:function(n){var a=this._tileCoordsToNwSe(n),s=this._crs,d=nt(s.project(a[0]),s.project(a[1])),p=d.min,b=d.max,A=(this._wmsVersion>=1.3&&this._crs===il?[p.y,p.x,b.y,b.x]:[p.x,p.y,b.x,b.y]).join(","),j=yi.prototype.getTileUrl.call(this,n);return j+Y(this.wmsParams,j,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+A},setParams:function(n,a){return m(this.wmsParams,n),a||this.redraw(),this}});function ra(n,a){return new qe(n,a)}yi.WMS=qe,ke.wms=ra;var sn=nn.extend({options:{padding:.1},initialize:function(n){U(this,n),w(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),bt(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var n={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(n.zoomanim=this._onAnimZoom),n},_onAnimZoom:function(n){this._updateTransform(n.center,n.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(n,a){var s=this._map.getZoomScale(a,this._zoom),d=this._map.getSize().multiplyBy(.5+this.options.padding),p=this._map.project(this._center,a),b=d.multiplyBy(-s).add(p).subtract(this._map._getNewPixelOrigin(n,a));dt.any3d?Qe(this._container,b,s):ee(this._container,b)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var n in this._layers)this._layers[n]._reset()},_onZoomEnd:function(){for(var n in this._layers)this._layers[n]._project()},_updatePaths:function(){for(var n in this._layers)this._layers[n]._update()},_update:function(){var n=this.options.padding,a=this._map.getSize(),s=this._map.containerPointToLayerPoint(a.multiplyBy(-n)).round();this._bounds=new Q(s,s.add(a.multiplyBy(1+n*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),Fa=sn.extend({options:{tolerance:0},getEvents:function(){var n=sn.prototype.getEvents.call(this);return n.viewprereset=this._onViewPreReset,n},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){sn.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var n=this._container=document.createElement("canvas");yt(n,"mousemove",this._onMouseMove,this),yt(n,"click dblclick mousedown mouseup contextmenu",this._onClick,this),yt(n,"mouseout",this._handleMouseOut,this),n._leaflet_disable_events=!0,this._ctx=n.getContext("2d")},_destroyContainer:function(){Zt(this._redrawRequest),delete this._ctx,Kt(this._container),Dt(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var n;this._redrawBounds=null;for(var a in this._layers)n=this._layers[a],n._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){sn.prototype._update.call(this);var n=this._bounds,a=this._container,s=n.getSize(),d=dt.retina?2:1;ee(a,n.min),a.width=d*s.x,a.height=d*s.y,a.style.width=s.x+"px",a.style.height=s.y+"px",dt.retina&&this._ctx.scale(2,2),this._ctx.translate(-n.min.x,-n.min.y),this.fire("update")}},_reset:function(){sn.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(n){this._updateDashArray(n),this._layers[w(n)]=n;var a=n._order={layer:n,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=a),this._drawLast=a,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(n){this._requestRedraw(n)},_removePath:function(n){var a=n._order,s=a.next,d=a.prev;s?s.prev=d:this._drawLast=d,d?d.next=s:this._drawFirst=s,delete n._order,delete this._layers[w(n)],this._requestRedraw(n)},_updatePath:function(n){this._extendRedrawBounds(n),n._project(),n._update(),this._requestRedraw(n)},_updateStyle:function(n){this._updateDashArray(n),this._requestRedraw(n)},_updateDashArray:function(n){if(typeof n.options.dashArray=="string"){var a=n.options.dashArray.split(/[, ]+/),s=[],d,p;for(p=0;p<a.length;p++){if(d=Number(a[p]),isNaN(d))return;s.push(d)}n.options._dashArray=s}else n.options._dashArray=n.options.dashArray},_requestRedraw:function(n){this._map&&(this._extendRedrawBounds(n),this._redrawRequest=this._redrawRequest||Xt(this._redraw,this))},_extendRedrawBounds:function(n){if(n._pxBounds){var a=(n.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new Q,this._redrawBounds.extend(n._pxBounds.min.subtract([a,a])),this._redrawBounds.extend(n._pxBounds.max.add([a,a]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var n=this._redrawBounds;if(n){var a=n.getSize();this._ctx.clearRect(n.min.x,n.min.y,a.x,a.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var n,a=this._redrawBounds;if(this._ctx.save(),a){var s=a.getSize();this._ctx.beginPath(),this._ctx.rect(a.min.x,a.min.y,s.x,s.y),this._ctx.clip()}this._drawing=!0;for(var d=this._drawFirst;d;d=d.next)n=d.layer,(!a||n._pxBounds&&n._pxBounds.intersects(a))&&n._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(n,a){if(this._drawing){var s,d,p,b,A=n._parts,j=A.length,q=this._ctx;if(j){for(q.beginPath(),s=0;s<j;s++){for(d=0,p=A[s].length;d<p;d++)b=A[s][d],q[d?"lineTo":"moveTo"](b.x,b.y);a&&q.closePath()}this._fillStroke(q,n)}}},_updateCircle:function(n){if(!(!this._drawing||n._empty())){var a=n._point,s=this._ctx,d=Math.max(Math.round(n._radius),1),p=(Math.max(Math.round(n._radiusY),1)||d)/d;p!==1&&(s.save(),s.scale(1,p)),s.beginPath(),s.arc(a.x,a.y/p,d,0,Math.PI*2,!1),p!==1&&s.restore(),this._fillStroke(s,n)}},_fillStroke:function(n,a){var s=a.options;s.fill&&(n.globalAlpha=s.fillOpacity,n.fillStyle=s.fillColor||s.color,n.fill(s.fillRule||"evenodd")),s.stroke&&s.weight!==0&&(n.setLineDash&&n.setLineDash(a.options&&a.options._dashArray||[]),n.globalAlpha=s.opacity,n.lineWidth=s.weight,n.strokeStyle=s.color,n.lineCap=s.lineCap,n.lineJoin=s.lineJoin,n.stroke())},_onClick:function(n){for(var a=this._map.mouseEventToLayerPoint(n),s,d,p=this._drawFirst;p;p=p.next)s=p.layer,s.options.interactive&&s._containsPoint(a)&&(!(n.type==="click"||n.type==="preclick")||!this._map._draggableMoved(s))&&(d=s);this._fireEvent(d?[d]:!1,n)},_onMouseMove:function(n){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var a=this._map.mouseEventToLayerPoint(n);this._handleMouseHover(n,a)}},_handleMouseOut:function(n){var a=this._hoveredLayer;a&&(Jt(this._container,"leaflet-interactive"),this._fireEvent([a],n,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(n,a){if(!this._mouseHoverThrottled){for(var s,d,p=this._drawFirst;p;p=p.next)s=p.layer,s.options.interactive&&s._containsPoint(a)&&(d=s);d!==this._hoveredLayer&&(this._handleMouseOut(n),d&&(bt(this._container,"leaflet-interactive"),this._fireEvent([d],n,"mouseover"),this._hoveredLayer=d)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,n),this._mouseHoverThrottled=!0,setTimeout(v(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(n,a,s){this._map._fireDOMEvent(a,s||a.type,n)},_bringToFront:function(n){var a=n._order;if(a){var s=a.next,d=a.prev;if(s)s.prev=d;else return;d?d.next=s:s&&(this._drawFirst=s),a.prev=this._drawLast,this._drawLast.next=a,a.next=null,this._drawLast=a,this._requestRedraw(n)}},_bringToBack:function(n){var a=n._order;if(a){var s=a.next,d=a.prev;if(d)d.next=s;else return;s?s.prev=d:d&&(this._drawLast=d),a.prev=null,a.next=this._drawFirst,this._drawFirst.prev=a,this._drawFirst=a,this._requestRedraw(n)}}});function Wa(n){return dt.canvas?new Fa(n):null}var bi=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(n){return document.createElement("<lvml:"+n+' class="lvml">')}}catch{}return function(n){return document.createElement("<"+n+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),oa={_initContainer:function(){this._container=Rt("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(sn.prototype._update.call(this),this.fire("update"))},_initPath:function(n){var a=n._container=bi("shape");bt(a,"leaflet-vml-shape "+(this.options.className||"")),a.coordsize="1 1",n._path=bi("path"),a.appendChild(n._path),this._updateStyle(n),this._layers[w(n)]=n},_addPath:function(n){var a=n._container;this._container.appendChild(a),n.options.interactive&&n.addInteractiveTarget(a)},_removePath:function(n){var a=n._container;Kt(a),n.removeInteractiveTarget(a),delete this._layers[w(n)]},_updateStyle:function(n){var a=n._stroke,s=n._fill,d=n.options,p=n._container;p.stroked=!!d.stroke,p.filled=!!d.fill,d.stroke?(a||(a=n._stroke=bi("stroke")),p.appendChild(a),a.weight=d.weight+"px",a.color=d.color,a.opacity=d.opacity,d.dashArray?a.dashStyle=et(d.dashArray)?d.dashArray.join(" "):d.dashArray.replace(/( *, *)/g," "):a.dashStyle="",a.endcap=d.lineCap.replace("butt","flat"),a.joinstyle=d.lineJoin):a&&(p.removeChild(a),n._stroke=null),d.fill?(s||(s=n._fill=bi("fill")),p.appendChild(s),s.color=d.fillColor||d.color,s.opacity=d.fillOpacity):s&&(p.removeChild(s),n._fill=null)},_updateCircle:function(n){var a=n._point.round(),s=Math.round(n._radius),d=Math.round(n._radiusY||s);this._setPath(n,n._empty()?"M0 0":"AL "+a.x+","+a.y+" "+s+","+d+" 0,"+65535*360)},_setPath:function(n,a){n._path.v=a},_bringToFront:function(n){ui(n._container)},_bringToBack:function(n){Yi(n._container)}},Ia=dt.vml?bi:Cs,Yn=sn.extend({_initContainer:function(){this._container=Ia("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=Ia("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){Kt(this._container),Dt(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){sn.prototype._update.call(this);var n=this._bounds,a=n.getSize(),s=this._container;(!this._svgSize||!this._svgSize.equals(a))&&(this._svgSize=a,s.setAttribute("width",a.x),s.setAttribute("height",a.y)),ee(s,n.min),s.setAttribute("viewBox",[n.min.x,n.min.y,a.x,a.y].join(" ")),this.fire("update")}},_initPath:function(n){var a=n._path=Ia("path");n.options.className&&bt(a,n.options.className),n.options.interactive&&bt(a,"leaflet-interactive"),this._updateStyle(n),this._layers[w(n)]=n},_addPath:function(n){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(n._path),n.addInteractiveTarget(n._path)},_removePath:function(n){Kt(n._path),n.removeInteractiveTarget(n._path),delete this._layers[w(n)]},_updatePath:function(n){n._project(),n._update()},_updateStyle:function(n){var a=n._path,s=n.options;a&&(s.stroke?(a.setAttribute("stroke",s.color),a.setAttribute("stroke-opacity",s.opacity),a.setAttribute("stroke-width",s.weight),a.setAttribute("stroke-linecap",s.lineCap),a.setAttribute("stroke-linejoin",s.lineJoin),s.dashArray?a.setAttribute("stroke-dasharray",s.dashArray):a.removeAttribute("stroke-dasharray"),s.dashOffset?a.setAttribute("stroke-dashoffset",s.dashOffset):a.removeAttribute("stroke-dashoffset")):a.setAttribute("stroke","none"),s.fill?(a.setAttribute("fill",s.fillColor||s.color),a.setAttribute("fill-opacity",s.fillOpacity),a.setAttribute("fill-rule",s.fillRule||"evenodd")):a.setAttribute("fill","none"))},_updatePoly:function(n,a){this._setPath(n,Ns(n._parts,a))},_updateCircle:function(n){var a=n._point,s=Math.max(Math.round(n._radius),1),d=Math.max(Math.round(n._radiusY),1)||s,p="a"+s+","+d+" 0 1,0 ",b=n._empty()?"M0 0":"M"+(a.x-s)+","+a.y+p+s*2+",0 "+p+-s*2+",0 ";this._setPath(n,b)},_setPath:function(n,a){n._path.setAttribute("d",a)},_bringToFront:function(n){ui(n._path)},_bringToBack:function(n){Yi(n._path)}});dt.vml&&Yn.include(oa);function xi(n){return dt.svg||dt.vml?new Yn(n):null}Tt.include({getRenderer:function(n){var a=n.options.renderer||this._getPaneRenderer(n.options.pane)||this.options.renderer||this._renderer;return a||(a=this._renderer=this._createRenderer()),this.hasLayer(a)||this.addLayer(a),a},_getPaneRenderer:function(n){if(n==="overlayPane"||n===void 0)return!1;var a=this._paneRenderers[n];return a===void 0&&(a=this._createRenderer({pane:n}),this._paneRenderers[n]=a),a},_createRenderer:function(n){return this.options.preferCanvas&&Wa(n)||xi(n)}});var cl=na.extend({initialize:function(n,a){na.prototype.initialize.call(this,this._boundsToLatLngs(n),a)},setBounds:function(n){return this.setLatLngs(this._boundsToLatLngs(n))},_boundsToLatLngs:function(n){return n=st(n),[n.getSouthWest(),n.getNorthWest(),n.getNorthEast(),n.getSouthEast()]}});function Ye(n,a){return new cl(n,a)}Yn.create=Ia,Yn.pointsToPath=Ns,He.geometryToLayer=Xa,He.coordsToLatLng=Cr,He.coordsToLatLngs=Qa,He.latLngToCoords=Ka,He.latLngsToCoords=Nr,He.getFeature=an,He.asFeature=ia,Tt.mergeOptions({boxZoom:!0});var jr=en.extend({initialize:function(n){this._map=n,this._container=n._container,this._pane=n._panes.overlayPane,this._resetStateTimeout=0,n.on("unload",this._destroy,this)},addHooks:function(){yt(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){Dt(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){Kt(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(n){if(!n.shiftKey||n.which!==1&&n.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),mn(),Za(),this._startPoint=this._map.mouseEventToContainerPoint(n),yt(document,{contextmenu:gn,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(n){this._moved||(this._moved=!0,this._box=Rt("div","leaflet-zoom-box",this._container),bt(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(n);var a=new Q(this._point,this._startPoint),s=a.getSize();ee(this._box,a.min),this._box.style.width=s.x+"px",this._box.style.height=s.y+"px"},_finish:function(){this._moved&&(Kt(this._box),Jt(this._container,"leaflet-crosshair")),Pa(),bo(),Dt(document,{contextmenu:gn,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(n){if(!(n.which!==1&&n.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(v(this._resetState,this),0);var a=new tt(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(a).fire("boxzoomend",{boxZoomBounds:a})}},_onKeyDown:function(n){n.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});Tt.addInitHook("addHandler","boxZoom",jr),Tt.mergeOptions({doubleClickZoom:!0});var yn=en.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(n){var a=this._map,s=a.getZoom(),d=a.options.zoomDelta,p=n.originalEvent.shiftKey?s-d:s+d;a.options.doubleClickZoom==="center"?a.setZoom(p):a.setZoomAround(n.containerPoint,p)}});Tt.addInitHook("addHandler","doubleClickZoom",yn),Tt.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var Do=en.extend({addHooks:function(){if(!this._draggable){var n=this._map;this._draggable=new kn(n._mapPane,n._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),n.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),n.on("zoomend",this._onZoomEnd,this),n.whenReady(this._onZoomEnd,this))}bt(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){Jt(this._map._container,"leaflet-grab"),Jt(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var n=this._map;if(n._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var a=st(this._map.options.maxBounds);this._offsetLimit=nt(this._map.latLngToContainerPoint(a.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(a.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;n.fire("movestart").fire("dragstart"),n.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(n){if(this._map.options.inertia){var a=this._lastTime=+new Date,s=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(s),this._times.push(a),this._prunePositions(a)}this._map.fire("move",n).fire("drag",n)},_prunePositions:function(n){for(;this._positions.length>1&&n-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var n=this._map.getSize().divideBy(2),a=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=a.subtract(n).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(n,a){return n-(n-a)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var n=this._draggable._newPos.subtract(this._draggable._startPos),a=this._offsetLimit;n.x<a.min.x&&(n.x=this._viscousLimit(n.x,a.min.x)),n.y<a.min.y&&(n.y=this._viscousLimit(n.y,a.min.y)),n.x>a.max.x&&(n.x=this._viscousLimit(n.x,a.max.x)),n.y>a.max.y&&(n.y=this._viscousLimit(n.y,a.max.y)),this._draggable._newPos=this._draggable._startPos.add(n)}},_onPreDragWrap:function(){var n=this._worldWidth,a=Math.round(n/2),s=this._initialWorldOffset,d=this._draggable._newPos.x,p=(d-a+s)%n+a-s,b=(d+a+s)%n-a-s,A=Math.abs(p+s)<Math.abs(b+s)?p:b;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=A},_onDragEnd:function(n){var a=this._map,s=a.options,d=!s.inertia||n.noInertia||this._times.length<2;if(a.fire("dragend",n),d)a.fire("moveend");else{this._prunePositions(+new Date);var p=this._lastPos.subtract(this._positions[0]),b=(this._lastTime-this._times[0])/1e3,A=s.easeLinearity,j=p.multiplyBy(A/b),q=j.distanceTo([0,0]),I=Math.min(s.inertiaMaxSpeed,q),ot=j.multiplyBy(I/q),ut=I/(s.inertiaDeceleration*A),ht=ot.multiplyBy(-ut/2).round();!ht.x&&!ht.y?a.fire("moveend"):(ht=a._limitOffset(ht,a.options.maxBounds),Xt(function(){a.panBy(ht,{duration:ut,easeLinearity:A,noMoveStart:!0,animate:!0})}))}}});Tt.addInitHook("addHandler","dragging",Do),Tt.mergeOptions({keyboard:!0,keyboardPanDelta:80});var $a=en.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(n){this._map=n,this._setPanDelta(n.options.keyboardPanDelta),this._setZoomDelta(n.options.zoomDelta)},addHooks:function(){var n=this._map._container;n.tabIndex<=0&&(n.tabIndex="0"),yt(n,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),Dt(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var n=document.body,a=document.documentElement,s=n.scrollTop||a.scrollTop,d=n.scrollLeft||a.scrollLeft;this._map._container.focus(),window.scrollTo(d,s)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(n){var a=this._panKeys={},s=this.keyCodes,d,p;for(d=0,p=s.left.length;d<p;d++)a[s.left[d]]=[-1*n,0];for(d=0,p=s.right.length;d<p;d++)a[s.right[d]]=[n,0];for(d=0,p=s.down.length;d<p;d++)a[s.down[d]]=[0,n];for(d=0,p=s.up.length;d<p;d++)a[s.up[d]]=[0,-1*n]},_setZoomDelta:function(n){var a=this._zoomKeys={},s=this.keyCodes,d,p;for(d=0,p=s.zoomIn.length;d<p;d++)a[s.zoomIn[d]]=n;for(d=0,p=s.zoomOut.length;d<p;d++)a[s.zoomOut[d]]=-n},_addHooks:function(){yt(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){Dt(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(n){if(!(n.altKey||n.ctrlKey||n.metaKey)){var a=n.keyCode,s=this._map,d;if(a in this._panKeys){if(!s._panAnim||!s._panAnim._inProgress)if(d=this._panKeys[a],n.shiftKey&&(d=O(d).multiplyBy(3)),s.options.maxBounds&&(d=s._limitOffset(O(d),s.options.maxBounds)),s.options.worldCopyJump){var p=s.wrapLatLng(s.unproject(s.project(s.getCenter()).add(d)));s.panTo(p)}else s.panBy(d)}else if(a in this._zoomKeys)s.setZoom(s.getZoom()+(n.shiftKey?3:1)*this._zoomKeys[a]);else if(a===27&&s._popup&&s._popup.options.closeOnEscapeKey)s.closePopup();else return;gn(n)}}});Tt.addInitHook("addHandler","keyboard",$a),Tt.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var Gn=en.extend({addHooks:function(){yt(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){Dt(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(n){var a=qa(n),s=this._map.options.wheelDebounceTime;this._delta+=a,this._lastMousePos=this._map.mouseEventToContainerPoint(n),this._startTime||(this._startTime=+new Date);var d=Math.max(s-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(v(this._performZoom,this),d),gn(n)},_performZoom:function(){var n=this._map,a=n.getZoom(),s=this._map.options.zoomSnap||0;n._stop();var d=this._delta/(this._map.options.wheelPxPerZoomLevel*4),p=4*Math.log(2/(1+Math.exp(-Math.abs(d))))/Math.LN2,b=s?Math.ceil(p/s)*s:p,A=n._limitZoom(a+(this._delta>0?b:-b))-a;this._delta=0,this._startTime=null,A&&(n.options.scrollWheelZoom==="center"?n.setZoom(a+A):n.setZoomAround(this._lastMousePos,a+A))}});Tt.addInitHook("addHandler","scrollWheelZoom",Gn);var Bo=600;Tt.mergeOptions({tapHold:dt.touchNative&&dt.safari&&dt.mobile,tapTolerance:15});var Ur=en.extend({addHooks:function(){yt(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){Dt(this._map._container,"touchstart",this._onDown,this)},_onDown:function(n){if(clearTimeout(this._holdTimeout),n.touches.length===1){var a=n.touches[0];this._startPos=this._newPos=new $(a.clientX,a.clientY),this._holdTimeout=setTimeout(v(function(){this._cancel(),this._isTapValid()&&(yt(document,"touchend",re),yt(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",a))},this),Bo),yt(document,"touchend touchcancel contextmenu",this._cancel,this),yt(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function n(){Dt(document,"touchend",re),Dt(document,"touchend touchcancel",n)},_cancel:function(){clearTimeout(this._holdTimeout),Dt(document,"touchend touchcancel contextmenu",this._cancel,this),Dt(document,"touchmove",this._onMove,this)},_onMove:function(n){var a=n.touches[0];this._newPos=new $(a.clientX,a.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(n,a){var s=new MouseEvent(n,{bubbles:!0,cancelable:!0,view:window,screenX:a.screenX,screenY:a.screenY,clientX:a.clientX,clientY:a.clientY});s._simulated=!0,a.target.dispatchEvent(s)}});Tt.addInitHook("addHandler","tapHold",Ur),Tt.mergeOptions({touchZoom:dt.touch,bounceAtZoomLimits:!0});var wi=en.extend({addHooks:function(){bt(this._map._container,"leaflet-touch-zoom"),yt(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){Jt(this._map._container,"leaflet-touch-zoom"),Dt(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(n){var a=this._map;if(!(!n.touches||n.touches.length!==2||a._animatingZoom||this._zooming)){var s=a.mouseEventToContainerPoint(n.touches[0]),d=a.mouseEventToContainerPoint(n.touches[1]);this._centerPoint=a.getSize()._divideBy(2),this._startLatLng=a.containerPointToLatLng(this._centerPoint),a.options.touchZoom!=="center"&&(this._pinchStartLatLng=a.containerPointToLatLng(s.add(d)._divideBy(2))),this._startDist=s.distanceTo(d),this._startZoom=a.getZoom(),this._moved=!1,this._zooming=!0,a._stop(),yt(document,"touchmove",this._onTouchMove,this),yt(document,"touchend touchcancel",this._onTouchEnd,this),re(n)}},_onTouchMove:function(n){if(!(!n.touches||n.touches.length!==2||!this._zooming)){var a=this._map,s=a.mouseEventToContainerPoint(n.touches[0]),d=a.mouseEventToContainerPoint(n.touches[1]),p=s.distanceTo(d)/this._startDist;if(this._zoom=a.getScaleZoom(p,this._startZoom),!a.options.bounceAtZoomLimits&&(this._zoom<a.getMinZoom()&&p<1||this._zoom>a.getMaxZoom()&&p>1)&&(this._zoom=a._limitZoom(this._zoom)),a.options.touchZoom==="center"){if(this._center=this._startLatLng,p===1)return}else{var b=s._add(d)._divideBy(2)._subtract(this._centerPoint);if(p===1&&b.x===0&&b.y===0)return;this._center=a.unproject(a.project(this._pinchStartLatLng,this._zoom).subtract(b),this._zoom)}this._moved||(a._moveStart(!0,!1),this._moved=!0),Zt(this._animRequest);var A=v(a._move,a,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=Xt(A,this,!0),re(n)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,Zt(this._animRequest),Dt(document,"touchmove",this._onTouchMove,this),Dt(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});Tt.addInitHook("addHandler","touchZoom",wi),Tt.BoxZoom=jr,Tt.DoubleClickZoom=yn,Tt.Drag=Do,Tt.Keyboard=$a,Tt.ScrollWheelZoom=Gn,Tt.TapHold=Ur,Tt.TouchZoom=wi,c.Bounds=Q,c.Browser=dt,c.CRS=te,c.Canvas=Fa,c.Circle=Co,c.CircleMarker=Va,c.Class=Se,c.Control=ze,c.DivIcon=zo,c.DivOverlay=rn,c.DomEvent=Uu,c.DomUtil=Xs,c.Draggable=kn,c.Evented=rt,c.FeatureGroup=Pe,c.GeoJSON=He,c.GridLayer=vi,c.Handler=en,c.Icon=$i,c.ImageOverlay=vn,c.LatLng=ft,c.LatLngBounds=tt,c.Layer=nn,c.LayerGroup=_i,c.LineUtil=el,c.Map=Tt,c.Marker=ea,c.Mixin=qu,c.Path=Rn,c.Point=$,c.PolyUtil=Yu,c.Polygon=na,c.Polyline=Mn,c.Popup=Ja,c.PosAnimation=Tr,c.Projection=nl,c.Rectangle=cl,c.Renderer=sn,c.SVG=Yn,c.SVGOverlay=qn,c.TileLayer=yi,c.Tooltip=Br,c.Transformation=ji,c.Util=ti,c.VideoOverlay=Dr,c.bind=v,c.bounds=nt,c.canvas=Wa,c.circle=Ju,c.circleMarker=rl,c.control=Wi,c.divIcon=ul,c.extend=m,c.featureGroup=Ga,c.geoJSON=zr,c.geoJson=ol,c.gridLayer=on,c.icon=Mr,c.imageOverlay=aa,c.latLng=at,c.latLngBounds=st,c.layerGroup=al,c.map=Ya,c.marker=Mo,c.point=O,c.polygon=Ze,c.polyline=Fu,c.popup=Wu,c.rectangle=Ye,c.setOptions=U,c.stamp=w,c.svg=xi,c.svgOverlay=ll,c.tileLayer=ke,c.tooltip=Iu,c.transformation=ei,c.version=h,c.videoOverlay=sl;var sa=window.L;c.noConflict=function(){return window.L=sa,this},window.L=c})}(bs,bs.exports)),bs.exports}var Ms=bx();const R_=Cp(Ms);function uh(o,u,c){return Object.freeze({instance:o,context:u,container:c})}function ch(o,u){return u==null?function(h,m){const _=M.useRef(void 0);return _.current||(_.current=o(h,m)),_}:function(h,m){const _=M.useRef(void 0);_.current||(_.current=o(h,m));const v=M.useRef(h),{instance:S}=_.current;return M.useEffect(function(){v.current!==h&&(u(S,h,v.current),v.current=h)},[S,h,u]),_}}function xx(o,u){M.useEffect(function(){return(u.layerContainer??u.map).addLayer(o.instance),function(){u.layerContainer?.removeLayer(o.instance),u.map.removeLayer(o.instance)}},[u,o])}function M_(o){return function(c){const h=O_(),m=o(lh(c,h),h);return L_(h.map,c.attribution),A_(m.current,c.eventHandlers),xx(m.current,h),m}}function wx(o,u){const c=ch(o,u),h=M_(c);return px(h)}function Sx(o,u){const c=ch(o),h=vx(c,u);return _x(h)}function Ex(o,u){const c=ch(o,u),h=M_(c);return gx(h)}function Tx(o,u,c){const{opacity:h,zIndex:m}=u;h!=null&&h!==c.opacity&&o.setOpacity(h),m!=null&&m!==c.zIndex&&o.setZIndex(m)}function Lx({bounds:o,boundsOptions:u,center:c,children:h,className:m,id:_,placeholder:v,style:S,whenReady:w,zoom:y,...E},z){const[B]=M.useState({className:m,id:_,style:S}),[J,N]=M.useState(null),U=M.useRef(void 0);M.useImperativeHandle(z,()=>J?.map??null,[J]);const Y=M.useCallback(it=>{if(it!==null&&!U.current){const et=new Ms.Map(it,E);U.current=et,c!=null&&y!=null?et.setView(c,y):o!=null&&et.fitBounds(o,u),w!=null&&et.whenReady(w),N(dx(et))}},[]);M.useEffect(()=>()=>{J?.map.remove()},[J]);const X=J?Uf.createElement(sh,{value:J},h):v??null;return Uf.createElement("div",{...B,ref:Y},X)}const Ox=M.forwardRef(Lx),Ax=wx(function({position:u,...c},h){const m=new Ms.Marker(u,c);return uh(m,mx(h,{overlayContainer:m}))},function(u,c,h){c.position!==h.position&&u.setLatLng(c.position),c.icon!=null&&c.icon!==h.icon&&u.setIcon(c.icon),c.zIndexOffset!=null&&c.zIndexOffset!==h.zIndexOffset&&u.setZIndexOffset(c.zIndexOffset),c.opacity!=null&&c.opacity!==h.opacity&&u.setOpacity(c.opacity),u.dragging!=null&&c.draggable!==h.draggable&&(c.draggable===!0?u.dragging.enable():u.dragging.disable())}),Rx=Sx(function(u,c){const h=new Ms.Popup(u,c.overlayContainer);return uh(h,c)},function(u,c,{position:h},m){M.useEffect(function(){const{instance:v}=u;function S(y){y.popup===v&&(v.update(),m(!0))}function w(y){y.popup===v&&m(!1)}return c.map.on({popupopen:S,popupclose:w}),c.overlayContainer==null?(h!=null&&v.setLatLng(h),v.openOn(c.map)):c.overlayContainer.bindPopup(v),function(){c.map.off({popupopen:S,popupclose:w}),c.overlayContainer?.unbindPopup(),c.map.removeLayer(v)}},[u,c,m,h])}),Mx=Ex(function({url:u,...c},h){const m=new Ms.TileLayer(u,lh(c,h));return uh(m,h)},function(u,c,h){Tx(u,c,h);const{url:m}=c;m!=null&&m!==h.url&&u.setUrl(m)});delete R_.Icon.Default.prototype._getIconUrl;R_.Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"});const Cx=()=>{const[o,u]=M.useState({totalIssues:0,openIssues:0,resolvedIssues:0,resolutionRate:0}),[c,h]=M.useState([]),[m,_]=M.useState(!0);M.useEffect(()=>{v()},[]);const v=async()=>{try{const[y,E]=await Promise.all([qt.get("http://localhost:5000/api/analytics/overview"),qt.get("http://localhost:5000/api/issues?limit=5&sort=-createdAt")]);u(y.data.data.overview),h(E.data.data.issues)}catch(y){console.error("Error fetching data:",y)}finally{_(!1)}},S=y=>{switch(y){case"resolved":return T.jsx(Ap,{className:"w-5 h-5 text-green-500"});case"in_progress":return T.jsx(Ib,{className:"w-5 h-5 text-yellow-500"});default:return T.jsx(Rp,{className:"w-5 h-5 text-red-500"})}},w=y=>{const E={pothole:"bg-orange-100 text-orange-800",garbage:"bg-gray-100 text-gray-800",water_leak:"bg-blue-100 text-blue-800",broken_light:"bg-yellow-100 text-yellow-800",damaged_road:"bg-red-100 text-red-800",other:"bg-purple-100 text-purple-800"};return E[y]||E.other};return m?T.jsx("div",{className:"flex items-center justify-center min-h-screen",children:T.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):T.jsxs("div",{className:"space-y-8",children:[T.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg text-white p-8",children:T.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[T.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Welcome to CityPulse"}),T.jsx("p",{className:"text-xl mb-8 opacity-90",children:"Report urban issues, track progress, and help make your city better"}),T.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[T.jsxs(we,{to:"/report",className:"inline-flex items-center px-6 py-3 bg-white text-blue-600 font-medium rounded-lg hover:bg-gray-100 transition-colors",children:[T.jsx(T_,{className:"w-5 h-5 mr-2"}),"Report an Issue"]}),T.jsxs(we,{to:"/dashboard",className:"inline-flex items-center px-6 py-3 bg-blue-700 text-white font-medium rounded-lg hover:bg-blue-800 transition-colors",children:[T.jsx(Kf,{className:"w-5 h-5 mr-2"}),"View Dashboard"]})]})]})}),T.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[T.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md border border-gray-200",children:T.jsxs("div",{className:"flex items-center",children:[T.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:T.jsx(rx,{className:"w-6 h-6 text-blue-600"})}),T.jsxs("div",{className:"ml-4",children:[T.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Issues"}),T.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o.totalIssues})]})]})}),T.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md border border-gray-200",children:T.jsxs("div",{className:"flex items-center",children:[T.jsx("div",{className:"p-2 bg-red-100 rounded-lg",children:T.jsx(Rp,{className:"w-6 h-6 text-red-600"})}),T.jsxs("div",{className:"ml-4",children:[T.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Open Issues"}),T.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o.openIssues})]})]})}),T.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md border border-gray-200",children:T.jsxs("div",{className:"flex items-center",children:[T.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:T.jsx(Ap,{className:"w-6 h-6 text-green-600"})}),T.jsxs("div",{className:"ml-4",children:[T.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Resolved"}),T.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o.resolvedIssues})]})]})}),T.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md border border-gray-200",children:T.jsxs("div",{className:"flex items-center",children:[T.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:T.jsx(Kf,{className:"w-6 h-6 text-yellow-600"})}),T.jsxs("div",{className:"ml-4",children:[T.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Resolution Rate"}),T.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[o.resolutionRate,"%"]})]})]})})]}),T.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[T.jsxs("div",{className:"bg-white rounded-lg shadow-md border border-gray-200",children:[T.jsx("div",{className:"p-6 border-b border-gray-200",children:T.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Recent Issues"})}),T.jsx("div",{className:"p-6",children:c.length>0?T.jsx("div",{className:"space-y-4",children:c.map(y=>T.jsxs("div",{className:"flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg",children:[S(y.status),T.jsxs("div",{className:"flex-1 min-w-0",children:[T.jsx(we,{to:`/issues/${y._id}`,className:"text-sm font-medium text-gray-900 hover:text-blue-600",children:y.title}),T.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[T.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${w(y.category)}`,children:y.category.replace("_"," ")}),T.jsxs("span",{className:"text-xs text-gray-500",children:[y.address.city,", ",y.address.state]})]}),T.jsx("p",{className:"text-xs text-gray-500 mt-1",children:new Date(y.createdAt).toLocaleDateString()})]})]},y._id))}):T.jsx("p",{className:"text-gray-500 text-center py-8",children:"No issues reported yet"})})]}),T.jsxs("div",{className:"bg-white rounded-lg shadow-md border border-gray-200",children:[T.jsx("div",{className:"p-6 border-b border-gray-200",children:T.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Issue Locations"})}),T.jsx("div",{className:"p-6",children:T.jsx("div",{className:"h-64 rounded-lg overflow-hidden",children:T.jsxs(Ox,{center:[28.6139,77.209],zoom:10,style:{height:"100%",width:"100%"},children:[T.jsx(Mx,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",attribution:'© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'}),c.map(y=>T.jsx(Ax,{position:[y.location.coordinates[1],y.location.coordinates[0]],children:T.jsx(Rx,{children:T.jsxs("div",{className:"p-2",children:[T.jsx("h3",{className:"font-medium",children:y.title}),T.jsx("p",{className:"text-sm text-gray-600",children:y.category.replace("_"," ")}),T.jsx("p",{className:"text-xs text-gray-500",children:y.address.city})]})})},y._id))]})})})]})]})]})},Nx=()=>T.jsx("div",{className:"max-w-4xl mx-auto",children:T.jsxs("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-8",children:[T.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Report an Issue"}),T.jsxs("div",{className:"text-center py-12",children:[T.jsx("p",{className:"text-gray-500 text-lg",children:"Issue reporting form coming soon..."}),T.jsx("p",{className:"text-gray-400 text-sm mt-2",children:"This feature will allow you to report urban issues with photos and location data."})]})]})}),zx=()=>T.jsx("div",{className:"max-w-7xl mx-auto",children:T.jsxs("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-8",children:[T.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Analytics Dashboard"}),T.jsxs("div",{className:"text-center py-12",children:[T.jsx("p",{className:"text-gray-500 text-lg",children:"Analytics dashboard coming soon..."}),T.jsx("p",{className:"text-gray-400 text-sm mt-2",children:"This will show comprehensive analytics, charts, and urban health scores."})]})]})}),Dx=()=>{const{id:o}=Mv();return T.jsx("div",{className:"max-w-4xl mx-auto",children:T.jsxs("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-8",children:[T.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Issue Details"}),T.jsxs("div",{className:"text-center py-12",children:[T.jsx("p",{className:"text-gray-500 text-lg",children:"Issue details page coming soon..."}),T.jsxs("p",{className:"text-gray-400 text-sm mt-2",children:["Issue ID: ",o]})]})]})})},Bx=()=>{const[o,u]=M.useState({email:"",password:""}),[c,h]=M.useState(!1),[m,_]=M.useState(!1),[v,S]=M.useState({}),{login:w}=oh(),y=bu(),z=Bi().state?.from?.pathname||"/",B=U=>{const{name:Y,value:X}=U.target;u(it=>({...it,[Y]:X})),v[Y]&&S(it=>({...it,[Y]:""}))},J=()=>{const U={};return o.email?/\S+@\S+\.\S+/.test(o.email)||(U.email="Email is invalid"):U.email="Email is required",o.password||(U.password="Password is required"),S(U),Object.keys(U).length===0},N=async U=>{if(U.preventDefault(),!!J()){_(!0);try{(await w(o.email,o.password)).success&&y(z,{replace:!0})}catch(Y){console.error("Login error:",Y)}finally{_(!1)}}};return T.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:T.jsxs("div",{className:"max-w-md w-full space-y-8",children:[T.jsxs("div",{children:[T.jsx("div",{className:"mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center",children:T.jsx("span",{className:"text-white font-bold text-xl",children:"C"})}),T.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to CityPulse"}),T.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",T.jsx(we,{to:"/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"create a new account"})]})]}),T.jsxs("form",{className:"mt-8 space-y-6",onSubmit:N,children:[T.jsxs("div",{className:"space-y-4",children:[T.jsxs("div",{children:[T.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),T.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",value:o.email,onChange:B,className:`mt-1 appearance-none relative block w-full px-3 py-2 border ${v.email?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`,placeholder:"Enter your email"}),v.email&&T.jsx("p",{className:"mt-1 text-sm text-red-600",children:v.email})]}),T.jsxs("div",{children:[T.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),T.jsxs("div",{className:"mt-1 relative",children:[T.jsx("input",{id:"password",name:"password",type:c?"text":"password",autoComplete:"current-password",value:o.password,onChange:B,className:`appearance-none relative block w-full px-3 py-2 pr-10 border ${v.password?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`,placeholder:"Enter your password"}),T.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!c),children:c?T.jsx(Jf,{className:"h-5 w-5 text-gray-400"}):T.jsx(Ff,{className:"h-5 w-5 text-gray-400"})})]}),v.password&&T.jsx("p",{className:"mt-1 text-sm text-red-600",children:v.password})]})]}),T.jsx("div",{className:"flex items-center justify-between",children:T.jsx("div",{className:"text-sm",children:T.jsx("a",{href:"#",className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})}),T.jsx("div",{children:T.jsx("button",{type:"submit",disabled:m,className:`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white ${m?"bg-blue-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}`,children:m?T.jsxs("div",{className:"flex items-center",children:[T.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"})}),T.jsx("div",{className:"text-center",children:T.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",T.jsx(we,{to:"/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up here"})]})})]})]})})},jx=()=>{const[o,u]=M.useState({username:"",email:"",password:"",confirmPassword:"",firstName:"",lastName:"",location:{city:"",state:"",country:"India"}}),[c,h]=M.useState(!1),[m,_]=M.useState(!1),[v,S]=M.useState(!1),[w,y]=M.useState({}),{register:E}=oh(),z=bu(),B=U=>{const{name:Y,value:X}=U.target;if(Y.startsWith("location.")){const it=Y.split(".")[1];u(et=>({...et,location:{...et.location,[it]:X}}))}else u(it=>({...it,[Y]:X}));w[Y]&&y(it=>({...it,[Y]:""}))},J=()=>{const U={};return o.username?o.username.length<3&&(U.username="Username must be at least 3 characters"):U.username="Username is required",o.email?/\S+@\S+\.\S+/.test(o.email)||(U.email="Email is invalid"):U.email="Email is required",o.password?o.password.length<6&&(U.password="Password must be at least 6 characters"):U.password="Password is required",o.confirmPassword?o.password!==o.confirmPassword&&(U.confirmPassword="Passwords do not match"):U.confirmPassword="Please confirm your password",o.firstName||(U.firstName="First name is required"),o.lastName||(U.lastName="Last name is required"),y(U),Object.keys(U).length===0},N=async U=>{if(U.preventDefault(),!!J()){S(!0);try{const{confirmPassword:Y,...X}=o;(await E(X)).success&&z("/")}catch(Y){console.error("Registration error:",Y)}finally{S(!1)}}};return T.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:T.jsxs("div",{className:"max-w-md w-full space-y-8",children:[T.jsxs("div",{children:[T.jsx("div",{className:"mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center",children:T.jsx("span",{className:"text-white font-bold text-xl",children:"C"})}),T.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),T.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",T.jsx(we,{to:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"sign in to your existing account"})]})]}),T.jsxs("form",{className:"mt-8 space-y-6",onSubmit:N,children:[T.jsxs("div",{className:"space-y-4",children:[T.jsxs("div",{children:[T.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700",children:"Username"}),T.jsx("input",{id:"username",name:"username",type:"text",value:o.username,onChange:B,className:`mt-1 appearance-none relative block w-full px-3 py-2 border ${w.username?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,placeholder:"Choose a username"}),w.username&&T.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.username})]}),T.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[T.jsxs("div",{children:[T.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700",children:"First Name"}),T.jsx("input",{id:"firstName",name:"firstName",type:"text",value:o.firstName,onChange:B,className:`mt-1 appearance-none relative block w-full px-3 py-2 border ${w.firstName?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,placeholder:"First name"}),w.firstName&&T.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.firstName})]}),T.jsxs("div",{children:[T.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700",children:"Last Name"}),T.jsx("input",{id:"lastName",name:"lastName",type:"text",value:o.lastName,onChange:B,className:`mt-1 appearance-none relative block w-full px-3 py-2 border ${w.lastName?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,placeholder:"Last name"}),w.lastName&&T.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.lastName})]})]}),T.jsxs("div",{children:[T.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),T.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",value:o.email,onChange:B,className:`mt-1 appearance-none relative block w-full px-3 py-2 border ${w.email?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,placeholder:"Enter your email"}),w.email&&T.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.email})]}),T.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[T.jsxs("div",{children:[T.jsx("label",{htmlFor:"location.city",className:"block text-sm font-medium text-gray-700",children:"City"}),T.jsx("input",{id:"location.city",name:"location.city",type:"text",value:o.location.city,onChange:B,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Your city"})]}),T.jsxs("div",{children:[T.jsx("label",{htmlFor:"location.state",className:"block text-sm font-medium text-gray-700",children:"State"}),T.jsx("input",{id:"location.state",name:"location.state",type:"text",value:o.location.state,onChange:B,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Your state"})]})]}),T.jsxs("div",{children:[T.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),T.jsxs("div",{className:"mt-1 relative",children:[T.jsx("input",{id:"password",name:"password",type:c?"text":"password",value:o.password,onChange:B,className:`appearance-none relative block w-full px-3 py-2 pr-10 border ${w.password?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,placeholder:"Create a password"}),T.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!c),children:c?T.jsx(Jf,{className:"h-5 w-5 text-gray-400"}):T.jsx(Ff,{className:"h-5 w-5 text-gray-400"})})]}),w.password&&T.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.password})]}),T.jsxs("div",{children:[T.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),T.jsxs("div",{className:"mt-1 relative",children:[T.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:m?"text":"password",value:o.confirmPassword,onChange:B,className:`appearance-none relative block w-full px-3 py-2 pr-10 border ${w.confirmPassword?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,placeholder:"Confirm your password"}),T.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>_(!m),children:m?T.jsx(Jf,{className:"h-5 w-5 text-gray-400"}):T.jsx(Ff,{className:"h-5 w-5 text-gray-400"})})]}),w.confirmPassword&&T.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.confirmPassword})]})]}),T.jsx("div",{children:T.jsx("button",{type:"submit",disabled:v,className:`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white ${v?"bg-blue-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}`,children:v?T.jsxs("div",{className:"flex items-center",children:[T.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create account"})}),T.jsx("div",{className:"text-center",children:T.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",T.jsx(we,{to:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in here"})]})})]})]})})},Ux=()=>T.jsx("div",{className:"max-w-4xl mx-auto",children:T.jsxs("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 p-8",children:[T.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"User Profile"}),T.jsxs("div",{className:"text-center py-12",children:[T.jsx("p",{className:"text-gray-500 text-lg",children:"User profile page coming soon..."}),T.jsx("p",{className:"text-gray-400 text-sm mt-2",children:"This will show user information, statistics, and settings."})]})]})});function Px(){return T.jsx(Xb,{children:T.jsx(py,{children:T.jsxs("div",{className:"min-h-screen bg-gray-50",children:[T.jsx(fx,{}),T.jsx("main",{className:"container mx-auto px-4 py-8",children:T.jsxs(Vv,{children:[T.jsx(Ma,{path:"/",element:T.jsx(Cx,{})}),T.jsx(Ma,{path:"/report",element:T.jsx(Nx,{})}),T.jsx(Ma,{path:"/dashboard",element:T.jsx(zx,{})}),T.jsx(Ma,{path:"/issues/:id",element:T.jsx(Dx,{})}),T.jsx(Ma,{path:"/login",element:T.jsx(Bx,{})}),T.jsx(Ma,{path:"/register",element:T.jsx(jx,{})}),T.jsx(Ma,{path:"/profile",element:T.jsx(Ux,{})})]})}),T.jsx(p0,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"}}})]})})})}$g.createRoot(document.getElementById("root")).render(T.jsx(M.StrictMode,{children:T.jsx(Px,{})}));
