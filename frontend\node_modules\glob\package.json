{"author": "<PERSON> <<EMAIL>> (https://blog.izs.me/)", "publishConfig": {"tag": "legacy-v10"}, "name": "glob", "description": "the most correct and second fastest glob implementation in JavaScript", "version": "10.4.5", "type": "module", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "bin": "./dist/esm/bin.mjs", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-glob.git"}, "files": ["dist"], "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --log-level warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "prepublish": "npm run benchclean", "profclean": "rm -f v8.log profile.txt", "test-regen": "npm run profclean && TEST_REGEN=1 node --no-warnings --loader ts-node/esm test/00-setup.ts", "prebench": "npm run prepare", "bench": "bash benchmark.sh", "preprof": "npm run prepare", "prof": "bash prof.sh", "benchclean": "node benchclean.cjs"}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "devDependencies": {"@types/node": "^20.11.30", "memfs": "^3.4.13", "mkdirp": "^3.0.1", "prettier": "^3.2.5", "rimraf": "^5.0.7", "sync-content": "^1.0.2", "tap": "^19.0.0", "tshy": "^1.14.0", "typedoc": "^0.25.12"}, "tap": {"before": "test/00-setup.ts"}, "license": "ISC", "funding": {"url": "https://github.com/sponsors/isaacs"}, "module": "./dist/esm/index.js"}