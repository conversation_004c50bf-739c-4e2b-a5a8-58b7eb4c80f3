{"version": 3, "file": "winchars.js", "sourceRoot": "", "sources": ["../../src/winchars.ts"], "names": [], "mappings": ";AAAA,mEAAmE;AACnE,kCAAkC;;;AAElC,MAAM,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AAErC,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACzB,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CACjD,CAAA;AAED,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAC3D,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAEpD,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,EAAE,CAClC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAD3C,QAAA,MAAM,UACqC;AACjD,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,EAAE,CAClC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAD3C,QAAA,MAAM,UACqC", "sourcesContent": ["// When writing files on Windows, translate the characters to their\n// 0xf000 higher-encoded versions.\n\nconst raw = ['|', '<', '>', '?', ':']\n\nconst win = raw.map(char =>\n  String.fromCharCode(0xf000 + char.charCodeAt(0)),\n)\n\nconst toWin = new Map(raw.map((char, i) => [char, win[i]]))\nconst toRaw = new Map(win.map((char, i) => [char, raw[i]]))\n\nexport const encode = (s: string) =>\n  raw.reduce((s, c) => s.split(c).join(toWin.get(c)), s)\nexport const decode = (s: string) =>\n  win.reduce((s, c) => s.split(c).join(toRaw.get(c)), s)\n"]}