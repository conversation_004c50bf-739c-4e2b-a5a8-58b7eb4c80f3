import React from 'react';
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Home from './pages/Home';
import SimpleHome from './pages/SimpleHome';
import ReportIssue from './pages/ReportIssue';
import Dashboard from './pages/Dashboard';
import IssueDetails from './pages/IssueDetails';
import Login from './pages/Login';
import SimpleLogin from './pages/SimpleLogin';
import Register from './pages/Register';
import Profile from './pages/Profile';
import Test from './pages/Test';
import MinimalTest from './pages/MinimalTest';
import { AuthProvider } from './contexts/AuthContext';

function App() {
  console.log('App component rendering...');

  return (
    <Router>
      <div className="min-h-screen">
        <main>
          <Routes>
            <Route path="/" element={<MinimalTest />} />
              <Route path="/home" element={<Home />} />
              <Route path="/test" element={<Test />} />
              <Route path="/report" element={<ReportIssue />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/issues/:id" element={<IssueDetails />} />
              <Route path="/login" element={<SimpleLogin />} />
              <Route path="/login-full" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/profile" element={<Profile />} />
            </Routes>
          </main>
        </div>
      </Router>
  );
}

export default App;
