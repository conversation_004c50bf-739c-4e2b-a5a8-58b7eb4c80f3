import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import MinimalTest from './pages/MinimalTest';
import SimpleHome from './pages/SimpleHome';
import { AuthProvider } from './contexts/AuthContext';

function SimpleApp() {
  console.log('SimpleApp component rendering...');

  return (
    <AuthProvider>
      <Router>
      <div className="min-h-screen bg-green-400 p-5">
        <h1 className="text-4xl font-bold text-white mb-4">Simple App Test with Router + Tailwind</h1>
        <p className="text-white text-lg mb-4">If you can see this, React + Router + Tailwind is working!</p>
        <div className="bg-white/20 p-4 rounded-lg mb-4">
          <h2 className="text-2xl font-semibold text-white mb-2">Debug Info:</h2>
          <ul className="text-white space-y-1">
            <li>React: ✅ Working</li>
            <li>React Router: ✅ Working</li>
            <li>Tailwind CSS: ✅ Working</li>
            <li>App Component: ✅ Working</li>
            <li>Basic Rendering: ✅ Working</li>
          </ul>
        </div>

        <Routes>
          <Route path="/" element={
            <div className="mt-5 p-4 bg-blue-400 rounded-lg">
              <h3 className="text-xl font-bold text-white">Home Route Working!</h3>
            </div>
          } />
          <Route path="/minimal" element={<MinimalTest />} />
          <Route path="/home" element={<SimpleHome />} />
        </Routes>

        <div className="mt-4 space-x-4">
          <a href="/minimal" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Test MinimalTest Component
          </a>
          <a href="/home" className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
            Test SimpleHome Component
          </a>
        </div>
      </div>
      </Router>
    </AuthProvider>
  );
}

export default SimpleApp;
