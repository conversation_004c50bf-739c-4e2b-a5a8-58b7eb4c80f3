import React from 'react';
import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="relative mt-20">
      <div className="bg-white/10 backdrop-blur-md border-t border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-xl">🌆</span>
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                  CityPulse
                </span>
              </div>
              <p className="text-white/80 text-lg mb-4 max-w-md">
                Empowering citizens to create better cities through AI-powered issue reporting and community engagement.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-white/60 hover:text-white transition-colors duration-200">
                  <span className="text-2xl">📧</span>
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors duration-200">
                  <span className="text-2xl">🐦</span>
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors duration-200">
                  <span className="text-2xl">📘</span>
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors duration-200">
                  <span className="text-2xl">📸</span>
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-white font-semibold text-lg mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li>
                  <Link to="/" className="text-white/70 hover:text-white transition-colors duration-200">
                    🏠 Home
                  </Link>
                </li>
                <li>
                  <Link to="/report" className="text-white/70 hover:text-white transition-colors duration-200">
                    📝 Report Issue
                  </Link>
                </li>
                <li>
                  <Link to="/dashboard" className="text-white/70 hover:text-white transition-colors duration-200">
                    📊 Dashboard
                  </Link>
                </li>
                <li>
                  <a href="#" className="text-white/70 hover:text-white transition-colors duration-200">
                    ❓ Help & Support
                  </a>
                </li>
              </ul>
            </div>

            {/* Features */}
            <div>
              <h3 className="text-white font-semibold text-lg mb-4">Features</h3>
              <ul className="space-y-2">
                <li className="text-white/70">🤖 AI Classification</li>
                <li className="text-white/70">🗺️ Interactive Maps</li>
                <li className="text-white/70">🗳️ Community Voting</li>
                <li className="text-white/70">📈 Real-time Analytics</li>
                <li className="text-white/70">📱 Mobile Responsive</li>
              </ul>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-white/20 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <div className="text-white/60 text-sm">
              © 2024 CityPulse. Made with ❤️ for better cities.
            </div>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-white/60 hover:text-white text-sm transition-colors duration-200">
                Privacy Policy
              </a>
              <a href="#" className="text-white/60 hover:text-white text-sm transition-colors duration-200">
                Terms of Service
              </a>
              <a href="#" className="text-white/60 hover:text-white text-sm transition-colors duration-200">
                Contact Us
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
