import React, { useState, useEffect } from 'react';
import { 
  HandThumbUpIcon, 
  HandThumbDownIcon,
  ChartBarIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';
import { 
  HandThumbUpIcon as HandThumbUpSolid, 
  HandThumbDownIcon as HandThumbDownSolid 
} from '@heroicons/react/24/solid';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import { useAuth } from '../contexts/AuthContext';

const VotingSystem = ({ issueId, initialVotes = { upvotes: 0, downvotes: 0, score: 0 } }) => {
  const { user, isAuthenticated } = useAuth();
  const [votes, setVotes] = useState(initialVotes);
  const [userVote, setUserVote] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showVoteDetails, setShowVoteDetails] = useState(false);
  const [voteDetails, setVoteDetails] = useState(null);

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchUserVote();
    }
  }, [issueId, isAuthenticated, user]);

  const fetchUserVote = async () => {
    try {
      const response = await axios.get(`http://localhost:5000/api/votes/users/${user._id}/issues/${issueId}`);
      setUserVote(response.data.data.vote);
    } catch (error) {
      console.error('Error fetching user vote:', error);
    }
  };

  const fetchVoteDetails = async () => {
    try {
      const response = await axios.get(`http://localhost:5000/api/votes/issues/${issueId}`);
      setVoteDetails(response.data.data);
    } catch (error) {
      console.error('Error fetching vote details:', error);
      toast.error('Failed to load vote details');
    }
  };

  const handleVote = async (voteType) => {
    if (!isAuthenticated) {
      toast.error('Please login to vote');
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post(`http://localhost:5000/api/votes/issues/${issueId}`, {
        voteType
      });

      const { action, voteType: newVoteType } = response.data.data;

      // Update local state based on action
      if (action === 'removed') {
        setUserVote(null);
        // Update vote counts
        if (userVote?.voteType === 'upvote') {
          setVotes(prev => ({
            ...prev,
            upvotes: Math.max(0, prev.upvotes - 1),
            score: prev.score - 1
          }));
        } else if (userVote?.voteType === 'downvote') {
          setVotes(prev => ({
            ...prev,
            downvotes: Math.max(0, prev.downvotes - 1),
            score: prev.score + 1
          }));
        }
        toast.success('Vote removed');
      } else if (action === 'created') {
        setUserVote({ voteType: newVoteType });
        // Update vote counts
        if (newVoteType === 'upvote') {
          setVotes(prev => ({
            ...prev,
            upvotes: prev.upvotes + 1,
            score: prev.score + 1
          }));
        } else {
          setVotes(prev => ({
            ...prev,
            downvotes: prev.downvotes + 1,
            score: prev.score - 1
          }));
        }
        toast.success('Vote recorded');
      } else if (action === 'updated') {
        const oldVoteType = userVote?.voteType;
        setUserVote({ voteType: newVoteType });
        
        // Update vote counts for vote change
        if (oldVoteType === 'upvote' && newVoteType === 'downvote') {
          setVotes(prev => ({
            ...prev,
            upvotes: Math.max(0, prev.upvotes - 1),
            downvotes: prev.downvotes + 1,
            score: prev.score - 2
          }));
        } else if (oldVoteType === 'downvote' && newVoteType === 'upvote') {
          setVotes(prev => ({
            ...prev,
            upvotes: prev.upvotes + 1,
            downvotes: Math.max(0, prev.downvotes - 1),
            score: prev.score + 2
          }));
        }
        toast.success('Vote updated');
      }
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to vote';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score) => {
    if (score > 0) return 'text-green-600';
    if (score < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getScoreBackground = (score) => {
    if (score > 0) return 'bg-green-50 border-green-200';
    if (score < 0) return 'bg-red-50 border-red-200';
    return 'bg-gray-50 border-gray-200';
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <UserGroupIcon className="w-5 h-5 mr-2" />
          Community Voting
        </h3>
        <button
          onClick={() => {
            setShowVoteDetails(!showVoteDetails);
            if (!showVoteDetails && !voteDetails) {
              fetchVoteDetails();
            }
          }}
          className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
        >
          <ChartBarIcon className="w-4 h-4 mr-1" />
          {showVoteDetails ? 'Hide Details' : 'View Details'}
        </button>
      </div>

      {/* Voting Buttons */}
      <div className="flex items-center space-x-4 mb-4">
        <button
          onClick={() => handleVote('upvote')}
          disabled={loading}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
            userVote?.voteType === 'upvote'
              ? 'bg-green-50 border-green-300 text-green-700'
              : 'bg-white border-gray-300 text-gray-700 hover:bg-green-50 hover:border-green-300'
          } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {userVote?.voteType === 'upvote' ? (
            <HandThumbUpSolid className="w-5 h-5" />
          ) : (
            <HandThumbUpIcon className="w-5 h-5" />
          )}
          <span className="font-medium">{votes.upvotes}</span>
        </button>

        <button
          onClick={() => handleVote('downvote')}
          disabled={loading}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
            userVote?.voteType === 'downvote'
              ? 'bg-red-50 border-red-300 text-red-700'
              : 'bg-white border-gray-300 text-gray-700 hover:bg-red-50 hover:border-red-300'
          } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {userVote?.voteType === 'downvote' ? (
            <HandThumbDownSolid className="w-5 h-5" />
          ) : (
            <HandThumbDownIcon className="w-5 h-5" />
          )}
          <span className="font-medium">{votes.downvotes}</span>
        </button>

        <div className={`px-4 py-2 rounded-lg border ${getScoreBackground(votes.score)}`}>
          <span className={`font-bold ${getScoreColor(votes.score)}`}>
            Score: {votes.score > 0 ? '+' : ''}{votes.score}
          </span>
        </div>
      </div>

      {/* Vote Details */}
      {showVoteDetails && voteDetails && (
        <div className="border-t border-gray-200 pt-4">
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{voteDetails.statistics.upvotes}</div>
              <div className="text-sm text-gray-600">Upvotes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{voteDetails.statistics.downvotes}</div>
              <div className="text-sm text-gray-600">Downvotes</div>
            </div>
          </div>

          {voteDetails.recentVotes && voteDetails.recentVotes.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Recent Votes</h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {voteDetails.recentVotes.slice(0, 5).map((vote) => (
                  <div key={vote._id} className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-xs font-medium">
                        {vote.user?.username?.charAt(0).toUpperCase() || 'U'}
                      </div>
                      <span className="text-gray-700">{vote.user?.username || 'Anonymous'}</span>
                      {vote.user?.credibilityScore && (
                        <span className="text-xs text-gray-500">
                          ({vote.user.credibilityScore} pts)
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-1">
                      {vote.voteType === 'upvote' ? (
                        <HandThumbUpSolid className="w-4 h-4 text-green-500" />
                      ) : (
                        <HandThumbDownSolid className="w-4 h-4 text-red-500" />
                      )}
                      <span className="text-xs text-gray-500">
                        {new Date(vote.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {!isAuthenticated && (
        <div className="text-center text-sm text-gray-500 mt-2">
          <a href="/login" className="text-blue-600 hover:text-blue-800">
            Login to vote and help prioritize issues
          </a>
        </div>
      )}
    </div>
  );
};

export default VotingSystem;
