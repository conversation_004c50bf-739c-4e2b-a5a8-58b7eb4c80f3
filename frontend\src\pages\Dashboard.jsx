import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  MapPinIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';
import axios from 'axios';

const Dashboard = () => {
  const [analytics, setAnalytics] = useState({
    overview: {},
    trends: [],
    geographic: {},
    urbanHealth: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      const [overviewRes, trendsRes, geoRes, healthRes] = await Promise.all([
        axios.get('http://localhost:5000/api/analytics/overview'),
        axios.get('http://localhost:5000/api/analytics/trends?days=30'),
        axios.get('http://localhost:5000/api/analytics/geographic'),
        axios.get('http://localhost:5000/api/analytics/urban-health-score')
      ]);

      setAnalytics({
        overview: overviewRes.data.data.overview,
        trends: trendsRes.data.data,
        geographic: geoRes.data.data,
        urbanHealth: healthRes.data.data.urbanHealthScores
      });
    } catch (error) {
      console.error('Error fetching analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative">
      {/* Animated Background */}
      <div className="fixed inset-0 bg-gradient-primary bg-pattern-dots -z-10">
        <div className="absolute inset-0 bg-purple-900/20"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-500 rounded-2xl shadow-xl mb-4">
            <ChartBarIcon className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-white mb-4 text-shadow-lg">📊 Analytics Dashboard</h1>
          <p className="text-white/80 text-xl">Comprehensive insights into your city's urban health</p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div className="stat-card group">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center mb-2">
                  <MapPinIcon className="w-6 h-6 text-blue-300 mr-2" />
                  <p className="text-sm font-medium text-white/80">Total Reports</p>
                </div>
                <p className="text-3xl font-bold text-white">{analytics.overview.totalIssues || 0}</p>
                <p className="text-xs text-white/60 mt-1">All time</p>
              </div>
              <div className="text-4xl opacity-20 group-hover:opacity-40 transition-opacity duration-300">📈</div>
            </div>
          </div>

          <div className="stat-card group">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center mb-2">
                  <ArrowTrendingUpIcon className="w-6 h-6 text-green-300 mr-2" />
                  <p className="text-sm font-medium text-white/80">Resolution Rate</p>
                </div>
                <p className="text-3xl font-bold text-white">{analytics.overview.resolutionRate || 0}%</p>
                <p className="text-xs text-white/60 mt-1">Success rate</p>
              </div>
              <div className="text-4xl opacity-20 group-hover:opacity-40 transition-opacity duration-300">✅</div>
            </div>
          </div>

          <div className="stat-card group">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center mb-2">
                  <ClockIcon className="w-6 h-6 text-yellow-300 mr-2" />
                  <p className="text-sm font-medium text-white/80">Avg Response</p>
                </div>
                <p className="text-3xl font-bold text-white">2.4</p>
                <p className="text-xs text-white/60 mt-1">Days</p>
              </div>
              <div className="text-4xl opacity-20 group-hover:opacity-40 transition-opacity duration-300">⏱️</div>
            </div>
          </div>

          <div className="stat-card group">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center mb-2">
                  <UserGroupIcon className="w-6 h-6 text-purple-300 mr-2" />
                  <p className="text-sm font-medium text-white/80">Active Users</p>
                </div>
                <p className="text-3xl font-bold text-white">{analytics.overview.activeUsers || 0}</p>
                <p className="text-xs text-white/60 mt-1">This month</p>
              </div>
              <div className="text-4xl opacity-20 group-hover:opacity-40 transition-opacity duration-300">👥</div>
            </div>
          </div>
        </div>

        {/* Charts and Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Issue Categories */}
          <div className="card group">
            <div className="p-6 border-b border-white/10">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                <span className="mr-2">📊</span>
                Issue Categories
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {[
                  { name: 'Potholes', count: 45, color: 'bg-orange-500', percentage: 35 },
                  { name: 'Garbage', count: 32, color: 'bg-gray-500', percentage: 25 },
                  { name: 'Water Leaks', count: 28, color: 'bg-blue-500', percentage: 22 },
                  { name: 'Broken Lights', count: 15, color: 'bg-yellow-500', percentage: 12 },
                  { name: 'Other', count: 8, color: 'bg-purple-500', percentage: 6 }
                ].map((category, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-4 h-4 rounded-full ${category.color}`}></div>
                      <span className="text-gray-700 font-medium">{category.name}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${category.color}`}
                          style={{ width: `${category.percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-gray-600 text-sm font-medium">{category.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Urban Health Score */}
          <div className="card group">
            <div className="p-6 border-b border-white/10">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                <span className="mr-2">🏙️</span>
                Urban Health Score
              </h2>
            </div>
            <div className="p-6">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-24 h-24 bg-green-400 rounded-full mb-4">
                  <span className="text-3xl font-bold text-white">85</span>
                </div>
                <p className="text-gray-600">Overall City Health</p>
                <p className="text-sm text-gray-500 mt-1">Based on issue resolution and community engagement</p>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Infrastructure</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="h-2 bg-green-500 rounded-full" style={{ width: '78%' }}></div>
                    </div>
                    <span className="text-sm text-gray-600">78%</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Cleanliness</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="h-2 bg-blue-500 rounded-full" style={{ width: '92%' }}></div>
                    </div>
                    <span className="text-sm text-gray-600">92%</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-700">Response Time</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div className="h-2 bg-yellow-500 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                    <span className="text-sm text-gray-600">85%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card">
          <div className="p-6 border-b border-white/10">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <span className="mr-2">⚡</span>
              Recent Activity
            </h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {[
                { action: 'Issue resolved', location: 'MG Road, Bangalore', time: '2 hours ago', type: 'success' },
                { action: 'New pothole reported', location: 'Connaught Place, Delhi', time: '4 hours ago', type: 'warning' },
                { action: 'Water leak fixed', location: 'Marine Drive, Mumbai', time: '6 hours ago', type: 'success' },
                { action: 'Street light repair', location: 'Park Street, Kolkata', time: '8 hours ago', type: 'info' }
              ].map((activity, index) => (
                <div key={index} className="flex items-center space-x-4 p-3 hover:bg-gray-50 rounded-lg transition-colors duration-200">
                  <div className={`w-3 h-3 rounded-full ${
                    activity.type === 'success' ? 'bg-green-500' :
                    activity.type === 'warning' ? 'bg-yellow-500' :
                    'bg-blue-500'
                  }`}></div>
                  <div className="flex-1">
                    <p className="text-gray-900 font-medium">{activity.action}</p>
                    <p className="text-gray-600 text-sm">{activity.location}</p>
                  </div>
                  <span className="text-gray-500 text-sm">{activity.time}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
