import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import { 
  PlusCircleIcon, 
  ChartBarIcon, 
  MapPinIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import axios from 'axios';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
import L from 'leaflet';
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const Home = () => {
  const [stats, setStats] = useState({
    totalIssues: 0,
    openIssues: 0,
    resolvedIssues: 0,
    resolutionRate: 0
  });
  const [recentIssues, setRecentIssues] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [statsResponse, issuesResponse] = await Promise.all([
        axios.get('http://localhost:5000/api/analytics/overview'),
        axios.get('http://localhost:5000/api/issues?limit=5&sort=-createdAt')
      ]);

      setStats(statsResponse.data.data.overview);
      setRecentIssues(issuesResponse.data.data.issues);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'resolved':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'in_progress':
        return <ClockIcon className="w-5 h-5 text-yellow-500" />;
      default:
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />;
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      pothole: 'bg-orange-100 text-orange-800',
      garbage: 'bg-gray-100 text-gray-800',
      water_leak: 'bg-blue-100 text-blue-800',
      broken_light: 'bg-yellow-100 text-yellow-800',
      damaged_road: 'bg-red-100 text-red-800',
      other: 'bg-purple-100 text-purple-800'
    };
    return colors[category] || colors.other;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Animated Background */}
      <div className="fixed inset-0 bg-gradient-primary bg-pattern-dots -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
      </div>

      {/* Floating Particles */}
      <div className="particles">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              animationDelay: `${Math.random() * 8}s`,
              animationDuration: `${Math.random() * 10 + 10}s`
            }}
          />
        ))}
      </div>

      <div className="relative z-10 space-y-12 pb-12">
        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div className="text-center">
              <div className="animate-float">
                <h1 className="hero-title mb-6 text-shadow-lg">
                  Welcome to CityPulse
                </h1>
                <p className="hero-subtitle mb-12 max-w-3xl mx-auto text-shadow">
                  🌆 Transform your city with AI-powered issue reporting. Join thousands of citizens making their communities better, one report at a time.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <Link
                  to="/report"
                  className="btn-primary group"
                >
                  <PlusCircleIcon className="w-6 h-6 mr-3 group-hover:rotate-90 transition-transform duration-300" />
                  Report an Issue
                  <span className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">🚀</span>
                </Link>
                <Link
                  to="/dashboard"
                  className="btn-secondary group"
                >
                  <ChartBarIcon className="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-300" />
                  View Dashboard
                  <span className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">📊</span>
                </Link>
              </div>

              {/* Feature Highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16">
                <div className="feature-card group">
                  <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">🤖</div>
                  <h3 className="text-xl font-semibold text-white mb-2">AI-Powered Classification</h3>
                  <p className="text-white/80">Smart image recognition automatically categorizes your reports</p>
                </div>
                <div className="feature-card group">
                  <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">🗺️</div>
                  <h3 className="text-xl font-semibold text-white mb-2">Interactive Maps</h3>
                  <p className="text-white/80">Real-time visualization of issues across your city</p>
                </div>
                <div className="feature-card group">
                  <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">🏆</div>
                  <h3 className="text-xl font-semibold text-white mb-2">Community Driven</h3>
                  <p className="text-white/80">Vote and prioritize issues that matter most to your community</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4 text-shadow">📊 Live City Statistics</h2>
            <p className="text-white/80 text-lg">Real-time data from your community</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="stat-card group hover:scale-105 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center mb-2">
                    <MapPinIcon className="w-6 h-6 text-blue-300 mr-2" />
                    <p className="text-sm font-medium text-white/80">Total Issues</p>
                  </div>
                  <p className="text-3xl font-bold text-white">{stats.totalIssues}</p>
                  <p className="text-xs text-white/60 mt-1">All time reports</p>
                </div>
                <div className="text-4xl opacity-20 group-hover:opacity-40 transition-opacity duration-300">📍</div>
              </div>
            </div>

            <div className="stat-card group hover:scale-105 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center mb-2">
                    <ExclamationTriangleIcon className="w-6 h-6 text-red-300 mr-2" />
                    <p className="text-sm font-medium text-white/80">Open Issues</p>
                  </div>
                  <p className="text-3xl font-bold text-white">{stats.openIssues}</p>
                  <p className="text-xs text-white/60 mt-1">Needs attention</p>
                </div>
                <div className="text-4xl opacity-20 group-hover:opacity-40 transition-opacity duration-300">⚠️</div>
              </div>
            </div>

            <div className="stat-card group hover:scale-105 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center mb-2">
                    <CheckCircleIcon className="w-6 h-6 text-green-300 mr-2" />
                    <p className="text-sm font-medium text-white/80">Resolved</p>
                  </div>
                  <p className="text-3xl font-bold text-white">{stats.resolvedIssues}</p>
                  <p className="text-xs text-white/60 mt-1">Successfully fixed</p>
                </div>
                <div className="text-4xl opacity-20 group-hover:opacity-40 transition-opacity duration-300">✅</div>
              </div>
            </div>

            <div className="stat-card group hover:scale-105 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center mb-2">
                    <ChartBarIcon className="w-6 h-6 text-yellow-300 mr-2" />
                    <p className="text-sm font-medium text-white/80">Success Rate</p>
                  </div>
                  <p className="text-3xl font-bold text-white">{stats.resolutionRate}%</p>
                  <p className="text-xs text-white/60 mt-1">Resolution efficiency</p>
                </div>
                <div className="text-4xl opacity-20 group-hover:opacity-40 transition-opacity duration-300">📈</div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Issues and Map */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Issues */}
            <div className="card group">
              <div className="p-6 border-b border-white/10">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-900">🔥 Recent Issues</h2>
                  <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    Live Updates
                  </span>
                </div>
              </div>
              <div className="p-6 max-h-96 overflow-y-auto">
                {recentIssues.length > 0 ? (
                  <div className="space-y-4">
                    {recentIssues.map((issue, index) => (
                      <div
                        key={issue._id}
                        className="flex items-start space-x-4 p-4 hover:bg-gray-50 rounded-xl transition-all duration-300 border border-gray-100 hover:border-blue-200 hover:shadow-md group/item"
                        style={{ animationDelay: `${index * 100}ms` }}
                      >
                        <div className="flex-shrink-0">
                          {getStatusIcon(issue.status)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <Link
                            to={`/issues/${issue._id}`}
                            className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors duration-200 group-hover/item:text-blue-600"
                          >
                            {issue.title}
                          </Link>
                          <div className="flex items-center space-x-2 mt-2">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(issue.category)}`}>
                              {issue.category.replace('_', ' ')}
                            </span>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                              📍 {issue.address.city}, {issue.address.state}
                            </span>
                          </div>
                          <div className="flex items-center justify-between mt-2">
                            <p className="text-xs text-gray-500">
                              🕒 {new Date(issue.createdAt).toLocaleDateString()}
                            </p>
                            <div className="flex items-center space-x-1 text-xs text-gray-400">
                              <span>👍 {issue.votes?.upvotes || 0}</span>
                              <span>👎 {issue.votes?.downvotes || 0}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🏙️</div>
                    <p className="text-gray-500 text-lg font-medium">No issues reported yet</p>
                    <p className="text-gray-400 text-sm mt-2">Be the first to make your city better!</p>
                    <Link
                      to="/report"
                      className="inline-block mt-4 text-blue-600 hover:text-blue-700 font-medium"
                    >
                      Report the first issue →
                    </Link>
                  </div>
                )}
              </div>
            </div>

            {/* Interactive Map */}
            <div className="card group">
              <div className="p-6 border-b border-white/10">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-900">🗺️ Live Issue Map</h2>
                  <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    {recentIssues.length} Issues
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="h-80 rounded-xl overflow-hidden border-2 border-gray-100 hover:border-blue-200 transition-colors duration-300">
                  <MapContainer
                    center={[28.6139, 77.2090]} // Delhi coordinates
                    zoom={10}
                    style={{ height: '100%', width: '100%' }}
                    className="rounded-xl"
                  >
                    <TileLayer
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    />
                    {recentIssues.map((issue) => (
                      <Marker
                        key={issue._id}
                        position={[issue.location.coordinates[1], issue.location.coordinates[0]]}
                      >
                        <Popup className="custom-popup">
                          <div className="p-3 min-w-48">
                            <h3 className="font-semibold text-gray-900 mb-2">{issue.title}</h3>
                            <div className="space-y-2">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(issue.category)}`}>
                                {issue.category.replace('_', ' ')}
                              </span>
                              <p className="text-sm text-gray-600">📍 {issue.address.city}</p>
                              <p className="text-xs text-gray-500">🕒 {new Date(issue.createdAt).toLocaleDateString()}</p>
                              <div className="flex items-center space-x-2 text-xs">
                                <span className="text-green-600">👍 {issue.votes?.upvotes || 0}</span>
                                <span className="text-red-600">👎 {issue.votes?.downvotes || 0}</span>
                              </div>
                            </div>
                          </div>
                        </Popup>
                      </Marker>
                    ))}
                  </MapContainer>
                </div>
                <div className="mt-4 text-center">
                  <Link
                    to="/dashboard"
                    className="text-blue-600 hover:text-blue-700 font-medium text-sm"
                  >
                    View Full Analytics Dashboard →
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
