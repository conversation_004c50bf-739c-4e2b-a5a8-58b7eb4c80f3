import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import { Icon } from 'leaflet';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import { 
  MapPinIcon, 
  FunnelIcon, 
  MagnifyingGlassIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

// Custom map icons for different issue categories
const createCustomIcon = (category, status) => {
  const colors = {
    pothole: '#ef4444',
    garbage: '#f59e0b',
    water_leak: '#3b82f6',
    broken_light: '#8b5cf6',
    damaged_road: '#6b7280',
    other: '#10b981'
  };

  const statusColors = {
    open: '#ef4444',
    in_progress: '#f59e0b',
    resolved: '#10b981',
    closed: '#6b7280'
  };

  const color = statusColors[status] || colors[category] || '#6b7280';
  
  return new Icon({
    iconUrl: `data:image/svg+xml;base64,${btoa(`
      <svg width="25" height="35" viewBox="0 0 25 35" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.5 0C5.6 0 0 5.6 0 12.5C0 19.4 12.5 35 12.5 35S25 19.4 25 12.5C25 5.6 19.4 0 12.5 0Z" fill="${color}"/>
        <circle cx="12.5" cy="12.5" r="6" fill="white"/>
      </svg>
    `)}`,
    iconSize: [25, 35],
    iconAnchor: [12.5, 35],
    popupAnchor: [0, -35]
  });
};

// Component to handle map updates
const MapUpdater = ({ center, zoom }) => {
  const map = useMap();
  
  useEffect(() => {
    if (center) {
      map.setView(center, zoom);
    }
  }, [center, zoom, map]);
  
  return null;
};

const IssueMap = () => {
  const [issues, setIssues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    category: '',
    status: '',
    search: ''
  });
  const [mapCenter, setMapCenter] = useState([28.6139, 77.2090]); // Default to Delhi
  const [mapZoom, setMapZoom] = useState(12);
  const [selectedIssue, setSelectedIssue] = useState(null);

  const categories = [
    { value: '', label: 'All Categories' },
    { value: 'pothole', label: 'Potholes', icon: '🕳️' },
    { value: 'garbage', label: 'Garbage', icon: '🗑️' },
    { value: 'water_leak', label: 'Water Leak', icon: '💧' },
    { value: 'broken_light', label: 'Broken Light', icon: '💡' },
    { value: 'damaged_road', label: 'Damaged Road', icon: '🛣️' },
    { value: 'other', label: 'Other', icon: '❓' }
  ];

  const statuses = [
    { value: '', label: 'All Statuses' },
    { value: 'open', label: 'Open', color: 'text-red-600' },
    { value: 'in_progress', label: 'In Progress', color: 'text-yellow-600' },
    { value: 'resolved', label: 'Resolved', color: 'text-green-600' },
    { value: 'closed', label: 'Closed', color: 'text-gray-600' }
  ];

  useEffect(() => {
    fetchIssues();
    getUserLocation();
  }, []);

  useEffect(() => {
    fetchIssues();
  }, [filters]);

  const getUserLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setMapCenter([position.coords.latitude, position.coords.longitude]);
          setMapZoom(14);
        },
        (error) => {
          console.log('Location access denied:', error);
        }
      );
    }
  };

  const fetchIssues = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (filters.category) params.append('category', filters.category);
      if (filters.status) params.append('status', filters.status);
      if (filters.search) params.append('search', filters.search);
      
      const response = await axios.get(`http://localhost:5000/api/issues?${params.toString()}`);
      setIssues(response.data.data.issues);
    } catch (error) {
      console.error('Error fetching issues:', error);
      toast.error('Failed to load issues');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'open':
        return <ExclamationTriangleIcon className="w-4 h-4 text-red-500" />;
      case 'in_progress':
        return <ClockIcon className="w-4 h-4 text-yellow-500" />;
      case 'resolved':
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
      default:
        return <MapPinIcon className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">🗺️ Interactive Issue Map</h1>
              <p className="text-gray-600">Real-time visualization of issues across your city</p>
            </div>
            <div className="text-sm text-gray-500">
              {issues.length} issues found
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-wrap gap-4 items-center">
            {/* Search */}
            <div className="relative flex-1 min-w-64">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search issues..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Category Filter */}
            <div className="flex items-center space-x-2">
              <FunnelIcon className="w-5 h-5 text-gray-400" />
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.icon} {category.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {statuses.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="flex-1 relative">
        <div className="h-[calc(100vh-200px)]">
          <MapContainer
            center={mapCenter}
            zoom={mapZoom}
            style={{ height: '100%', width: '100%' }}
            className="z-0"
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            />
            <MapUpdater center={mapCenter} zoom={mapZoom} />
            
            {issues.map((issue) => (
              <Marker
                key={issue._id}
                position={[issue.location.coordinates[1], issue.location.coordinates[0]]}
                icon={createCustomIcon(issue.category, issue.status)}
                eventHandlers={{
                  click: () => setSelectedIssue(issue)
                }}
              >
                <Popup>
                  <div className="p-2 min-w-64">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-gray-900 text-sm">{issue.title}</h3>
                      {getStatusIcon(issue.status)}
                    </div>
                    <p className="text-gray-600 text-xs mb-2 line-clamp-2">{issue.description}</p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span className="capitalize">{issue.category.replace('_', ' ')}</span>
                      <span>{formatDate(issue.createdAt)}</span>
                    </div>
                    {issue.votes && (
                      <div className="mt-2 flex items-center space-x-2 text-xs">
                        <span className="text-green-600">👍 {issue.votes.upvotes}</span>
                        <span className="text-red-600">👎 {issue.votes.downvotes}</span>
                        <span className="text-gray-600">Score: {issue.votes.score}</span>
                      </div>
                    )}
                  </div>
                </Popup>
              </Marker>
            ))}
          </MapContainer>
        </div>

        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-600">Loading issues...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default IssueMap;
