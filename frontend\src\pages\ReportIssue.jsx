import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import {
  PhotoIcon,
  MapPinIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import axios from 'axios';
import toast from 'react-hot-toast';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
import L from 'leaflet';
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const LocationMarker = ({ position, setPosition }) => {
  useMapEvents({
    click(e) {
      setPosition([e.latlng.lat, e.latlng.lng]);
    },
  });

  return position === null ? null : (
    <Marker position={position} />
  );
};

const ReportIssue = () => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    address: {
      street: '',
      city: '',
      state: '',
      pincode: '',
      country: 'India'
    },
    tags: []
  });

  const [location, setLocation] = useState(null);
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [currentStep, setCurrentStep] = useState(1);

  const categories = [
    { value: 'pothole', label: 'Pothole', icon: '🕳️' },
    { value: 'garbage', label: 'Garbage Dump', icon: '🗑️' },
    { value: 'water_leak', label: 'Water Leak', icon: '💧' },
    { value: 'broken_light', label: 'Broken Street Light', icon: '💡' },
    { value: 'damaged_road', label: 'Damaged Road', icon: '🛣️' },
    { value: 'other', label: 'Other', icon: '❓' }
  ];

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: { pathname: '/report' } } });
    }
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    // Get user's current location
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation([position.coords.latitude, position.coords.longitude]);
        },
        (error) => {
          console.error('Error getting location:', error);
          // Default to Delhi coordinates
          setLocation([28.6139, 77.2090]);
        }
      );
    } else {
      setLocation([28.6139, 77.2090]);
    }
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name.startsWith('address.')) {
      const addressField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);

    if (files.length + images.length > 5) {
      toast.error('Maximum 5 images allowed');
      return;
    }

    const validFiles = files.filter(file => {
      const isValidType = file.type.startsWith('image/');
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB

      if (!isValidType) {
        toast.error(`${file.name} is not a valid image file`);
        return false;
      }

      if (!isValidSize) {
        toast.error(`${file.name} is too large (max 10MB)`);
        return false;
      }

      return true;
    });

    setImages(prev => [...prev, ...validFiles]);
  };

  const removeImage = (index) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const validateStep = (step) => {
    const newErrors = {};

    if (step === 1) {
      if (!formData.title.trim()) {
        newErrors.title = 'Title is required';
      }
      if (!formData.description.trim()) {
        newErrors.description = 'Description is required';
      }
      if (!formData.category) {
        newErrors.category = 'Category is required';
      }
    }

    if (step === 2) {
      if (!location) {
        newErrors.location = 'Location is required';
      }
      if (!formData.address.city.trim()) {
        newErrors['address.city'] = 'City is required';
      }
      if (!formData.address.state.trim()) {
        newErrors['address.state'] = 'State is required';
      }
    }

    if (step === 3) {
      if (images.length === 0) {
        newErrors.images = 'At least one image is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateStep(3)) {
      return;
    }

    setLoading(true);

    try {
      const formDataToSend = new FormData();

      // Add form fields
      formDataToSend.append('title', formData.title);
      formDataToSend.append('description', formData.description);
      formDataToSend.append('category', formData.category);
      formDataToSend.append('location', JSON.stringify({
        coordinates: [location[1], location[0]] // [longitude, latitude]
      }));
      formDataToSend.append('address', JSON.stringify(formData.address));

      if (formData.tags.length > 0) {
        formDataToSend.append('tags', JSON.stringify(formData.tags));
      }

      // Add images
      images.forEach((image, index) => {
        formDataToSend.append('images', image);
      });

      const response = await axios.post('http://localhost:5000/api/issues', formDataToSend, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      toast.success('Issue reported successfully!');
      navigate(`/issues/${response.data.data.issue._id}`);

    } catch (error) {
      console.error('Error submitting issue:', error);
      const message = error.response?.data?.message || 'Failed to submit issue';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen relative">
      {/* Animated Background */}
      <div className="fixed inset-0 bg-gradient-primary bg-pattern-grid -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="glass rounded-2xl">
          {/* Header */}
          <div className="px-8 py-6 border-b border-white/10">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-xl mb-4">
                <span className="text-2xl">📝</span>
              </div>
              <h1 className="text-3xl font-bold text-white mb-2">Report an Issue</h1>
              <p className="text-white/80 text-lg">Help improve your city by reporting urban issues</p>
            </div>

            {/* Progress Steps */}
            <div className="flex items-center justify-center mt-8">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                    step <= currentStep
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 border-transparent text-white shadow-lg'
                      : 'border-white/30 text-white/50 bg-white/10'
                  }`}>
                    {step < currentStep ? (
                      <CheckCircleIcon className="w-6 h-6" />
                    ) : (
                      <span className="text-lg font-bold">{step}</span>
                    )}
                  </div>
                  {step < 3 && (
                    <div className={`w-20 h-1 mx-3 rounded-full transition-all duration-300 ${
                      step < currentStep ? 'bg-gradient-to-r from-blue-500 to-purple-600' : 'bg-white/20'
                    }`} />
                  )}
                </div>
              ))}
            </div>

            <div className="flex justify-between mt-4 text-sm text-white/80 max-w-md mx-auto">
              <span className="flex items-center">
                <span className="mr-1">📝</span>
                Details
              </span>
              <span className="flex items-center">
                <span className="mr-1">📍</span>
                Location
              </span>
              <span className="flex items-center">
                <span className="mr-1">📸</span>
                Photos
              </span>
            </div>
        </div>

          <form onSubmit={handleSubmit} className="p-8">
          {/* Step 1: Issue Details */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-white mb-2">
                  📝 Issue Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className={`input-field ${
                    errors.title ? 'border-red-400 ring-red-400' : ''
                  }`}
                  placeholder="Brief description of the issue"
                />
                {errors.title && (
                  <p className="mt-2 text-sm text-red-300 bg-red-500/20 px-3 py-1 rounded-lg">{errors.title}</p>
                )}
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-white mb-2">
                  📄 Detailed Description *
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={4}
                  value={formData.description}
                  onChange={handleInputChange}
                  className={`input-field ${
                    errors.description ? 'border-red-400 ring-red-400' : ''
                  }`}
                  placeholder="Provide more details about the issue..."
                />
                {errors.description && (
                  <p className="mt-2 text-sm text-red-300 bg-red-500/20 px-3 py-1 rounded-lg">{errors.description}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  🏷️ Issue Category *
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {categories.map((category) => (
                    <label
                      key={category.value}
                      className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                        formData.category === category.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <input
                        type="radio"
                        name="category"
                        value={category.value}
                        checked={formData.category === category.value}
                        onChange={handleInputChange}
                        className="sr-only"
                      />
                      <span className="text-2xl mr-3">{category.icon}</span>
                      <span className="text-sm font-medium">{category.label}</span>
                    </label>
                  ))}
                </div>
                {errors.category && (
                  <p className="mt-2 text-sm text-red-300 bg-red-500/20 px-3 py-1 rounded-lg">{errors.category}</p>
                )}
              </div>
            </div>
          )}

          {/* Step 2: Location */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  📍 Location on Map *
                </label>
                <p className="text-sm text-white/60 mb-4">
                  Click on the map to mark the exact location of the issue
                </p>
                <div className="h-64 rounded-lg overflow-hidden border border-gray-300">
                  {location && (
                    <MapContainer
                      center={location}
                      zoom={15}
                      style={{ height: '100%', width: '100%' }}
                    >
                      <TileLayer
                        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                      />
                      <LocationMarker position={location} setPosition={setLocation} />
                    </MapContainer>
                  )}
                </div>
                {errors.location && (
                  <p className="mt-2 text-sm text-red-300 bg-red-500/20 px-3 py-1 rounded-lg">{errors.location}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="address.city" className="block text-sm font-medium text-white mb-2">
                    🏙️ City *
                  </label>
                  <input
                    type="text"
                    id="address.city"
                    name="address.city"
                    value={formData.address.city}
                    onChange={handleInputChange}
                    className={`input-field ${
                      errors['address.city'] ? 'border-red-400 ring-red-400' : ''
                    }`}
                    placeholder="City name"
                  />
                  {errors['address.city'] && (
                    <p className="mt-2 text-sm text-red-300 bg-red-500/20 px-3 py-1 rounded-lg">{errors['address.city']}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="address.state" className="block text-sm font-medium text-white mb-2">
                    🗺️ State *
                  </label>
                  <input
                    type="text"
                    id="address.state"
                    name="address.state"
                    value={formData.address.state}
                    onChange={handleInputChange}
                    className={`input-field ${
                      errors['address.state'] ? 'border-red-400 ring-red-400' : ''
                    }`}
                    placeholder="State name"
                  />
                  {errors['address.state'] && (
                    <p className="mt-2 text-sm text-red-300 bg-red-500/20 px-3 py-1 rounded-lg">{errors['address.state']}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Photos */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  📸 Upload Photos *
                </label>
                <p className="text-sm text-white/60 mb-4">
                  Add photos of the issue (maximum 5 images, 10MB each)
                </p>

                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <label htmlFor="images" className="cursor-pointer">
                      <span className="mt-2 block text-sm font-medium text-white">
                        Click to upload images
                      </span>
                      <span className="mt-1 block text-sm text-white/60">
                        PNG, JPG, GIF up to 10MB each
                      </span>
                    </label>
                    <input
                      id="images"
                      name="images"
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageChange}
                      className="sr-only"
                    />
                  </div>
                </div>

                {errors.images && (
                  <p className="mt-2 text-sm text-red-300 bg-red-500/20 px-3 py-1 rounded-lg">{errors.images}</p>
                )}
              </div>

              {/* Image Preview */}
              {images.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-white mb-3">
                    Selected Images ({images.length}/5)
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {images.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg border border-gray-300"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                        >
                          ×
                        </button>
                        <p className="mt-1 text-xs text-white/60 truncate">
                          {image.name}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6 border-t border-white/10 mt-8">
            <button
              type="button"
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className={`px-4 py-2 text-sm font-medium rounded-md ${
                currentStep === 1
                  ? 'text-white/40 cursor-not-allowed'
                  : 'text-white bg-white/10 hover:bg-white/20 border border-white/20'
              }`}
            >
              Previous
            </button>

            {currentStep < 3 ? (
              <button
                type="button"
                onClick={handleNext}
                className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-sm font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                Next
              </button>
            ) : (
              <button
                type="submit"
                disabled={loading}
                className={`px-6 py-2 text-sm font-medium rounded-xl text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ${
                  loading
                    ? 'bg-blue-400 cursor-not-allowed'
                    : 'bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700'
                }`}
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </div>
                ) : (
                  '🚀 Submit Issue'
                )}
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReportIssue;
