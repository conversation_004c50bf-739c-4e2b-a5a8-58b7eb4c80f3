import React from 'react';
import { Link } from 'react-router-dom';
import {
  PlusCircleIcon,
  ChartBarIcon,
  MapPinIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

const SimpleHome = () => {
  return (
    <div className="min-h-screen">
      {/* Beautiful Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 -z-10">
        <div className="absolute inset-0 bg-black/20"></div>
      </div>

      <div className="relative z-10 px-4 py-12">
        {/* Hero Section */}
        <div className="max-w-6xl mx-auto text-center mb-16">
          <div className="mb-8">
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
              🌆 Welcome to CityPulse
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto">
              Transform your city with AI-powered issue reporting. Join thousands of citizens making their communities better.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <Link
              to="/report"
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-4 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center"
            >
              <PlusCircleIcon className="w-6 h-6 mr-3" />
              Report an Issue
            </Link>
            <Link
              to="/dashboard"
              className="bg-white/10 backdrop-blur-md hover:bg-white/20 text-white font-semibold py-4 px-8 rounded-xl border border-white/20 hover:border-white/40 transform hover:scale-105 transition-all duration-300 flex items-center"
            >
              <ChartBarIcon className="w-6 h-6 mr-3" />
              View Dashboard
            </Link>
          </div>

          {/* Feature Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <Link to="/report" className="group">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 group-hover:scale-105 cursor-pointer">
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">🤖</div>
                <h3 className="text-xl font-semibold text-white mb-2">AI-Powered</h3>
                <p className="text-white/80 mb-4">Smart classification of urban issues using advanced AI</p>
                <div className="text-sm text-white/60 group-hover:text-white/80 transition-colors">
                  ✨ Try AI classification when reporting issues
                </div>
              </div>
            </Link>
            <Link to="/map" className="group">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 group-hover:scale-105 cursor-pointer">
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">🗺️</div>
                <h3 className="text-xl font-semibold text-white mb-2">Interactive Maps</h3>
                <p className="text-white/80 mb-4">Real-time visualization of issues across your city</p>
                <div className="text-sm text-white/60 group-hover:text-white/80 transition-colors">
                  🗺️ Explore the interactive issue map
                </div>
              </div>
            </Link>
            <Link to="/dashboard" className="group">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 group-hover:scale-105 cursor-pointer">
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">🏆</div>
                <h3 className="text-xl font-semibold text-white mb-2">Community Driven</h3>
                <p className="text-white/80 mb-4">Vote and prioritize issues that matter most</p>
                <div className="text-sm text-white/60 group-hover:text-white/80 transition-colors">
                  🏆 View community voting and analytics
                </div>
              </div>
            </Link>
          </div>
        </div>

        {/* Statistics */}
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">📊 Live City Statistics</h2>
            <p className="text-white/80 text-lg">Real-time data from your community</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center mb-2">
                    <MapPinIcon className="w-6 h-6 text-blue-300 mr-2" />
                    <p className="text-sm font-medium text-white/80">Total Issues</p>
                  </div>
                  <p className="text-3xl font-bold text-white">128</p>
                  <p className="text-xs text-white/60 mt-1">All time reports</p>
                </div>
                <div className="text-4xl opacity-20">📍</div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center mb-2">
                    <ExclamationTriangleIcon className="w-6 h-6 text-red-300 mr-2" />
                    <p className="text-sm font-medium text-white/80">Open Issues</p>
                  </div>
                  <p className="text-3xl font-bold text-white">23</p>
                  <p className="text-xs text-white/60 mt-1">Needs attention</p>
                </div>
                <div className="text-4xl opacity-20">⚠️</div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center mb-2">
                    <CheckCircleIcon className="w-6 h-6 text-green-300 mr-2" />
                    <p className="text-sm font-medium text-white/80">Resolved</p>
                  </div>
                  <p className="text-3xl font-bold text-white">105</p>
                  <p className="text-xs text-white/60 mt-1">Successfully fixed</p>
                </div>
                <div className="text-4xl opacity-20">✅</div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center mb-2">
                    <ChartBarIcon className="w-6 h-6 text-yellow-300 mr-2" />
                    <p className="text-sm font-medium text-white/80">Success Rate</p>
                  </div>
                  <p className="text-3xl font-bold text-white">82%</p>
                  <p className="text-xs text-white/60 mt-1">Resolution efficiency</p>
                </div>
                <div className="text-4xl opacity-20">📈</div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="max-w-4xl mx-auto text-center mt-16">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Make a Difference?</h2>
            <p className="text-white/80 text-lg mb-6">
              Join thousands of citizens who are already making their cities better
            </p>
            <Link
              to="/register"
              className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-semibold py-4 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 inline-flex items-center"
            >
              ✨ Get Started Today
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleHome;
