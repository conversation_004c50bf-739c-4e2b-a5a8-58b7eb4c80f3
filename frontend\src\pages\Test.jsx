import React from 'react';

const Test = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-4">🌆 CityPulse Test Page</h1>
        <p className="text-white/80 text-lg mb-8">If you can see this, the basic setup is working!</p>
        <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
          <h2 className="text-xl font-semibold text-white mb-4">✅ Working Components:</h2>
          <ul className="text-white/80 space-y-2">
            <li>✅ React Router</li>
            <li>✅ Tailwind CSS</li>
            <li>✅ Basic Styling</li>
            <li>✅ Component Rendering</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Test;
